var dl=Object.defineProperty,oo=e=>{throw TypeError(e)},ml=(e,t,n)=>t in e?dl(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,X=(e,t,n)=>ml(e,typeof t!="symbol"?t+"":t,n),Va=(e,t,n)=>t.has(e)||oo("Cannot "+n),h=(e,t,n)=>(Va(e,t,"read from private field"),n?n.call(e):t.get(e)),U=(e,t,n)=>t.has(e)?oo("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),F=(e,t,n,r)=>(Va(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),_=(e,t,n)=>(Va(e,t,"access private method"),n),Xa=(e,t,n,r)=>({set _(a){F(e,t,a,n)},get _(){return h(e,t,r)}});class yr extends Error{constructor(t,n,r,a){super(t),X(this,"sourceStart"),X(this,"sourceEnd"),X(this,"parserState"),this.name="ParseError",this.sourceStart=n,this.sourceEnd=r,this.parserState=a}}class un extends yr{constructor(t,n,r,a,s){super(t,n,r,a),X(this,"token"),this.token=s}}const Kt={UnexpectedNewLineInString:"Unexpected newline while consuming a string token.",UnexpectedEOFInString:"Unexpected EOF while consuming a string token.",UnexpectedEOFInComment:"Unexpected EOF while consuming a comment.",UnexpectedEOFInURL:"Unexpected EOF while consuming a url token.",UnexpectedEOFInEscapedCodePoint:"Unexpected EOF while consuming an escaped code point.",UnexpectedCharacterInURL:"Unexpected character while consuming a url token.",InvalidEscapeSequenceInURL:"Invalid escape sequence while consuming a url token.",InvalidEscapeSequenceAfterBackslash:'Invalid escape sequence after "\\"'};function Yt(...e){let t="";for(let n=0;n<e.length;n++)t+=e[n][1];return t}const _n=13,Me=45,In=10,Tn=43,hn=65533;function gl(e){return e.source.codePointAt(e.cursor)===60&&e.source.codePointAt(e.cursor+1)===33&&e.source.codePointAt(e.cursor+2)===Me&&e.source.codePointAt(e.cursor+3)===Me}function ct(e){return e>=48&&e<=57}function vl(e){return e>=65&&e<=90}function bl(e){return e>=97&&e<=122}function Hn(e){return e>=48&&e<=57||e>=97&&e<=102||e>=65&&e<=70}function wl(e){return bl(e)||vl(e)}function Un(e){return wl(e)||$l(e)||e===95}function Ka(e){return Un(e)||ct(e)||e===Me}function $l(e){return e===183||e===8204||e===8205||e===8255||e===8256||e===8204||192<=e&&e<=214||216<=e&&e<=246||248<=e&&e<=893||895<=e&&e<=8191||8304<=e&&e<=8591||11264<=e&&e<=12271||12289<=e&&e<=55295||63744<=e&&e<=64975||65008<=e&&e<=65533||e===0||!!zn(e)||e>=65536}function na(e){return e===In||e===_n||e===12}function xn(e){return e===32||e===In||e===9||e===_n||e===12}function zn(e){return e>=55296&&e<=57343}function Gn(e){return e.source.codePointAt(e.cursor)===92&&!na(e.source.codePointAt(e.cursor+1)??-1)}function ra(e,t){return t.source.codePointAt(t.cursor)===Me?t.source.codePointAt(t.cursor+1)===Me||!!Un(t.source.codePointAt(t.cursor+1)??-1)||t.source.codePointAt(t.cursor+1)===92&&!na(t.source.codePointAt(t.cursor+2)??-1):!!Un(t.source.codePointAt(t.cursor)??-1)||Gn(t)}function io(e){return e.source.codePointAt(e.cursor)===Tn||e.source.codePointAt(e.cursor)===Me?!!ct(e.source.codePointAt(e.cursor+1)??-1)||e.source.codePointAt(e.cursor+1)===46&&ct(e.source.codePointAt(e.cursor+2)??-1):e.source.codePointAt(e.cursor)===46?ct(e.source.codePointAt(e.cursor+1)??-1):ct(e.source.codePointAt(e.cursor)??-1)}function yl(e){return e.source.codePointAt(e.cursor)===47&&e.source.codePointAt(e.cursor+1)===42}function Nl(e){return e.source.codePointAt(e.cursor)===Me&&e.source.codePointAt(e.cursor+1)===Me&&e.source.codePointAt(e.cursor+2)===62}var v,S,aa;function El(e){switch(e){case v.OpenParen:return v.CloseParen;case v.CloseParen:return v.OpenParen;case v.OpenCurly:return v.CloseCurly;case v.CloseCurly:return v.OpenCurly;case v.OpenSquare:return v.CloseSquare;case v.CloseSquare:return v.OpenSquare;default:return null}}function Cl(e){switch(e[0]){case v.OpenParen:return[v.CloseParen,")",-1,-1,void 0];case v.CloseParen:return[v.OpenParen,"(",-1,-1,void 0];case v.OpenCurly:return[v.CloseCurly,"}",-1,-1,void 0];case v.CloseCurly:return[v.OpenCurly,"{",-1,-1,void 0];case v.OpenSquare:return[v.CloseSquare,"]",-1,-1,void 0];case v.CloseSquare:return[v.OpenSquare,"[",-1,-1,void 0];default:return null}}function xl(e,t){for(t.advanceCodePoint(2);;){const n=t.readCodePoint();if(n===void 0){const r=[v.Comment,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0];return e.onParseError(new un(Kt.UnexpectedEOFInComment,t.representationStart,t.representationEnd,["4.3.2. Consume comments","Unexpected EOF"],r)),r}if(n===42&&t.source.codePointAt(t.cursor)!==void 0&&t.source.codePointAt(t.cursor)===47){t.advanceCodePoint();break}}return[v.Comment,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0]}function sa(e,t){const n=t.readCodePoint();if(n===void 0)return e.onParseError(new yr(Kt.UnexpectedEOFInEscapedCodePoint,t.representationStart,t.representationEnd,["4.3.7. Consume an escaped code point","Unexpected EOF"])),hn;if(Hn(n)){const r=[n];let a;for(;(a=t.source.codePointAt(t.cursor))!==void 0&&Hn(a)&&r.length<6;)r.push(a),t.advanceCodePoint();xn(t.source.codePointAt(t.cursor)??-1)&&(t.source.codePointAt(t.cursor)===_n&&t.source.codePointAt(t.cursor+1)===In&&t.advanceCodePoint(),t.advanceCodePoint());const s=parseInt(String.fromCodePoint(...r),16);return s===0||zn(s)||s>1114111?hn:s}return n===0||zn(n)?hn:n}function oa(e,t){const n=[];for(;;){const r=t.source.codePointAt(t.cursor)??-1;if(r===0||zn(r))n.push(hn),t.advanceCodePoint(+(r>65535)+1);else if(Ka(r))n.push(r),t.advanceCodePoint(+(r>65535)+1);else{if(!Gn(t))return n;t.advanceCodePoint(),n.push(sa(e,t))}}}function Fl(e,t){t.advanceCodePoint();const n=t.source.codePointAt(t.cursor);if(n!==void 0&&(Ka(n)||Gn(t))){let r=aa.Unrestricted;ra(0,t)&&(r=aa.ID);const a=oa(e,t);return[v.Hash,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:String.fromCodePoint(...a),type:r}]}return[v.Delim,"#",t.representationStart,t.representationEnd,{value:"#"}]}function kl(e,t){let n=S.Integer;for(t.source.codePointAt(t.cursor)!==Tn&&t.source.codePointAt(t.cursor)!==Me||t.advanceCodePoint();ct(t.source.codePointAt(t.cursor)??-1);)t.advanceCodePoint();if(t.source.codePointAt(t.cursor)===46&&ct(t.source.codePointAt(t.cursor+1)??-1))for(t.advanceCodePoint(2),n=S.Number;ct(t.source.codePointAt(t.cursor)??-1);)t.advanceCodePoint();if(t.source.codePointAt(t.cursor)===101||t.source.codePointAt(t.cursor)===69){if(ct(t.source.codePointAt(t.cursor+1)??-1))t.advanceCodePoint(2);else{if(t.source.codePointAt(t.cursor+1)!==Me&&t.source.codePointAt(t.cursor+1)!==Tn||!ct(t.source.codePointAt(t.cursor+2)??-1))return n;t.advanceCodePoint(3)}for(n=S.Number;ct(t.source.codePointAt(t.cursor)??-1);)t.advanceCodePoint()}return n}function Ya(e,t){let n;{const s=t.source.codePointAt(t.cursor);s===Me?n="-":s===Tn&&(n="+")}const r=kl(0,t),a=parseFloat(t.source.slice(t.representationStart,t.representationEnd+1));if(ra(0,t)){const s=oa(e,t);return[v.Dimension,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:a,signCharacter:n,type:r,unit:String.fromCodePoint(...s)}]}return t.source.codePointAt(t.cursor)===37?(t.advanceCodePoint(),[v.Percentage,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:a,signCharacter:n}]):[v.Number,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:a,signCharacter:n,type:r}]}function Sl(e){for(;xn(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();return[v.Whitespace,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0]}(function(e){e.Comment="comment",e.AtKeyword="at-keyword-token",e.BadString="bad-string-token",e.BadURL="bad-url-token",e.CDC="CDC-token",e.CDO="CDO-token",e.Colon="colon-token",e.Comma="comma-token",e.Delim="delim-token",e.Dimension="dimension-token",e.EOF="EOF-token",e.Function="function-token",e.Hash="hash-token",e.Ident="ident-token",e.Number="number-token",e.Percentage="percentage-token",e.Semicolon="semicolon-token",e.String="string-token",e.URL="url-token",e.Whitespace="whitespace-token",e.OpenParen="(-token",e.CloseParen=")-token",e.OpenSquare="[-token",e.CloseSquare="]-token",e.OpenCurly="{-token",e.CloseCurly="}-token",e.UnicodeRange="unicode-range-token"})(v||(v={})),(function(e){e.Integer="integer",e.Number="number"})(S||(S={})),(function(e){e.Unrestricted="unrestricted",e.ID="id"})(aa||(aa={}));class Ml{constructor(t){X(this,"cursor",0),X(this,"source",""),X(this,"representationStart",0),X(this,"representationEnd",-1),this.source=t}advanceCodePoint(t=1){this.cursor=this.cursor+t,this.representationEnd=this.cursor-1}readCodePoint(){const t=this.source.codePointAt(this.cursor);if(t!==void 0)return this.cursor=this.cursor+1,this.representationEnd=this.cursor-1,t}unreadCodePoint(t=1){this.cursor=this.cursor-t,this.representationEnd=this.cursor-1}resetRepresentation(){this.representationStart=this.cursor,this.representationEnd=-1}}function Al(e,t){let n="";const r=t.readCodePoint();for(;;){const a=t.readCodePoint();if(a===void 0){const s=[v.String,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:n}];return e.onParseError(new un(Kt.UnexpectedEOFInString,t.representationStart,t.representationEnd,["4.3.5. Consume a string token","Unexpected EOF"],s)),s}if(na(a)){t.unreadCodePoint();const s=[v.BadString,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0];return e.onParseError(new un(Kt.UnexpectedNewLineInString,t.representationStart,t.source.codePointAt(t.cursor)===_n&&t.source.codePointAt(t.cursor+1)===In?t.representationEnd+2:t.representationEnd+1,["4.3.5. Consume a string token","Unexpected newline"],s)),s}if(a===r)return[v.String,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:n}];if(a!==92)a===0||zn(a)?n+=String.fromCodePoint(hn):n+=String.fromCodePoint(a);else{if(t.source.codePointAt(t.cursor)===void 0)continue;if(na(t.source.codePointAt(t.cursor)??-1)){t.source.codePointAt(t.cursor)===_n&&t.source.codePointAt(t.cursor+1)===In&&t.advanceCodePoint(),t.advanceCodePoint();continue}n+=String.fromCodePoint(sa(e,t))}}}function Pl(e){return!(e.length!==3||e[0]!==117&&e[0]!==85||e[1]!==114&&e[1]!==82||e[2]!==108&&e[2]!==76)}function Za(e,t){for(;;){const n=t.source.codePointAt(t.cursor);if(n===void 0)return;if(n===41)return void t.advanceCodePoint();Gn(t)?(t.advanceCodePoint(),sa(e,t)):t.advanceCodePoint()}}function Dl(e,t){for(;xn(t.source.codePointAt(t.cursor)??-1);)t.advanceCodePoint();let n="";for(;;){if(t.source.codePointAt(t.cursor)===void 0){const s=[v.URL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:n}];return e.onParseError(new un(Kt.UnexpectedEOFInURL,t.representationStart,t.representationEnd,["4.3.6. Consume a url token","Unexpected EOF"],s)),s}if(t.source.codePointAt(t.cursor)===41)return t.advanceCodePoint(),[v.URL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:n}];if(xn(t.source.codePointAt(t.cursor)??-1)){for(t.advanceCodePoint();xn(t.source.codePointAt(t.cursor)??-1);)t.advanceCodePoint();if(t.source.codePointAt(t.cursor)===void 0){const s=[v.URL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:n}];return e.onParseError(new un(Kt.UnexpectedEOFInURL,t.representationStart,t.representationEnd,["4.3.6. Consume a url token","Consume as much whitespace as possible","Unexpected EOF"],s)),s}return t.source.codePointAt(t.cursor)===41?(t.advanceCodePoint(),[v.URL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:n}]):(Za(e,t),[v.BadURL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0])}const a=t.source.codePointAt(t.cursor);if(a===34||a===39||a===40||(r=a??-1)===11||r===127||0<=r&&r<=8||14<=r&&r<=31){Za(e,t);const s=[v.BadURL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0];return e.onParseError(new un(Kt.UnexpectedCharacterInURL,t.representationStart,t.representationEnd,["4.3.6. Consume a url token",`Unexpected U+0022 QUOTATION MARK ("), U+0027 APOSTROPHE ('), U+0028 LEFT PARENTHESIS (() or non-printable code point`],s)),s}if(a===92){if(Gn(t)){t.advanceCodePoint(),n+=String.fromCodePoint(sa(e,t));continue}Za(e,t);const s=[v.BadURL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0];return e.onParseError(new un(Kt.InvalidEscapeSequenceInURL,t.representationStart,t.representationEnd,["4.3.6. Consume a url token","U+005C REVERSE SOLIDUS (\\)","The input stream does not start with a valid escape sequence"],s)),s}t.source.codePointAt(t.cursor)===0||zn(t.source.codePointAt(t.cursor)??-1)?(n+=String.fromCodePoint(hn),t.advanceCodePoint()):(n+=t.source[t.cursor],t.advanceCodePoint())}var r}function Ja(e,t){const n=oa(e,t);if(t.source.codePointAt(t.cursor)!==40)return[v.Ident,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:String.fromCodePoint(...n)}];if(Pl(n)){t.advanceCodePoint();let r=0;for(;;){const a=xn(t.source.codePointAt(t.cursor)??-1),s=xn(t.source.codePointAt(t.cursor+1)??-1);if(a&&s){r+=1,t.advanceCodePoint(1);continue}const o=a?t.source.codePointAt(t.cursor+1):t.source.codePointAt(t.cursor);if(o===34||o===39)return r>0&&t.unreadCodePoint(r),[v.Function,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:String.fromCodePoint(...n)}];break}return Dl(e,t)}return t.advanceCodePoint(),[v.Function,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:String.fromCodePoint(...n)}]}function Bl(e){return!(e.source.codePointAt(e.cursor)!==117&&e.source.codePointAt(e.cursor)!==85||e.source.codePointAt(e.cursor+1)!==Tn||e.source.codePointAt(e.cursor+2)!==63&&!Hn(e.source.codePointAt(e.cursor+2)??-1))}function Rl(e,t){t.advanceCodePoint(2);const n=[],r=[];let a;for(;(a=t.source.codePointAt(t.cursor))!==void 0&&n.length<6&&Hn(a);)n.push(a),t.advanceCodePoint();for(;(a=t.source.codePointAt(t.cursor))!==void 0&&n.length<6&&a===63;)r.length===0&&r.push(...n),n.push(48),r.push(70),t.advanceCodePoint();if(!r.length&&t.source.codePointAt(t.cursor)===Me&&Hn(t.source.codePointAt(t.cursor+1)??-1))for(t.advanceCodePoint();(a=t.source.codePointAt(t.cursor))!==void 0&&r.length<6&&Hn(a);)r.push(a),t.advanceCodePoint();if(!r.length){const i=parseInt(String.fromCodePoint(...n),16);return[v.UnicodeRange,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{startOfRange:i,endOfRange:i}]}const s=parseInt(String.fromCodePoint(...n),16),o=parseInt(String.fromCodePoint(...r),16);return[v.UnicodeRange,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{startOfRange:s,endOfRange:o}]}function Zt(e,t){const n=lo(e),r=[];for(;!n.endOfFile();)r.push(n.nextToken());return r.push(n.nextToken()),r}function lo(e,t){const n=e.css.valueOf(),r=e.unicodeRangesAllowed??!1,a=new Ml(n),s={onParseError:Ol};return{nextToken:function(){a.resetRepresentation();const o=a.source.codePointAt(a.cursor);if(o===void 0)return[v.EOF,"",-1,-1,void 0];if(o===47&&yl(a))return xl(s,a);if(r&&(o===117||o===85)&&Bl(a))return Rl(0,a);if(Un(o))return Ja(s,a);if(ct(o))return Ya(s,a);switch(o){case 44:return a.advanceCodePoint(),[v.Comma,",",a.representationStart,a.representationEnd,void 0];case 58:return a.advanceCodePoint(),[v.Colon,":",a.representationStart,a.representationEnd,void 0];case 59:return a.advanceCodePoint(),[v.Semicolon,";",a.representationStart,a.representationEnd,void 0];case 40:return a.advanceCodePoint(),[v.OpenParen,"(",a.representationStart,a.representationEnd,void 0];case 41:return a.advanceCodePoint(),[v.CloseParen,")",a.representationStart,a.representationEnd,void 0];case 91:return a.advanceCodePoint(),[v.OpenSquare,"[",a.representationStart,a.representationEnd,void 0];case 93:return a.advanceCodePoint(),[v.CloseSquare,"]",a.representationStart,a.representationEnd,void 0];case 123:return a.advanceCodePoint(),[v.OpenCurly,"{",a.representationStart,a.representationEnd,void 0];case 125:return a.advanceCodePoint(),[v.CloseCurly,"}",a.representationStart,a.representationEnd,void 0];case 39:case 34:return Al(s,a);case 35:return Fl(s,a);case Tn:case 46:return io(a)?Ya(s,a):(a.advanceCodePoint(),[v.Delim,a.source[a.representationStart],a.representationStart,a.representationEnd,{value:a.source[a.representationStart]}]);case In:case _n:case 12:case 9:case 32:return Sl(a);case Me:return io(a)?Ya(s,a):Nl(a)?(a.advanceCodePoint(3),[v.CDC,"-->",a.representationStart,a.representationEnd,void 0]):ra(0,a)?Ja(s,a):(a.advanceCodePoint(),[v.Delim,"-",a.representationStart,a.representationEnd,{value:"-"}]);case 60:return gl(a)?(a.advanceCodePoint(4),[v.CDO,"<!--",a.representationStart,a.representationEnd,void 0]):(a.advanceCodePoint(),[v.Delim,"<",a.representationStart,a.representationEnd,{value:"<"}]);case 64:if(a.advanceCodePoint(),ra(0,a)){const i=oa(s,a);return[v.AtKeyword,a.source.slice(a.representationStart,a.representationEnd+1),a.representationStart,a.representationEnd,{value:String.fromCodePoint(...i)}]}return[v.Delim,"@",a.representationStart,a.representationEnd,{value:"@"}];case 92:{if(Gn(a))return Ja(s,a);a.advanceCodePoint();const i=[v.Delim,"\\",a.representationStart,a.representationEnd,{value:"\\"}];return s.onParseError(new un(Kt.InvalidEscapeSequenceAfterBackslash,a.representationStart,a.representationEnd,["4.3.1. Consume a token","U+005C REVERSE SOLIDUS (\\)","The input stream does not start with a valid escape sequence"],i)),i}}return a.advanceCodePoint(),[v.Delim,a.source[a.representationStart],a.representationStart,a.representationEnd,{value:a.source[a.representationStart]}]},endOfFile:function(){return a.source.codePointAt(a.cursor)===void 0}}}function Ol(){}function co(e,t){const n=[];for(const i of t)n.push(i.codePointAt(0));const r=Wl(n);r[0]===101&&Qa(r,0,r[0]);const a=String.fromCodePoint(...r),s=e[4].signCharacter==="+"?e[4].signCharacter:"",o=e[4].value.toString();e[1]=`${s}${o}${a}`,e[4].unit=t}function Wl(e){let t=0;if(e[0]===0)e.splice(0,1,hn),t=1;else if(e[0]===Me&&e[1]===Me)t=2;else if(e[0]===Me&&e[1])t=2,Un(e[1])||(t+=Qa(e,1,e[1]));else{if(e[0]===Me&&!e[1])return[92,e[0]];Un(e[0])?t=1:(t=1,t+=Qa(e,0,e[0]))}for(let n=t;n<e.length;n++)e[n]!==0?Ka(e[n])||(n+=Ll(e,n,e[n])):(e.splice(n,1,hn),n++);return e}function Ll(e,t,n){return e.splice(t,1,92,n),1}function Qa(e,t,n){const r=n.toString(16),a=[];for(const s of r)a.push(s.codePointAt(0));return e.splice(t,1,92,...a,32),1+a.length}const _l=Object.values(v);function es(e){return!!Array.isArray(e)&&!(e.length<4)&&!!_l.includes(e[0])&&typeof e[1]=="string"&&typeof e[2]=="number"&&typeof e[3]=="number"}function fe(e){if(!e)return!1;switch(e[0]){case v.Dimension:case v.Number:case v.Percentage:return!0;default:return!1}}function uo(e){if(!e)return!1;switch(e[0]){case v.Whitespace:case v.Comment:return!0;default:return!1}}function Nt(e){return!!e&&e[0]===v.Comma}function ho(e){return!!e&&e[0]===v.Comment}function Nr(e){return!!e&&e[0]===v.Delim}function se(e){return!!e&&e[0]===v.Dimension}function Dt(e){return!!e&&e[0]===v.EOF}function Il(e){return!!e&&e[0]===v.Function}function Tl(e){return!!e&&e[0]===v.Hash}function ue(e){return!!e&&e[0]===v.Ident}function H(e){return!!e&&e[0]===v.Number}function ne(e){return!!e&&e[0]===v.Percentage}function ts(e){return!!e&&e[0]===v.Whitespace}function fo(e){return!!e&&e[0]===v.OpenParen}function Hl(e){return!!e&&e[0]===v.CloseParen}function Ul(e){return!!e&&e[0]===v.OpenSquare}function zl(e){return!!e&&e[0]===v.OpenCurly}var ut;function po(e){let t=e.slice();return(n,r,a)=>{let s=-1;for(let o=t.indexOf(r);o<t.length&&(s=n.indexOf(t[o]),s===-1||s<a);o++);return s===-1||s===a&&r===n[a]&&(s++,s>=n.length)?-1:(t=n.slice(),s)}}function ia(e,t){const n=t[0];if(fo(n)||zl(n)||Ul(n)){const r=jl(e,t);return{advance:r.advance,node:r.node}}if(Il(n)){const r=Gl(e,t);return{advance:r.advance,node:r.node}}if(ts(n)){const r=go(e,t);return{advance:r.advance,node:r.node}}if(ho(n)){const r=ql(e,t);return{advance:r.advance,node:r.node}}return{advance:1,node:new G(n)}}(function(e){e.Function="function",e.SimpleBlock="simple-block",e.Whitespace="whitespace",e.Comment="comment",e.Token="token"})(ut||(ut={}));class mo{constructor(){X(this,"value",[])}indexOf(t){return this.value.indexOf(t)}at(t){if(typeof t=="number")return t<0&&(t=this.value.length+t),this.value[t]}forEach(t,n){if(this.value.length===0)return;const r=po(this.value);let a=0;for(;a<this.value.length;){const s=this.value[a];let o;if(n&&(o={...n}),t({node:s,parent:this,state:o},a)===!1)return!1;if(a=r(this.value,s,a),a===-1)break}}walk(t,n){this.value.length!==0&&this.forEach(((r,a)=>t(r,a)!==!1&&(!("walk"in r.node)||!this.value.includes(r.node)||r.node.walk(t,r.state)!==!1)&&void 0),n)}}class qe extends mo{constructor(t,n,r){super(),X(this,"type",ut.Function),X(this,"name"),X(this,"endToken"),this.name=t,this.endToken=n,this.value=r}getName(){return this.name[4].value}normalize(){Dt(this.endToken)&&(this.endToken=[v.CloseParen,")",-1,-1,void 0])}tokens(){return Dt(this.endToken)?[this.name,...this.value.flatMap((t=>t.tokens()))]:[this.name,...this.value.flatMap((t=>t.tokens())),this.endToken]}toString(){const t=this.value.map((n=>es(n)?Yt(n):n.toString())).join("");return Yt(this.name)+t+Yt(this.endToken)}toJSON(){return{type:this.type,name:this.getName(),tokens:this.tokens(),value:this.value.map((t=>t.toJSON()))}}isFunctionNode(){return qe.isFunctionNode(this)}static isFunctionNode(t){return!!t&&t instanceof qe&&t.type===ut.Function}}function Gl(e,t){const n=[];let r=1;for(;;){const a=t[r];if(!a||Dt(a))return e.onParseError(new yr("Unexpected EOF while consuming a function.",t[0][2],t[t.length-1][3],["5.4.9. Consume a function","Unexpected EOF"])),{advance:t.length,node:new qe(t[0],a,n)};if(Hl(a))return{advance:r+1,node:new qe(t[0],a,n)};if(uo(a)){const o=vo(e,t.slice(r));r+=o.advance,n.push(...o.nodes);continue}const s=ia(e,t.slice(r));r+=s.advance,n.push(s.node)}}class wr extends mo{constructor(t,n,r){super(),X(this,"type",ut.SimpleBlock),X(this,"startToken"),X(this,"endToken"),this.startToken=t,this.endToken=n,this.value=r}normalize(){if(Dt(this.endToken)){const t=Cl(this.startToken);t&&(this.endToken=t)}}tokens(){return Dt(this.endToken)?[this.startToken,...this.value.flatMap((t=>t.tokens()))]:[this.startToken,...this.value.flatMap((t=>t.tokens())),this.endToken]}toString(){const t=this.value.map((n=>es(n)?Yt(n):n.toString())).join("");return Yt(this.startToken)+t+Yt(this.endToken)}toJSON(){return{type:this.type,startToken:this.startToken,tokens:this.tokens(),value:this.value.map((t=>t.toJSON()))}}isSimpleBlockNode(){return wr.isSimpleBlockNode(this)}static isSimpleBlockNode(t){return!!t&&t instanceof wr&&t.type===ut.SimpleBlock}}function jl(e,t){const n=El(t[0][0]);if(!n)throw new Error("Failed to parse, a mirror variant must exist for all block open tokens.");const r=[];let a=1;for(;;){const s=t[a];if(!s||Dt(s))return e.onParseError(new yr("Unexpected EOF while consuming a simple block.",t[0][2],t[t.length-1][3],["5.4.8. Consume a simple block","Unexpected EOF"])),{advance:t.length,node:new wr(t[0],s,r)};if(s[0]===n)return{advance:a+1,node:new wr(t[0],s,r)};if(uo(s)){const i=vo(e,t.slice(a));a+=i.advance,r.push(...i.nodes);continue}const o=ia(e,t.slice(a));a+=o.advance,r.push(o.node)}}class lt{constructor(t){X(this,"type",ut.Whitespace),X(this,"value"),this.value=t}tokens(){return this.value}toString(){return Yt(...this.value)}toJSON(){return{type:this.type,tokens:this.tokens()}}isWhitespaceNode(){return lt.isWhitespaceNode(this)}static isWhitespaceNode(t){return!!t&&t instanceof lt&&t.type===ut.Whitespace}}function go(e,t){let n=0;for(;;){const r=t[n];if(!ts(r))return{advance:n,node:new lt(t.slice(0,n))};n++}}class $r{constructor(t){X(this,"type",ut.Comment),X(this,"value"),this.value=t}tokens(){return[this.value]}toString(){return Yt(this.value)}toJSON(){return{type:this.type,tokens:this.tokens()}}isCommentNode(){return $r.isCommentNode(this)}static isCommentNode(t){return!!t&&t instanceof $r&&t.type===ut.Comment}}function ql(e,t){return{advance:1,node:new $r(t[0])}}function vo(e,t){const n=[];let r=0;for(;;)if(ts(t[r])){const a=go(0,t.slice(r));r+=a.advance,n.push(a.node)}else{if(!ho(t[r]))return{advance:r,nodes:n};n.push(new $r(t[r])),r++}}class G{constructor(t){X(this,"type",ut.Token),X(this,"value"),this.value=t}tokens(){return[this.value]}toString(){return this.value[1]}toJSON(){return{type:this.type,tokens:this.tokens()}}isTokenNode(){return G.isTokenNode(this)}static isTokenNode(t){return!!t&&t instanceof G&&t.type===ut.Token}}function Vl(e,t){const n={onParseError:(()=>{})},r=[...e];Dt(r[r.length-1])&&r.push([v.EOF,"",r[r.length-1][2],r[r.length-1][3],void 0]);const a=ia(n,r);if(Dt(r[Math.min(a.advance,r.length-1)]))return a.node;n.onParseError(new yr("Expected EOF after parsing a component value.",e[0][2],e[e.length-1][3],["5.3.9. Parse a component value","Expected EOF"]))}function Xl(e,t){const n={onParseError:t?.onParseError??(()=>{})},r=[...e];if(e.length===0)return[];Dt(r[r.length-1])&&r.push([v.EOF,"",r[r.length-1][2],r[r.length-1][3],void 0]);const a=[];let s=[],o=0;for(;;){if(!r[o]||Dt(r[o]))return s.length&&a.push(s),a;if(Nt(r[o])){a.push(s),s=[],o++;continue}const i=ia(n,e.slice(o));s.push(i.node),o+=i.advance}}function Kl(e,t,n){if(e.length===0)return;const r=po(e);let a=0;for(;a<e.length;){const s=e[a];if(t({node:s,parent:{value:e},state:void 0},a)===!1)return!1;if(a=r(e,s,a),a===-1)break}}function Yl(e,t,n){e.length!==0&&Kl(e,((r,a)=>t(r,a)!==!1&&(!("walk"in r.node)||!e.includes(r.node)||r.node.walk(t,r.state)!==!1)&&void 0))}function bo(e,t){for(let n=0;n<e.length;n++)Yl(e[n],((r,a)=>{if(typeof a!="number")return;const s=t(r.node);s&&(Array.isArray(s)?r.parent.value.splice(a,1,...s):r.parent.value.splice(a,1,s))}));return e}function Zl(e){return wr.isSimpleBlockNode(e)}function Ve(e){return qe.isFunctionNode(e)}function rt(e){return lt.isWhitespaceNode(e)}function at(e){return $r.isCommentNode(e)}function Jt(e){return rt(e)||at(e)}function I(e){return G.isTokenNode(e)}const Jl=/[A-Z]/g;function Xe(e){return e.replace(Jl,(t=>String.fromCharCode(t.charCodeAt(0)+32)))}const Ql={cm:"px",in:"px",mm:"px",pc:"px",pt:"px",px:"px",q:"px",deg:"deg",grad:"deg",rad:"deg",turn:"deg",ms:"s",s:"s",hz:"hz",khz:"hz"},ec=new Map([["cm",e=>e],["mm",e=>10*e],["q",e=>40*e],["in",e=>e/2.54],["pc",e=>e/2.54*6],["pt",e=>e/2.54*72],["px",e=>e/2.54*96]]),la=new Map([["deg",e=>e],["grad",e=>e/.9],["rad",e=>e/180*Math.PI],["turn",e=>e/360]]),Er=new Map([["deg",e=>.9*e],["grad",e=>e],["rad",e=>.9*e/180*Math.PI],["turn",e=>.9*e/360]]),tc=new Map([["hz",e=>e],["khz",e=>e/1e3]]),nc=new Map([["cm",e=>2.54*e],["mm",e=>25.4*e],["q",e=>25.4*e*4],["in",e=>e],["pc",e=>6*e],["pt",e=>72*e],["px",e=>96*e]]),rc=new Map([["hz",e=>1e3*e],["khz",e=>e]]),ac=new Map([["cm",e=>e/10],["mm",e=>e],["q",e=>4*e],["in",e=>e/25.4],["pc",e=>e/25.4*6],["pt",e=>e/25.4*72],["px",e=>e/25.4*96]]),sc=new Map([["ms",e=>e],["s",e=>e/1e3]]),oc=new Map([["cm",e=>e/6*2.54],["mm",e=>e/6*25.4],["q",e=>e/6*25.4*4],["in",e=>e/6],["pc",e=>e],["pt",e=>e/6*72],["px",e=>e/6*96]]),ic=new Map([["cm",e=>e/72*2.54],["mm",e=>e/72*25.4],["q",e=>e/72*25.4*4],["in",e=>e/72],["pc",e=>e/72*6],["pt",e=>e],["px",e=>e/72*96]]),lc=new Map([["cm",e=>e/96*2.54],["mm",e=>e/96*25.4],["q",e=>e/96*25.4*4],["in",e=>e/96],["pc",e=>e/96*6],["pt",e=>e/96*72],["px",e=>e]]),cc=new Map([["cm",e=>e/4/10],["mm",e=>e/4],["q",e=>e],["in",e=>e/4/25.4],["pc",e=>e/4/25.4*6],["pt",e=>e/4/25.4*72],["px",e=>e/4/25.4*96]]),wo=new Map([["deg",e=>180*e/Math.PI],["grad",e=>180*e/Math.PI/.9],["rad",e=>e],["turn",e=>180*e/Math.PI/360]]),uc=new Map([["ms",e=>1e3*e],["s",e=>e]]),Cr=new Map([["deg",e=>360*e],["grad",e=>360*e/.9],["rad",e=>360*e/180*Math.PI],["turn",e=>e]]),$o=new Map([["cm",ec],["mm",ac],["q",cc],["in",nc],["pc",oc],["pt",ic],["px",lc],["ms",sc],["s",uc],["deg",la],["grad",Er],["rad",wo],["turn",Cr],["hz",tc],["khz",rc]]);function st(e,t){if(!se(e)||!se(t))return t;const n=Xe(e[4].unit),r=Xe(t[4].unit);if(n===r)return t;const a=$o.get(r);if(!a)return t;const s=a.get(n);if(!s)return t;const o=s(t[4].value),i=[v.Dimension,"",t[2],t[3],{...t[4],signCharacter:o<0?"-":void 0,type:Number.isInteger(o)?S.Integer:S.Number,value:o}];return co(i,e[4].unit),i}function hc(e){if(!se(e))return e;const t=Xe(e[4].unit),n=Ql[t];if(t===n)return e;const r=$o.get(t);if(!r)return e;const a=r.get(n);if(!a)return e;const s=a(e[4].value),o=[v.Dimension,"",e[2],e[3],{...e[4],signCharacter:s<0?"-":void 0,type:Number.isInteger(s)?S.Integer:S.Number,value:s}];return co(o,n),o}function fc(e){if(e.length!==2)return-1;const t=e[0].value;let n=e[1].value;if(H(t)&&H(n)){const r=t[4].value+n[4].value;return new G([v.Number,r.toString(),t[2],n[3],{value:r,type:t[4].type===S.Integer&&n[4].type===S.Integer?S.Integer:S.Number}])}if(ne(t)&&ne(n)){const r=t[4].value+n[4].value;return new G([v.Percentage,r.toString()+"%",t[2],n[3],{value:r}])}if(se(t)&&se(n)&&(n=st(t,n),Xe(t[4].unit)===Xe(n[4].unit))){const r=t[4].value+n[4].value;return new G([v.Dimension,r.toString()+t[4].unit,t[2],n[3],{value:r,type:t[4].type===S.Integer&&n[4].type===S.Integer?S.Integer:S.Number,unit:t[4].unit}])}return-1}function pc(e){if(e.length!==2)return-1;const t=e[0].value,n=e[1].value;if(H(t)&&H(n)){const r=t[4].value/n[4].value;return new G([v.Number,r.toString(),t[2],n[3],{value:r,type:Number.isInteger(r)?S.Integer:S.Number}])}if(ne(t)&&H(n)){const r=t[4].value/n[4].value;return new G([v.Percentage,r.toString()+"%",t[2],n[3],{value:r}])}if(se(t)&&H(n)){const r=t[4].value/n[4].value;return new G([v.Dimension,r.toString()+t[4].unit,t[2],n[3],{value:r,type:Number.isInteger(r)?S.Integer:S.Number,unit:t[4].unit}])}return-1}function Fn(e){return!!e&&typeof e=="object"&&"inputs"in e&&Array.isArray(e.inputs)&&"operation"in e}function Ke(e){if(e===-1)return-1;const t=[];for(let n=0;n<e.inputs.length;n++){const r=e.inputs[n];if(I(r)){t.push(r);continue}const a=Ke(r);if(a===-1)return-1;t.push(a)}return e.operation(t)}function dc(e){if(e.length!==2)return-1;const t=e[0].value,n=e[1].value;if(H(t)&&H(n)){const r=t[4].value*n[4].value;return new G([v.Number,r.toString(),t[2],n[3],{value:r,type:t[4].type===S.Integer&&n[4].type===S.Integer?S.Integer:S.Number}])}if(ne(t)&&H(n)){const r=t[4].value*n[4].value;return new G([v.Percentage,r.toString()+"%",t[2],n[3],{value:r}])}if(H(t)&&ne(n)){const r=t[4].value*n[4].value;return new G([v.Percentage,r.toString()+"%",t[2],n[3],{value:r}])}if(se(t)&&H(n)){const r=t[4].value*n[4].value;return new G([v.Dimension,r.toString()+t[4].unit,t[2],n[3],{value:r,type:t[4].type===S.Integer&&n[4].type===S.Integer?S.Integer:S.Number,unit:t[4].unit}])}if(H(t)&&se(n)){const r=t[4].value*n[4].value;return new G([v.Dimension,r.toString()+n[4].unit,t[2],n[3],{value:r,type:t[4].type===S.Integer&&n[4].type===S.Integer?S.Integer:S.Number,unit:n[4].unit}])}return-1}function jn(e,t){for(let n=0;n<e.length;n++){const r=e[n];if(!I(r))continue;const a=r.value;if(!ue(a))continue;const s=Xe(a[4].value);switch(s){case"e":e.splice(n,1,new G([v.Number,Math.E.toString(),a[2],a[3],{value:Math.E,type:S.Number}]));break;case"pi":e.splice(n,1,new G([v.Number,Math.PI.toString(),a[2],a[3],{value:Math.PI,type:S.Number}]));break;case"infinity":e.splice(n,1,new G([v.Number,"infinity",a[2],a[3],{value:1/0,type:S.Number}]));break;case"-infinity":e.splice(n,1,new G([v.Number,"-infinity",a[2],a[3],{value:-1/0,type:S.Number}]));break;case"nan":e.splice(n,1,new G([v.Number,"NaN",a[2],a[3],{value:Number.NaN,type:S.Number}]));break;default:if(t.has(s)){const o=t.get(s);e.splice(n,1,new G(o))}}}return e}function ca(e){if(e.length!==1)return-1;const t=e[0].value;return fe(t)?e[0]:-1}function ht(e,t,n){return se(t)?xr(e,t[4].unit,n):ne(t)?mc(e,n):H(t)?Bt(e,n):-1}function xr(e,t,n){const r=e.tokens();return{inputs:[new G([v.Dimension,n.toString()+t,r[0][2],r[r.length-1][3],{value:n,type:Number.isInteger(n)?S.Integer:S.Number,unit:t}])],operation:ca}}function mc(e,t){const n=e.tokens();return{inputs:[new G([v.Percentage,t.toString()+"%",n[0][2],n[n.length-1][3],{value:t}])],operation:ca}}function Bt(e,t){const n=e.tokens();return{inputs:[new G([v.Number,t.toString(),n[0][2],n[n.length-1][3],{value:t,type:Number.isInteger(t)?S.Integer:S.Number}])],operation:ca}}function gc(e,t){const n=t.value;return H(n)?xr(e,"rad",Math.acos(n[4].value)):-1}function vc(e,t){const n=t.value;return H(n)?xr(e,"rad",Math.asin(n[4].value)):-1}function bc(e,t){const n=t.value;return H(n)?xr(e,"rad",Math.atan(n[4].value)):-1}function ua(e){return se(e)||H(e)}function ns(e){if(e.length===0)return!0;const t=e[0];if(!fe(t))return!1;if(e.length===1)return!0;if(se(t)){const n=Xe(t[4].unit);for(let r=1;r<e.length;r++){const a=e[r];if(t[0]!==a[0]||n!==Xe(a[4].unit))return!1}return!0}for(let n=1;n<e.length;n++){const r=e[n];if(t[0]!==r[0])return!1}return!0}function Qt(e,t){return!!fe(e)&&(se(e)?e[0]===t[0]&&Xe(e[4].unit)===Xe(t[4].unit):e[0]===t[0])}function wc(e,t,n){const r=t.value;if(!ua(r))return-1;const a=st(r,n.value);return Qt(r,a)?xr(e,"rad",Math.atan2(r[4].value,a[4].value)):-1}function $c(e,t,n){const r=t.value;return!fe(r)||!n.rawPercentages&&ne(r)?-1:ht(e,r,Math.abs(r[4].value))}function yc(e,t,n,r,a){if(!I(t)||!I(n)||!I(r))return-1;const s=t.value;if(!fe(s)||!a.rawPercentages&&ne(s))return-1;const o=st(s,n.value);if(!Qt(s,o))return-1;const i=st(s,r.value);return Qt(s,i)?ht(e,s,Math.max(s[4].value,Math.min(o[4].value,i[4].value))):-1}function Nc(e,t){const n=t.value;if(!ua(n))return-1;let r=n[4].value;if(se(n))switch(n[4].unit.toLowerCase()){case"rad":break;case"deg":r=la.get("rad")(n[4].value);break;case"grad":r=Er.get("rad")(n[4].value);break;case"turn":r=Cr.get("rad")(n[4].value);break;default:return-1}return r=Math.cos(r),Bt(e,r)}function Ec(e,t){const n=t.value;return H(n)?Bt(e,Math.exp(n[4].value)):-1}function Cc(e,t,n){if(!t.every(I))return-1;const r=t[0].value;if(!fe(r)||!n.rawPercentages&&ne(r))return-1;const a=t.map((i=>st(r,i.value)));if(!ns(a))return-1;const s=a.map((i=>i[4].value)),o=Math.hypot(...s);return ht(e,r,o)}function yo(e,t,n){if(!t.every(I))return-1;const r=t[0].value;if(!fe(r)||!n.rawPercentages&&ne(r))return-1;const a=t.map((i=>st(r,i.value)));if(!ns(a))return-1;const s=a.map((i=>i[4].value)),o=Math.max(...s);return ht(e,r,o)}function No(e,t,n){if(!t.every(I))return-1;const r=t[0].value;if(!fe(r)||!n.rawPercentages&&ne(r))return-1;const a=t.map((i=>st(r,i.value)));if(!ns(a))return-1;const s=a.map((i=>i[4].value)),o=Math.min(...s);return ht(e,r,o)}function xc(e,t,n){const r=t.value;if(!fe(r))return-1;const a=st(r,n.value);if(!Qt(r,a))return-1;let s;return s=a[4].value===0?Number.NaN:Number.isFinite(r[4].value)&&(Number.isFinite(a[4].value)||(a[4].value!==Number.POSITIVE_INFINITY||r[4].value!==Number.NEGATIVE_INFINITY&&!Object.is(0*r[4].value,-0))&&(a[4].value!==Number.NEGATIVE_INFINITY||r[4].value!==Number.POSITIVE_INFINITY&&!Object.is(0*r[4].value,0)))?Number.isFinite(a[4].value)?(r[4].value%a[4].value+a[4].value)%a[4].value:r[4].value:Number.NaN,ht(e,r,s)}function Fc(e,t,n){const r=t.value,a=n.value;return!H(r)||!Qt(r,a)?-1:Bt(e,Math.pow(r[4].value,a[4].value))}function kc(e,t,n){const r=t.value;if(!fe(r))return-1;const a=st(r,n.value);if(!Qt(r,a))return-1;let s;return s=a[4].value===0?Number.NaN:Number.isFinite(r[4].value)?Number.isFinite(a[4].value)?r[4].value%a[4].value:r[4].value:Number.NaN,ht(e,r,s)}function Sc(e,t,n,r,a){const s=n.value;if(!fe(s)||!a.rawPercentages&&ne(s))return-1;const o=st(s,r.value);if(!Qt(s,o))return-1;let i;if(o[4].value===0)i=Number.NaN;else if(Number.isFinite(s[4].value)||Number.isFinite(o[4].value))if(!Number.isFinite(s[4].value)&&Number.isFinite(o[4].value))i=s[4].value;else if(Number.isFinite(s[4].value)&&!Number.isFinite(o[4].value))switch(t){case"down":i=s[4].value<0?-1/0:Object.is(-0,0*s[4].value)?-0:0;break;case"up":i=s[4].value>0?1/0:Object.is(0,0*s[4].value)?0:-0;break;default:i=Object.is(0,0*s[4].value)?0:-0}else if(Number.isFinite(o[4].value))switch(t){case"down":i=Math.floor(s[4].value/o[4].value)*o[4].value;break;case"up":i=Math.ceil(s[4].value/o[4].value)*o[4].value;break;case"to-zero":i=Math.trunc(s[4].value/o[4].value)*o[4].value;break;default:{let c=Math.floor(s[4].value/o[4].value)*o[4].value,l=Math.ceil(s[4].value/o[4].value)*o[4].value;if(c>l){const d=c;c=l,l=d}const u=Math.abs(s[4].value-c),f=Math.abs(s[4].value-l);i=u===f?l:u<f?c:l;break}}else i=s[4].value;else i=Number.NaN;return ht(e,s,i)}function Mc(e,t,n){const r=t.value;return!fe(r)||!n.rawPercentages&&ne(r)?-1:Bt(e,Math.sign(r[4].value))}function Ac(e,t){const n=t.value;if(!ua(n))return-1;let r=n[4].value;if(se(n))switch(Xe(n[4].unit)){case"rad":break;case"deg":r=la.get("rad")(n[4].value);break;case"grad":r=Er.get("rad")(n[4].value);break;case"turn":r=Cr.get("rad")(n[4].value);break;default:return-1}return r=Math.sin(r),Bt(e,r)}function Pc(e,t){const n=t.value;return H(n)?Bt(e,Math.sqrt(n[4].value)):-1}function Dc(e,t){const n=t.value;if(!ua(n))return-1;const r=n[4].value;let a=0,s=n[4].value;if(se(n))switch(Xe(n[4].unit)){case"rad":a=wo.get("deg")(r);break;case"deg":a=r,s=la.get("rad")(r);break;case"grad":a=Er.get("deg")(r),s=Er.get("rad")(r);break;case"turn":a=Cr.get("deg")(r),s=Cr.get("rad")(r);break;default:return-1}const o=a/90;return s=a%90==0&&o%2!=0?o>0?1/0:-1/0:Math.tan(s),Bt(e,s)}function Bc(e){if(e.length!==2)return-1;const t=e[0].value;let n=e[1].value;if(H(t)&&H(n)){const r=t[4].value-n[4].value;return new G([v.Number,r.toString(),t[2],n[3],{value:r,type:t[4].type===S.Integer&&n[4].type===S.Integer?S.Integer:S.Number}])}if(ne(t)&&ne(n)){const r=t[4].value-n[4].value;return new G([v.Percentage,r.toString()+"%",t[2],n[3],{value:r}])}if(se(t)&&se(n)&&(n=st(t,n),Xe(t[4].unit)===Xe(n[4].unit))){const r=t[4].value-n[4].value;return new G([v.Dimension,r.toString()+t[4].unit,t[2],n[3],{value:r,type:t[4].type===S.Integer&&n[4].type===S.Integer?S.Integer:S.Number,unit:t[4].unit}])}return-1}function Rc(e,t){if(t.length===1){const n=t[0];if(!n||!I(n))return-1;const r=n.value;return H(r)?Bt(e,Math.log(r[4].value)):-1}if(t.length===2){const n=t[0];if(!n||!I(n))return-1;const r=n.value;if(!H(r))return-1;const a=t[1];if(!a||!I(a))return-1;const s=a.value;return H(s)?Bt(e,Math.log(r[4].value)/Math.log(s[4].value)):-1}return-1}const Oc=/^none$/i;function rs(e){if(Array.isArray(e)){const n=e.filter((r=>!(rt(r)&&at(r))));return n.length===1&&rs(n[0])}if(!I(e))return!1;const t=e.value;return!!ue(t)&&Oc.test(t[4].value)}const Wc=String.fromCodePoint(0);function Lc(e,t,n,r,a,s){var o;if(t.fixed===-1&&!s.randomCaching)return-1;s.randomCaching||(s.randomCaching={propertyName:"",propertyN:0,elementID:"",documentID:""}),s.randomCaching&&!s.randomCaching.propertyN&&(s.randomCaching.propertyN=0);const i=n.value;if(!fe(i))return-1;const c=st(i,r.value);if(!Qt(i,c))return-1;let l=null;if(a&&(l=st(i,a.value),!Qt(i,l)))return-1;if(!Number.isFinite(i[4].value)||!Number.isFinite(c[4].value)||!Number.isFinite(c[4].value-i[4].value))return ht(e,i,Number.NaN);if(l&&!Number.isFinite(l[4].value))return ht(e,i,i[4].value);const u=t.fixed===-1?_c(Ic([t.dashedIdent?t.dashedIdent:`${(o=s.randomCaching)==null?void 0:o.propertyName} ${s.randomCaching.propertyN++}`,t.elementShared?"":s.randomCaching.elementID,s.randomCaching.documentID].join(Wc))):()=>t.fixed;let f=i[4].value,d=c[4].value;if(f>d&&([f,d]=[d,f]),l&&(l[4].value<=0||Math.abs(f-d)/l[4].value>1e10)&&(l=null),l){const p=Math.max(l[4].value/1e3,1e-9),m=[f];let y=0;for(;;){y+=l[4].value;const N=f+y;if(!(N+p<d)){m.push(d);break}if(m.push(N),N+l[4].value-p>d)break}const $=u();return ht(e,i,Number(m[Math.floor(m.length*$)].toFixed(5)))}const b=u();return ht(e,i,Number((b*(d-f)+f).toFixed(5)))}function _c(e=.34944106645296036,t=.19228640875738723,n=.8784393832007205,r=.04850964319275053){return()=>{const a=((e|=0)+(t|=0)|0)+(r|=0)|0;return r=r+1|0,e=t^t>>>9,t=(n|=0)+(n<<3)|0,n=(n=n<<21|n>>>11)+a|0,(a>>>0)/4294967296}}function Ic(e){let t=0,n=0,r=0;t^=-1;for(let a=0,s=e.length;a<s;a++)r=255&(t^e.charCodeAt(a)),n=+("0x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substring(9*r,9*r+8)),t=t>>>8^n;return(-1^t)>>>0}const as=new Map([["abs",function(e,t,n){return Rt(e,t,n,$c)}],["acos",function(e,t,n){return Rt(e,t,n,gc)}],["asin",function(e,t,n){return Rt(e,t,n,vc)}],["atan",function(e,t,n){return Rt(e,t,n,bc)}],["atan2",function(e,t,n){return ha(e,t,n,wc)}],["calc",Te],["clamp",function(e,t,n){const r=jn([...e.value.filter((p=>!Jt(p)))],t),a=[],s=[],o=[];{let p=a;for(let m=0;m<r.length;m++){const y=r[m];if(I(y)&&Nt(y.value)){if(p===o)return-1;if(p===s){p=o;continue}if(p===a){p=s;continue}return-1}p.push(y)}}const i=rs(a),c=rs(o);if(i&&c)return Te(ot(s),t,n);const l=Ke(Te(ot(s),t,n));if(l===-1)return-1;if(i){const p=Ke(Te(ot(o),t,n));return p===-1?-1:No((u=l,f=p,new qe([v.Function,"min(",-1,-1,{value:"min"}],[v.CloseParen,")",-1,-1,void 0],[u,new G([v.Comma,",",-1,-1,void 0]),f])),[l,p],n)}if(c){const p=Ke(Te(ot(a),t,n));return p===-1?-1:yo(Gc(p,l),[p,l],n)}var u,f;const d=Ke(Te(ot(a),t,n));if(d===-1)return-1;const b=Ke(Te(ot(o),t,n));return b===-1?-1:yc(e,d,l,b,n)}],["cos",function(e,t,n){return Rt(e,t,n,Nc)}],["exp",function(e,t,n){return Rt(e,t,n,Ec)}],["hypot",function(e,t,n){return fa(e,e.value,t,n,Cc)}],["log",function(e,t,n){return fa(e,e.value,t,n,Rc)}],["max",function(e,t,n){return fa(e,e.value,t,n,yo)}],["min",function(e,t,n){return fa(e,e.value,t,n,No)}],["mod",function(e,t,n){return ha(e,t,n,xc)}],["pow",function(e,t,n){return ha(e,t,n,Fc)}],["random",function(e,t,n){const r=zc(e.value.filter((u=>!Jt(u))),t,n);if(r===-1)return-1;const[a,s]=r,o=Eo(s,t,n);if(o===-1)return-1;const[i,c,l]=o;return!i||!c?-1:Lc(e,a,i,c,l,n)}],["rem",function(e,t,n){return ha(e,t,n,kc)}],["round",function(e,t,n){const r=jn([...e.value.filter((u=>!Jt(u)))],t);let a="",s=!1;const o=[],i=[];{let u=o;for(let f=0;f<r.length;f++){const d=r[f];if(!a&&o.length===0&&i.length===0&&I(d)&&ue(d.value)){const b=d.value[4].value.toLowerCase();if(Uc.has(b)){a=b;continue}}if(I(d)&&Nt(d.value)){if(u===i)return-1;if(u===o&&a&&o.length===0)continue;if(u===o){s=!0,u=i;continue}return-1}u.push(d)}}const c=Ke(Te(ot(o),t,n));if(c===-1)return-1;s||i.length!==0||i.push(new G([v.Number,"1",-1,-1,{value:1,type:S.Integer}]));const l=Ke(Te(ot(i),t,n));return l===-1?-1:(a||(a="nearest"),Sc(e,a,c,l,n))}],["sign",function(e,t,n){return Rt(e,t,n,Mc)}],["sin",function(e,t,n){return Rt(e,t,n,Ac)}],["sqrt",function(e,t,n){return Rt(e,t,n,Pc)}],["tan",function(e,t,n){return Rt(e,t,n,Dc)}]]);function Te(e,t,n){const r=jn([...e.value.filter((s=>!Jt(s)))],t);if(r.length===1&&I(r[0]))return{inputs:[r[0]],operation:ca};let a=0;for(;a<r.length;){const s=r[a];if(Zl(s)&&fo(s.startToken)){const o=Te(s,t,n);if(o===-1)return-1;r.splice(a,1,o)}else if(Ve(s)){const o=as.get(s.getName().toLowerCase());if(!o)return-1;const i=o(s,t,n);if(i===-1)return-1;r.splice(a,1,i)}else a++}if(a=0,r.length===1&&Fn(r[0]))return r[0];for(;a<r.length;){const s=r[a];if(!s||!I(s)&&!Fn(s)){a++;continue}const o=r[a+1];if(!o||!I(o)){a++;continue}const i=o.value;if(!Nr(i)||i[4].value!=="*"&&i[4].value!=="/"){a++;continue}const c=r[a+2];if(!c||!I(c)&&!Fn(c))return-1;i[4].value!=="*"?i[4].value!=="/"?a++:r.splice(a,3,{inputs:[s,c],operation:pc}):r.splice(a,3,{inputs:[s,c],operation:dc})}if(a=0,r.length===1&&Fn(r[0]))return r[0];for(;a<r.length;){const s=r[a];if(!s||!I(s)&&!Fn(s)){a++;continue}const o=r[a+1];if(!o||!I(o)){a++;continue}const i=o.value;if(!Nr(i)||i[4].value!=="+"&&i[4].value!=="-"){a++;continue}const c=r[a+2];if(!c||!I(c)&&!Fn(c))return-1;i[4].value!=="+"?i[4].value!=="-"?a++:r.splice(a,3,{inputs:[s,c],operation:Bc}):r.splice(a,3,{inputs:[s,c],operation:fc})}return r.length===1&&Fn(r[0])?r[0]:-1}function Rt(e,t,n,r){const a=Tc(e.value,t,n);return a===-1?-1:r(e,a,n)}function Tc(e,t,n){const r=Ke(Te(ot(jn([...e.filter((a=>!Jt(a)))],t)),t,n));return r===-1?-1:r}function ha(e,t,n,r){const a=Hc(e.value,t,n);if(a===-1)return-1;const[s,o]=a;return r(e,s,o,n)}function Hc(e,t,n){const r=jn([...e.filter((c=>!Jt(c)))],t),a=[],s=[];{let c=a;for(let l=0;l<r.length;l++){const u=r[l];if(I(u)&&Nt(u.value)){if(c===s)return-1;if(c===a){c=s;continue}return-1}c.push(u)}}const o=Ke(Te(ot(a),t,n));if(o===-1)return-1;const i=Ke(Te(ot(s),t,n));return i===-1?-1:[o,i]}function fa(e,t,n,r,a){const s=Eo(e.value,n,r);return s===-1?-1:a(e,s,r)}function Eo(e,t,n){const r=jn([...e.filter((s=>!Jt(s)))],t),a=[];{const s=[];let o=[];for(let i=0;i<r.length;i++){const c=r[i];I(c)&&Nt(c.value)?(s.push(o),o=[]):o.push(c)}s.push(o);for(let i=0;i<s.length;i++){if(s[i].length===0)return-1;const c=Ke(Te(ot(s[i]),t,n));if(c===-1)return-1;a.push(c)}}return a}const Uc=new Set(["nearest","up","down","to-zero"]);function zc(e,t,n){const r={isAuto:!1,dashedIdent:"",fixed:-1,elementShared:!1},a=e[0];if(!I(a)||!ue(a.value))return[r,e];for(let s=0;s<e.length;s++){const o=e[s];if(!I(o))return-1;if(Nt(o.value))return[r,e.slice(s+1)];if(!ue(o.value))return-1;const i=o.value[4].value.toLowerCase();if(i!=="element-shared")if(i!=="fixed")if(i!=="auto"){if(i.startsWith("--")){if(r.fixed!==-1||r.isAuto)return-1;r.dashedIdent=i}}else{if(r.fixed!==-1||r.dashedIdent)return-1;r.isAuto=!0}else{if(r.elementShared||r.dashedIdent||r.isAuto)return-1;s++;const c=e[s];if(!c)return-1;const l=Ke(Te(ot([c]),t,n));if(l===-1||!H(l.value)||l.value[4].value<0||l.value[4].value>1)return-1;r.fixed=Math.max(0,Math.min(l.value[4].value,1-1e-9))}else{if(r.fixed!==-1)return-1;r.elementShared=!0}}return-1}function ot(e){return new qe([v.Function,"calc(",-1,-1,{value:"calc"}],[v.CloseParen,")",-1,-1,void 0],e)}function Gc(e,t){return new qe([v.Function,"max(",-1,-1,{value:"max"}],[v.CloseParen,")",-1,-1,void 0],[e,new G([v.Comma,",",-1,-1,void 0]),t])}function jc(e){if(e===-1)return-1;if(Ve(e))return e;const t=e.value;return fe(t)&&Number.isNaN(t[4].value)?H(t)?new qe([v.Function,"calc(",t[2],t[3],{value:"calc"}],[v.CloseParen,")",t[2],t[3],void 0],[new G([v.Ident,"NaN",t[2],t[3],{value:"NaN"}])]):se(t)?new qe([v.Function,"calc(",t[2],t[3],{value:"calc"}],[v.CloseParen,")",t[2],t[3],void 0],[new G([v.Ident,"NaN",t[2],t[3],{value:"NaN"}]),new lt([[v.Whitespace," ",t[2],t[3],void 0]]),new G([v.Delim,"*",t[2],t[3],{value:"*"}]),new lt([[v.Whitespace," ",t[2],t[3],void 0]]),new G([v.Dimension,"1"+t[4].unit,t[2],t[3],{value:1,type:S.Integer,unit:t[4].unit}])]):ne(t)?new qe([v.Function,"calc(",t[2],t[3],{value:"calc"}],[v.CloseParen,")",t[2],t[3],void 0],[new G([v.Ident,"NaN",t[2],t[3],{value:"NaN"}]),new lt([[v.Whitespace," ",t[2],t[3],void 0]]),new G([v.Delim,"*",t[2],t[3],{value:"*"}]),new lt([[v.Whitespace," ",t[2],t[3],void 0]]),new G([v.Percentage,"1%",t[2],t[3],{value:1}])]):-1:e}function qc(e){if(e===-1)return-1;if(Ve(e))return e;const t=e.value;if(!fe(t)||Number.isFinite(t[4].value)||Number.isNaN(t[4].value))return e;let n="";return Number.NEGATIVE_INFINITY===t[4].value&&(n="-"),H(t)?new qe([v.Function,"calc(",t[2],t[3],{value:"calc"}],[v.CloseParen,")",t[2],t[3],void 0],[new G([v.Ident,n+"infinity",t[2],t[3],{value:n+"infinity"}])]):se(t)?new qe([v.Function,"calc(",t[2],t[3],{value:"calc"}],[v.CloseParen,")",t[2],t[3],void 0],[new G([v.Ident,n+"infinity",t[2],t[3],{value:n+"infinity"}]),new lt([[v.Whitespace," ",t[2],t[3],void 0]]),new G([v.Delim,"*",t[2],t[3],{value:"*"}]),new lt([[v.Whitespace," ",t[2],t[3],void 0]]),new G([v.Dimension,"1"+t[4].unit,t[2],t[3],{value:1,type:S.Integer,unit:t[4].unit}])]):new qe([v.Function,"calc(",t[2],t[3],{value:"calc"}],[v.CloseParen,")",t[2],t[3],void 0],[new G([v.Ident,n+"infinity",t[2],t[3],{value:n+"infinity"}]),new lt([[v.Whitespace," ",t[2],t[3],void 0]]),new G([v.Delim,"*",t[2],t[3],{value:"*"}]),new lt([[v.Whitespace," ",t[2],t[3],void 0]]),new G([v.Percentage,"1%",t[2],t[3],{value:1}])])}function Vc(e){if(e===-1)return-1;if(Ve(e))return e;const t=e.value;return fe(t)&&Object.is(-0,t[4].value)&&(t[1]==="-0"||(ne(t)?t[1]="-0%":se(t)?t[1]="-0"+t[4].unit:t[1]="-0")),e}function Xc(e,t=13){if(e===-1)return-1;if(t<=0||Ve(e))return e;const n=e.value;if(!fe(n)||Number.isInteger(n[4].value))return e;const r=Number(n[4].value.toFixed(t)).toString();return H(n)?n[1]=r:ne(n)?n[1]=r+"%":se(n)&&(n[1]=r+n[4].unit),e}function Kc(e){return e===-1?-1:(Ve(e)||se(e.value)&&(e.value=hc(e.value)),e)}function Yc(e,t){let n=e;return t!=null&&t.toCanonicalUnits&&(n=Kc(n)),n=Xc(n,t?.precision),n=Vc(n),t!=null&&t.censorIntoStandardRepresentableValues||(n=jc(n),n=qc(n)),n}function Zc(e){const t=new Map;if(!e)return t;for(const[n,r]of e)if(es(r))t.set(n,r);else if(typeof r=="string"){const a=lo({css:r}),s=a.nextToken();if(a.nextToken(),!a.endOfFile()||!fe(s))continue;t.set(n,s)}return t}function He(e,t){return qn(Xl(Zt({css:e}),{}),t).map((n=>n.map((r=>Yt(...r.tokens()))).join(""))).join(",")}function qn(e,t){const n=Zc(t?.globals);return bo(e,(r=>{if(!Ve(r))return;const a=as.get(r.getName().toLowerCase());if(!a)return;const s=Yc(Ke(a(r,n,t??{})),t);return s!==-1?s:void 0}))}const Fr=new Set(as.keys()),Jc=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,Co=new Set,ss=typeof process=="object"&&process?process:{},xo=(e,t,n,r)=>{typeof ss.emitWarning=="function"?ss.emitWarning(e,t,n,r):console.error(`[${n}] ${t}: ${e}`)};let pa=globalThis.AbortController,Fo=globalThis.AbortSignal;var ko;if(typeof pa>"u"){Fo=class{constructor(){X(this,"onabort"),X(this,"_onabort",[]),X(this,"reason"),X(this,"aborted",!1)}addEventListener(n,r){this._onabort.push(r)}},pa=class{constructor(){X(this,"signal",new Fo),t()}abort(n){var r,a;if(!this.signal.aborted){this.signal.reason=n,this.signal.aborted=!0;for(const s of this.signal._onabort)s(n);(a=(r=this.signal).onabort)==null||a.call(r,n)}}};let e=((ko=ss.env)==null?void 0:ko.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{e&&(e=!1,xo("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const Qc=e=>!Co.has(e),fn=e=>e&&e===Math.floor(e)&&e>0&&isFinite(e),So=e=>fn(e)?e<=Math.pow(2,8)?Uint8Array:e<=Math.pow(2,16)?Uint16Array:e<=Math.pow(2,32)?Uint32Array:e<=Number.MAX_SAFE_INTEGER?da:null:null;class da extends Array{constructor(t){super(t),this.fill(0)}}var kr;const Mo=class ta{constructor(t,n){if(X(this,"heap"),X(this,"length"),!h(ta,kr))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new n(t),this.length=0}static create(t){const n=So(t);if(!n)return[];F(ta,kr,!0);const r=new ta(t,n);return F(ta,kr,!1),r}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};kr=new WeakMap,U(Mo,kr,!1);let eu=Mo;var Ao,Po,Ot,ft,Wt,Vn,Lt,Sr,Mr,_t,ke,pt,Ee,he,Y,Ye,dt,Ue,Ae,It,Pe,Tt,Ht,mt,Ut,kn,Ze,Ar,W,os,Xn,pn,ma,gt,Do,Kn,Pr,ga,dn,mn,is,va,ba,ce,ls,Dr,gn,cs;const tu=class pl{constructor(t){U(this,W),U(this,Ot),U(this,ft),U(this,Wt),U(this,Vn),U(this,Lt),U(this,Sr),U(this,Mr),U(this,_t),X(this,"ttl"),X(this,"ttlResolution"),X(this,"ttlAutopurge"),X(this,"updateAgeOnGet"),X(this,"updateAgeOnHas"),X(this,"allowStale"),X(this,"noDisposeOnSet"),X(this,"noUpdateTTL"),X(this,"maxEntrySize"),X(this,"sizeCalculation"),X(this,"noDeleteOnFetchRejection"),X(this,"noDeleteOnStaleGet"),X(this,"allowStaleOnFetchAbort"),X(this,"allowStaleOnFetchRejection"),X(this,"ignoreFetchAbort"),U(this,ke),U(this,pt),U(this,Ee),U(this,he),U(this,Y),U(this,Ye),U(this,dt),U(this,Ue),U(this,Ae),U(this,It),U(this,Pe),U(this,Tt),U(this,Ht),U(this,mt),U(this,Ut),U(this,kn),U(this,Ze),U(this,Ar),U(this,Xn,()=>{}),U(this,pn,()=>{}),U(this,ma,()=>{}),U(this,gt,()=>!1),U(this,Kn,z=>{}),U(this,Pr,(z,j,K)=>{}),U(this,ga,(z,j,K,ee)=>{if(K||ee)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0}),X(this,Ao,"LRUCache");const{max:n=0,ttl:r,ttlResolution:a=1,ttlAutopurge:s,updateAgeOnGet:o,updateAgeOnHas:i,allowStale:c,dispose:l,onInsert:u,disposeAfter:f,noDisposeOnSet:d,noUpdateTTL:b,maxSize:p=0,maxEntrySize:m=0,sizeCalculation:y,fetchMethod:$,memoMethod:N,noDeleteOnFetchRejection:C,noDeleteOnStaleGet:E,allowStaleOnFetchRejection:J,allowStaleOnFetchAbort:P,ignoreFetchAbort:B,perf:M}=t;if(M!==void 0&&typeof M?.now!="function")throw new TypeError("perf option must have a now() method if specified");if(F(this,_t,M??Jc),n!==0&&!fn(n))throw new TypeError("max option must be a nonnegative integer");const O=n?So(n):Array;if(!O)throw new Error("invalid max value: "+n);if(F(this,Ot,n),F(this,ft,p),this.maxEntrySize=m||h(this,ft),this.sizeCalculation=y,this.sizeCalculation){if(!h(this,ft)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(N!==void 0&&typeof N!="function")throw new TypeError("memoMethod must be a function if defined");if(F(this,Mr,N),$!==void 0&&typeof $!="function")throw new TypeError("fetchMethod must be a function if specified");if(F(this,Sr,$),F(this,kn,!!$),F(this,Ee,new Map),F(this,he,new Array(n).fill(void 0)),F(this,Y,new Array(n).fill(void 0)),F(this,Ye,new O(n)),F(this,dt,new O(n)),F(this,Ue,0),F(this,Ae,0),F(this,It,eu.create(n)),F(this,ke,0),F(this,pt,0),typeof l=="function"&&F(this,Wt,l),typeof u=="function"&&F(this,Vn,u),typeof f=="function"?(F(this,Lt,f),F(this,Pe,[])):(F(this,Lt,void 0),F(this,Pe,void 0)),F(this,Ut,!!h(this,Wt)),F(this,Ar,!!h(this,Vn)),F(this,Ze,!!h(this,Lt)),this.noDisposeOnSet=!!d,this.noUpdateTTL=!!b,this.noDeleteOnFetchRejection=!!C,this.allowStaleOnFetchRejection=!!J,this.allowStaleOnFetchAbort=!!P,this.ignoreFetchAbort=!!B,this.maxEntrySize!==0){if(h(this,ft)!==0&&!fn(h(this,ft)))throw new TypeError("maxSize must be a positive integer if specified");if(!fn(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");_(this,W,Do).call(this)}if(this.allowStale=!!c,this.noDeleteOnStaleGet=!!E,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!i,this.ttlResolution=fn(a)||a===0?a:1,this.ttlAutopurge=!!s,this.ttl=r||0,this.ttl){if(!fn(this.ttl))throw new TypeError("ttl must be a positive integer if specified");_(this,W,os).call(this)}if(h(this,Ot)===0&&this.ttl===0&&h(this,ft)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!h(this,Ot)&&!h(this,ft)){const z="LRU_CACHE_UNBOUNDED";Qc(z)&&(Co.add(z),xo("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",z,pl))}}get perf(){return h(this,_t)}static unsafeExposeInternals(t){return{starts:h(t,Ht),ttls:h(t,mt),sizes:h(t,Tt),keyMap:h(t,Ee),keyList:h(t,he),valList:h(t,Y),next:h(t,Ye),prev:h(t,dt),get head(){return h(t,Ue)},get tail(){return h(t,Ae)},free:h(t,It),isBackgroundFetch:n=>{var r;return _(r=t,W,ce).call(r,n)},backgroundFetch:(n,r,a,s)=>{var o;return _(o=t,W,ba).call(o,n,r,a,s)},moveToTail:n=>{var r;return _(r=t,W,Dr).call(r,n)},indexes:n=>{var r;return _(r=t,W,dn).call(r,n)},rindexes:n=>{var r;return _(r=t,W,mn).call(r,n)},isStale:n=>{var r;return h(r=t,gt).call(r,n)}}}get max(){return h(this,Ot)}get maxSize(){return h(this,ft)}get calculatedSize(){return h(this,pt)}get size(){return h(this,ke)}get fetchMethod(){return h(this,Sr)}get memoMethod(){return h(this,Mr)}get dispose(){return h(this,Wt)}get onInsert(){return h(this,Vn)}get disposeAfter(){return h(this,Lt)}getRemainingTTL(t){return h(this,Ee).has(t)?1/0:0}*entries(){for(const t of _(this,W,dn).call(this))h(this,Y)[t]!==void 0&&h(this,he)[t]!==void 0&&!_(this,W,ce).call(this,h(this,Y)[t])&&(yield[h(this,he)[t],h(this,Y)[t]])}*rentries(){for(const t of _(this,W,mn).call(this))h(this,Y)[t]!==void 0&&h(this,he)[t]!==void 0&&!_(this,W,ce).call(this,h(this,Y)[t])&&(yield[h(this,he)[t],h(this,Y)[t]])}*keys(){for(const t of _(this,W,dn).call(this)){const n=h(this,he)[t];n!==void 0&&!_(this,W,ce).call(this,h(this,Y)[t])&&(yield n)}}*rkeys(){for(const t of _(this,W,mn).call(this)){const n=h(this,he)[t];n!==void 0&&!_(this,W,ce).call(this,h(this,Y)[t])&&(yield n)}}*values(){for(const t of _(this,W,dn).call(this))h(this,Y)[t]!==void 0&&!_(this,W,ce).call(this,h(this,Y)[t])&&(yield h(this,Y)[t])}*rvalues(){for(const t of _(this,W,mn).call(this))h(this,Y)[t]!==void 0&&!_(this,W,ce).call(this,h(this,Y)[t])&&(yield h(this,Y)[t])}[(Po=Symbol.iterator,Ao=Symbol.toStringTag,Po)](){return this.entries()}find(t,n={}){for(const r of _(this,W,dn).call(this)){const a=h(this,Y)[r],s=_(this,W,ce).call(this,a)?a.__staleWhileFetching:a;if(s!==void 0&&t(s,h(this,he)[r],this))return this.get(h(this,he)[r],n)}}forEach(t,n=this){for(const r of _(this,W,dn).call(this)){const a=h(this,Y)[r],s=_(this,W,ce).call(this,a)?a.__staleWhileFetching:a;s!==void 0&&t.call(n,s,h(this,he)[r],this)}}rforEach(t,n=this){for(const r of _(this,W,mn).call(this)){const a=h(this,Y)[r],s=_(this,W,ce).call(this,a)?a.__staleWhileFetching:a;s!==void 0&&t.call(n,s,h(this,he)[r],this)}}purgeStale(){let t=!1;for(const n of _(this,W,mn).call(this,{allowStale:!0}))h(this,gt).call(this,n)&&(_(this,W,gn).call(this,h(this,he)[n],"expire"),t=!0);return t}info(t){const n=h(this,Ee).get(t);if(n===void 0)return;const r=h(this,Y)[n],a=_(this,W,ce).call(this,r)?r.__staleWhileFetching:r;if(a===void 0)return;const s={value:a};if(h(this,mt)&&h(this,Ht)){const o=h(this,mt)[n],i=h(this,Ht)[n];if(o&&i){const c=o-(h(this,_t).now()-i);s.ttl=c,s.start=Date.now()}}return h(this,Tt)&&(s.size=h(this,Tt)[n]),s}dump(){const t=[];for(const n of _(this,W,dn).call(this,{allowStale:!0})){const r=h(this,he)[n],a=h(this,Y)[n],s=_(this,W,ce).call(this,a)?a.__staleWhileFetching:a;if(s===void 0||r===void 0)continue;const o={value:s};if(h(this,mt)&&h(this,Ht)){o.ttl=h(this,mt)[n];const i=h(this,_t).now()-h(this,Ht)[n];o.start=Math.floor(Date.now()-i)}h(this,Tt)&&(o.size=h(this,Tt)[n]),t.unshift([r,o])}return t}load(t){this.clear();for(const[n,r]of t){if(r.start){const a=Date.now()-r.start;r.start=h(this,_t).now()-a}this.set(n,r.value,r)}}set(t,n,r={}){var a,s,o,i,c,l,u;if(n===void 0)return this.delete(t),this;const{ttl:f=this.ttl,start:d,noDisposeOnSet:b=this.noDisposeOnSet,sizeCalculation:p=this.sizeCalculation,status:m}=r;let{noUpdateTTL:y=this.noUpdateTTL}=r;const $=h(this,ga).call(this,t,n,r.size||0,p);if(this.maxEntrySize&&$>this.maxEntrySize)return m&&(m.set="miss",m.maxEntrySizeExceeded=!0),_(this,W,gn).call(this,t,"set"),this;let N=h(this,ke)===0?void 0:h(this,Ee).get(t);if(N===void 0)N=h(this,ke)===0?h(this,Ae):h(this,It).length!==0?h(this,It).pop():h(this,ke)===h(this,Ot)?_(this,W,va).call(this,!1):h(this,ke),h(this,he)[N]=t,h(this,Y)[N]=n,h(this,Ee).set(t,N),h(this,Ye)[h(this,Ae)]=N,h(this,dt)[N]=h(this,Ae),F(this,Ae,N),Xa(this,ke)._++,h(this,Pr).call(this,N,$,m),m&&(m.set="add"),y=!1,h(this,Ar)&&((a=h(this,Vn))==null||a.call(this,n,t,"add"));else{_(this,W,Dr).call(this,N);const C=h(this,Y)[N];if(n!==C){if(h(this,kn)&&_(this,W,ce).call(this,C)){C.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:E}=C;E!==void 0&&!b&&(h(this,Ut)&&((s=h(this,Wt))==null||s.call(this,E,t,"set")),h(this,Ze)&&((o=h(this,Pe))==null||o.push([E,t,"set"])))}else b||(h(this,Ut)&&((i=h(this,Wt))==null||i.call(this,C,t,"set")),h(this,Ze)&&((c=h(this,Pe))==null||c.push([C,t,"set"])));if(h(this,Kn).call(this,N),h(this,Pr).call(this,N,$,m),h(this,Y)[N]=n,m){m.set="replace";const E=C&&_(this,W,ce).call(this,C)?C.__staleWhileFetching:C;E!==void 0&&(m.oldValue=E)}}else m&&(m.set="update");h(this,Ar)&&((l=this.onInsert)==null||l.call(this,n,t,n===C?"update":"replace"))}if(f!==0&&!h(this,mt)&&_(this,W,os).call(this),h(this,mt)&&(y||h(this,ma).call(this,N,f,d),m&&h(this,pn).call(this,m,N)),!b&&h(this,Ze)&&h(this,Pe)){const C=h(this,Pe);let E;for(;E=C?.shift();)(u=h(this,Lt))==null||u.call(this,...E)}return this}pop(){var t;try{for(;h(this,ke);){const n=h(this,Y)[h(this,Ue)];if(_(this,W,va).call(this,!0),_(this,W,ce).call(this,n)){if(n.__staleWhileFetching)return n.__staleWhileFetching}else if(n!==void 0)return n}}finally{if(h(this,Ze)&&h(this,Pe)){const n=h(this,Pe);let r;for(;r=n?.shift();)(t=h(this,Lt))==null||t.call(this,...r)}}}has(t,n={}){const{updateAgeOnHas:r=this.updateAgeOnHas,status:a}=n,s=h(this,Ee).get(t);if(s!==void 0){const o=h(this,Y)[s];if(_(this,W,ce).call(this,o)&&o.__staleWhileFetching===void 0)return!1;if(h(this,gt).call(this,s))a&&(a.has="stale",h(this,pn).call(this,a,s));else return r&&h(this,Xn).call(this,s),a&&(a.has="hit",h(this,pn).call(this,a,s)),!0}else a&&(a.has="miss");return!1}peek(t,n={}){const{allowStale:r=this.allowStale}=n,a=h(this,Ee).get(t);if(a===void 0||!r&&h(this,gt).call(this,a))return;const s=h(this,Y)[a];return _(this,W,ce).call(this,s)?s.__staleWhileFetching:s}async fetch(t,n={}){const{allowStale:r=this.allowStale,updateAgeOnGet:a=this.updateAgeOnGet,noDeleteOnStaleGet:s=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:i=this.noDisposeOnSet,size:c=0,sizeCalculation:l=this.sizeCalculation,noUpdateTTL:u=this.noUpdateTTL,noDeleteOnFetchRejection:f=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:d=this.allowStaleOnFetchRejection,ignoreFetchAbort:b=this.ignoreFetchAbort,allowStaleOnFetchAbort:p=this.allowStaleOnFetchAbort,context:m,forceRefresh:y=!1,status:$,signal:N}=n;if(!h(this,kn))return $&&($.fetch="get"),this.get(t,{allowStale:r,updateAgeOnGet:a,noDeleteOnStaleGet:s,status:$});const C={allowStale:r,updateAgeOnGet:a,noDeleteOnStaleGet:s,ttl:o,noDisposeOnSet:i,size:c,sizeCalculation:l,noUpdateTTL:u,noDeleteOnFetchRejection:f,allowStaleOnFetchRejection:d,allowStaleOnFetchAbort:p,ignoreFetchAbort:b,status:$,signal:N};let E=h(this,Ee).get(t);if(E===void 0){$&&($.fetch="miss");const J=_(this,W,ba).call(this,t,E,C,m);return J.__returned=J}else{const J=h(this,Y)[E];if(_(this,W,ce).call(this,J)){const O=r&&J.__staleWhileFetching!==void 0;return $&&($.fetch="inflight",O&&($.returnedStale=!0)),O?J.__staleWhileFetching:J.__returned=J}const P=h(this,gt).call(this,E);if(!y&&!P)return $&&($.fetch="hit"),_(this,W,Dr).call(this,E),a&&h(this,Xn).call(this,E),$&&h(this,pn).call(this,$,E),J;const B=_(this,W,ba).call(this,t,E,C,m),M=B.__staleWhileFetching!==void 0&&r;return $&&($.fetch=P?"stale":"refresh",M&&P&&($.returnedStale=!0)),M?B.__staleWhileFetching:B.__returned=B}}async forceFetch(t,n={}){const r=await this.fetch(t,n);if(r===void 0)throw new Error("fetch() returned undefined");return r}memo(t,n={}){const r=h(this,Mr);if(!r)throw new Error("no memoMethod provided to constructor");const{context:a,forceRefresh:s,...o}=n,i=this.get(t,o);if(!s&&i!==void 0)return i;const c=r(t,i,{options:o,context:a});return this.set(t,c,o),c}get(t,n={}){const{allowStale:r=this.allowStale,updateAgeOnGet:a=this.updateAgeOnGet,noDeleteOnStaleGet:s=this.noDeleteOnStaleGet,status:o}=n,i=h(this,Ee).get(t);if(i!==void 0){const c=h(this,Y)[i],l=_(this,W,ce).call(this,c);return o&&h(this,pn).call(this,o,i),h(this,gt).call(this,i)?(o&&(o.get="stale"),l?(o&&r&&c.__staleWhileFetching!==void 0&&(o.returnedStale=!0),r?c.__staleWhileFetching:void 0):(s||_(this,W,gn).call(this,t,"expire"),o&&r&&(o.returnedStale=!0),r?c:void 0)):(o&&(o.get="hit"),l?c.__staleWhileFetching:(_(this,W,Dr).call(this,i),a&&h(this,Xn).call(this,i),c))}else o&&(o.get="miss")}delete(t){return _(this,W,gn).call(this,t,"delete")}clear(){return _(this,W,cs).call(this,"delete")}};Ot=new WeakMap,ft=new WeakMap,Wt=new WeakMap,Vn=new WeakMap,Lt=new WeakMap,Sr=new WeakMap,Mr=new WeakMap,_t=new WeakMap,ke=new WeakMap,pt=new WeakMap,Ee=new WeakMap,he=new WeakMap,Y=new WeakMap,Ye=new WeakMap,dt=new WeakMap,Ue=new WeakMap,Ae=new WeakMap,It=new WeakMap,Pe=new WeakMap,Tt=new WeakMap,Ht=new WeakMap,mt=new WeakMap,Ut=new WeakMap,kn=new WeakMap,Ze=new WeakMap,Ar=new WeakMap,W=new WeakSet,os=function(){const e=new da(h(this,Ot)),t=new da(h(this,Ot));F(this,mt,e),F(this,Ht,t),F(this,ma,(a,s,o=h(this,_t).now())=>{if(t[a]=s!==0?o:0,e[a]=s,s!==0&&this.ttlAutopurge){const i=setTimeout(()=>{h(this,gt).call(this,a)&&_(this,W,gn).call(this,h(this,he)[a],"expire")},s+1);i.unref&&i.unref()}}),F(this,Xn,a=>{t[a]=e[a]!==0?h(this,_t).now():0}),F(this,pn,(a,s)=>{if(e[s]){const o=e[s],i=t[s];if(!o||!i)return;a.ttl=o,a.start=i,a.now=n||r();const c=a.now-i;a.remainingTTL=o-c}});let n=0;const r=()=>{const a=h(this,_t).now();if(this.ttlResolution>0){n=a;const s=setTimeout(()=>n=0,this.ttlResolution);s.unref&&s.unref()}return a};this.getRemainingTTL=a=>{const s=h(this,Ee).get(a);if(s===void 0)return 0;const o=e[s],i=t[s];if(!o||!i)return 1/0;const c=(n||r())-i;return o-c},F(this,gt,a=>{const s=t[a],o=e[a];return!!o&&!!s&&(n||r())-s>o})},Xn=new WeakMap,pn=new WeakMap,ma=new WeakMap,gt=new WeakMap,Do=function(){const e=new da(h(this,Ot));F(this,pt,0),F(this,Tt,e),F(this,Kn,t=>{F(this,pt,h(this,pt)-e[t]),e[t]=0}),F(this,ga,(t,n,r,a)=>{if(_(this,W,ce).call(this,n))return 0;if(!fn(r))if(a){if(typeof a!="function")throw new TypeError("sizeCalculation must be a function");if(r=a(n,t),!fn(r))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}else throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");return r}),F(this,Pr,(t,n,r)=>{if(e[t]=n,h(this,ft)){const a=h(this,ft)-e[t];for(;h(this,pt)>a;)_(this,W,va).call(this,!0)}F(this,pt,h(this,pt)+e[t]),r&&(r.entrySize=n,r.totalCalculatedSize=h(this,pt))})},Kn=new WeakMap,Pr=new WeakMap,ga=new WeakMap,dn=function*({allowStale:e=this.allowStale}={}){if(h(this,ke))for(let t=h(this,Ae);!(!_(this,W,is).call(this,t)||((e||!h(this,gt).call(this,t))&&(yield t),t===h(this,Ue)));)t=h(this,dt)[t]},mn=function*({allowStale:e=this.allowStale}={}){if(h(this,ke))for(let t=h(this,Ue);!(!_(this,W,is).call(this,t)||((e||!h(this,gt).call(this,t))&&(yield t),t===h(this,Ae)));)t=h(this,Ye)[t]},is=function(e){return e!==void 0&&h(this,Ee).get(h(this,he)[e])===e},va=function(e){var t,n;const r=h(this,Ue),a=h(this,he)[r],s=h(this,Y)[r];return h(this,kn)&&_(this,W,ce).call(this,s)?s.__abortController.abort(new Error("evicted")):(h(this,Ut)||h(this,Ze))&&(h(this,Ut)&&((t=h(this,Wt))==null||t.call(this,s,a,"evict")),h(this,Ze)&&((n=h(this,Pe))==null||n.push([s,a,"evict"]))),h(this,Kn).call(this,r),e&&(h(this,he)[r]=void 0,h(this,Y)[r]=void 0,h(this,It).push(r)),h(this,ke)===1?(F(this,Ue,F(this,Ae,0)),h(this,It).length=0):F(this,Ue,h(this,Ye)[r]),h(this,Ee).delete(a),Xa(this,ke)._--,r},ba=function(e,t,n,r){const a=t===void 0?void 0:h(this,Y)[t];if(_(this,W,ce).call(this,a))return a;const s=new pa,{signal:o}=n;o?.addEventListener("abort",()=>s.abort(o.reason),{signal:s.signal});const i={signal:s.signal,options:n,context:r},c=(p,m=!1)=>{const{aborted:y}=s.signal,$=n.ignoreFetchAbort&&p!==void 0;if(n.status&&(y&&!m?(n.status.fetchAborted=!0,n.status.fetchError=s.signal.reason,$&&(n.status.fetchAbortIgnored=!0)):n.status.fetchResolved=!0),y&&!$&&!m)return u(s.signal.reason);const N=d;return h(this,Y)[t]===d&&(p===void 0?N.__staleWhileFetching!==void 0?h(this,Y)[t]=N.__staleWhileFetching:_(this,W,gn).call(this,e,"fetch"):(n.status&&(n.status.fetchUpdated=!0),this.set(e,p,i.options))),p},l=p=>(n.status&&(n.status.fetchRejected=!0,n.status.fetchError=p),u(p)),u=p=>{const{aborted:m}=s.signal,y=m&&n.allowStaleOnFetchAbort,$=y||n.allowStaleOnFetchRejection,N=$||n.noDeleteOnFetchRejection,C=d;if(h(this,Y)[t]===d&&(!N||C.__staleWhileFetching===void 0?_(this,W,gn).call(this,e,"fetch"):y||(h(this,Y)[t]=C.__staleWhileFetching)),$)return n.status&&C.__staleWhileFetching!==void 0&&(n.status.returnedStale=!0),C.__staleWhileFetching;if(C.__returned===C)throw p},f=(p,m)=>{var y;const $=(y=h(this,Sr))==null?void 0:y.call(this,e,a,i);$&&$ instanceof Promise&&$.then(N=>p(N===void 0?void 0:N),m),s.signal.addEventListener("abort",()=>{(!n.ignoreFetchAbort||n.allowStaleOnFetchAbort)&&(p(void 0),n.allowStaleOnFetchAbort&&(p=N=>c(N,!0)))})};n.status&&(n.status.fetchDispatched=!0);const d=new Promise(f).then(c,l),b=Object.assign(d,{__abortController:s,__staleWhileFetching:a,__returned:void 0});return t===void 0?(this.set(e,b,{...i.options,status:void 0}),t=h(this,Ee).get(e)):h(this,Y)[t]=b,b},ce=function(e){if(!h(this,kn))return!1;const t=e;return!!t&&t instanceof Promise&&t.hasOwnProperty("__staleWhileFetching")&&t.__abortController instanceof pa},ls=function(e,t){h(this,dt)[t]=e,h(this,Ye)[e]=t},Dr=function(e){e!==h(this,Ae)&&(e===h(this,Ue)?F(this,Ue,h(this,Ye)[e]):_(this,W,ls).call(this,h(this,dt)[e],h(this,Ye)[e]),_(this,W,ls).call(this,h(this,Ae),e),F(this,Ae,e))},gn=function(e,t){var n,r,a,s;let o=!1;if(h(this,ke)!==0){const i=h(this,Ee).get(e);if(i!==void 0)if(o=!0,h(this,ke)===1)_(this,W,cs).call(this,t);else{h(this,Kn).call(this,i);const c=h(this,Y)[i];if(_(this,W,ce).call(this,c)?c.__abortController.abort(new Error("deleted")):(h(this,Ut)||h(this,Ze))&&(h(this,Ut)&&((n=h(this,Wt))==null||n.call(this,c,e,t)),h(this,Ze)&&((r=h(this,Pe))==null||r.push([c,e,t]))),h(this,Ee).delete(e),h(this,he)[i]=void 0,h(this,Y)[i]=void 0,i===h(this,Ae))F(this,Ae,h(this,dt)[i]);else if(i===h(this,Ue))F(this,Ue,h(this,Ye)[i]);else{const l=h(this,dt)[i];h(this,Ye)[l]=h(this,Ye)[i];const u=h(this,Ye)[i];h(this,dt)[u]=h(this,dt)[i]}Xa(this,ke)._--,h(this,It).push(i)}}if(h(this,Ze)&&(a=h(this,Pe))!=null&&a.length){const i=h(this,Pe);let c;for(;c=i?.shift();)(s=h(this,Lt))==null||s.call(this,...c)}return o},cs=function(e){var t,n,r;for(const a of _(this,W,mn).call(this,{allowStale:!0})){const s=h(this,Y)[a];if(_(this,W,ce).call(this,s))s.__abortController.abort(new Error("deleted"));else{const o=h(this,he)[a];h(this,Ut)&&((t=h(this,Wt))==null||t.call(this,s,o,e)),h(this,Ze)&&((n=h(this,Pe))==null||n.push([s,o,e]))}}if(h(this,Ee).clear(),h(this,Y).fill(void 0),h(this,he).fill(void 0),h(this,mt)&&h(this,Ht)&&(h(this,mt).fill(0),h(this,Ht).fill(0)),h(this,Tt)&&h(this,Tt).fill(0),F(this,Ue,0),F(this,Ae,0),h(this,It).length=0,F(this,pt,0),F(this,ke,0),h(this,Ze)&&h(this,Pe)){const a=h(this,Pe);let s;for(;s=a?.shift();)(r=h(this,Lt))==null||r.call(this,...s)}};let nu=tu;const k=e=>typeof e=="string"||e instanceof String,Yn=e=>k(e)||typeof e=="number",wa="(?:0|[1-9]\\d*)",ru="clamp|max|min",au="exp|hypot|log|pow|sqrt",su="abs|sign",ou="mod|rem|round",iu="a?(?:cos|sin|tan)|atan2",Bo=`${ru}|${au}|${su}|${ou}|${iu}`,us=`calc|${Bo}`,lu=`var|${us}`,Zn="deg|g?rad|turn",Br="[cm]m|[dls]?v(?:[bhiw]|max|min)|in|p[ctx]|q|r?(?:[cl]h|cap|e[mx]|ic)",ze=`[+-]?(?:${wa}(?:\\.\\d*)?|\\.\\d+)(?:e-?${wa})?`,Ro=`\\+?(?:${wa}(?:\\.\\d*)?|\\.\\d+)(?:e-?${wa})?`,g="none",Ge=`${ze}%`,$a=`^(?:${us})\\(|(?<=[*\\/\\s\\(])(?:${us})\\(`,Oo=`^(?:${Bo})\\($`,Rr="^var\\(|(?<=[*\\/\\s\\(])var\\(",cu=`^(?:${lu})\\(`,ya=`(?:\\s*\\/\\s*(?:${ze}|${Ge}|${g}))?`,Wo=`(?:\\s*,\\s*(?:${ze}|${Ge}))?`,Lo="(?:ok)?l(?:ab|ch)|color|hsla?|hwb|rgba?",uu="[a-z]+|#[\\da-f]{3}|#[\\da-f]{4}|#[\\da-f]{6}|#[\\da-f]{8}",_o="(?:ok)?lch|hsl|hwb",Io="(?:de|in)creasing|longer|shorter",hu=`${ze}(?:${Zn})?`,To=`(?:${ze}(?:${Zn})?|${g})`,Or=`(?:${ze}|${Ge}|${g})`,Ho=`(?:${_o})(?:\\s(?:${Io})\\shue)?`,fu=`(${_o})(?:\\s(${Io})\\shue)?`,Uo="(?:ok)?lab",pu="(?:ok)?lch",du="srgb(?:-linear)?",hs=`(?:a98|prophoto)-rgb|display-p3|rec2020|${du}`,fs="xyz(?:-d(?:50|65))?",zo=`${Uo}|${hs}|${fs}`,Na=`${Ho}|${zo}`,pe="color(",Ea="light-dark(",vn="color-mix(",Wr=`(?:${Lo})\\(\\s*from\\s+`,mu=`(${Lo})\\(\\s*from\\s+`,ps="var(",Go=`(?:${hs}|${fs})(?:\\s+${Or}){3}${ya}`,gu="^light-dark\\(",jo=`^${Wr}|(?<=[\\s])${Wr}`,ds=`${To}(?:\\s+${Or}){2}${ya}`,qo=`${hu}(?:\\s*,\\s*${Ge}){2}${Wo}`,ms=`(?:${Or}\\s+){2}${To}${ya}`,Ca=`${Or}(?:\\s+${Or}){2}${ya}`,Vo=`(?:${ze}(?:\\s*,\\s*${ze}){2}|${Ge}(?:\\s*,\\s*${Ge}){2})${Wo}`,Jn=`${uu}|hsla?\\(\\s*${qo}\\s*\\)|rgba?\\(\\s*${Vo}\\s*\\)|(?:hsla?|hwb)\\(\\s*${ds}\\s*\\)|(?:(?:ok)?lab|rgba?)\\(\\s*${Ca}\\s*\\)|(?:ok)?lch\\(\\s*${ms}\\s*\\)|color\\(\\s*${Go}\\s*\\)`,Qn=`(?:${Jn})(?:\\s+${Ge})?`,xa=`color-mix\\(\\s*in\\s+(?:${Na})\\s*,\\s*${Qn}\\s*,\\s*${Qn}\\s*\\)`,vu=`color-mix\\(\\s*in\\s+(${Na})\\s*,\\s*(${Qn})\\s*,\\s*(${Qn})\\s*\\)`,oe="computedValue",Z="mixValue",Q="specifiedValue",gs="color",vs=.001,Lr=.5,bs=2,ve=3,zt=4,Gt=8,_r=10,Fa=12,L=16,bu=60,er=180,vt=360,q=100,T=255,Sn=2,Ir=3,tr=2.4,nr=12.92,bt=.055,Tr=116,Xo=500,Ko=200,ws=216/24389,ka=24389/27,Yo=[.3457/.3585,1,(1-.3457-.3585)/.3585],Hr=[[.955473421488075,-.02309845494876471,.06325924320057072],[-.0283697093338637,1.0099953980813041,.021041441191917323],[.012314014864481998,-.020507649298898964,1.330365926242124]],Et=[[1.0479297925449969,.022946870601609652,-.05019226628920524],[.02962780877005599,.9904344267538799,-.017073799063418826],[-.009243040646204504,.015055191490298152,.7518742814281371]],$s=[[506752/1228815,87881/245763,12673/70218],[87098/409605,175762/245763,12673/175545],[7918/409605,87881/737289,1001167/1053270]],Sa=[[12831/3959,-329/214,-1974/3959],[-851781/878810,1648619/878810,36519/878810],[705/12673,-2585/12673,705/667]],wu=[[.819022437996703,.3619062600528904,-.1288737815209879],[.0329836539323885,.9292868615863434,.0361446663506424],[.0481771893596242,.2642395317527308,.6335478284694309]],Zo=[[1.2268798758459243,-.5578149944602171,.2813910456659647],[-.0405757452148008,1.112286803280317,-.0717110580655164],[-.0763729366746601,-.4214933324022432,1.5869240198367816]],Jo=[[1,.3963377773761749,.2158037573099136],[1,-.1055613458156586,-.0638541728258133],[1,-.0894841775298119,-1.2914855480194092]],$u=[[.210454268309314,.7936177747023054,-.0040720430116193],[1.9779985324311684,-2.42859224204858,.450593709617411],[.0259040424655478,.7827717124575296,-.8086757549230774]],yu=[[608311/1250200,189793/714400,198249/1000160],[35783/156275,247089/357200,198249/2500400],[0/1,32229/714400,5220557/5000800]],Nu=[[63426534/99577255,20160776/139408157,47086771/278816314],[26158966/99577255,472592308/697040785,8267143/139408157],[0/1,19567812/697040785,295819943/278816314]],Eu=[[573536/994567,263643/1420810,187206/994567],[591459/1989134,6239551/9945670,374412/4972835],[53769/1989134,351524/4972835,4929758/4972835]],Cu=[[.7977666449006423,.13518129740053308,.0313477341283922],[.2880748288194013,.711835234241873,8993693872564e-17],[0,0,.8251046025104602]],Qo=new RegExp(`^(?:${Jn})$`),rr=new RegExp(`^${fu}$`),xu=/^xyz(?:-d(?:50|65))?$/,ye=/^currentColor$/i,ar=new RegExp(`^color\\(\\s*(${Go})\\s*\\)$`),ys=new RegExp(`^hsla?\\(\\s*(${ds}|${qo})\\s*\\)$`),Ns=new RegExp(`^hwb\\(\\s*(${ds})\\s*\\)$`),Es=new RegExp(`^lab\\(\\s*(${Ca})\\s*\\)$`),Cs=new RegExp(`^lch\\(\\s*(${ms})\\s*\\)$`),ei=new RegExp(`^${xa}$`),Fu=new RegExp(`^${vu}$`),ti=new RegExp(`${xa}`,"g"),xs=new RegExp(`^oklab\\(\\s*(${Ca})\\s*\\)$`),Fs=new RegExp(`^oklch\\(\\s*(${ms})\\s*\\)$`),Je=/^(?:specifi|comput)edValue$/,sr={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},Ct=(e,t,n=!1)=>{if(t===Q)return x(e,""),"";if(n)return x(e,null),new A;const r=["rgb",0,0,0,0];return x(e,r),r},xt=(e,t=!1)=>{switch(e){case"hsl":case"hwb":case Z:return new A;case Q:return"";default:return t?new A:["rgb",0,0,0,0]}},wt=(e,t={})=>{if(!Array.isArray(e))throw new TypeError(`${e} is not an array.`);const{alpha:n=!1,minLength:r=ve,maxLength:a=zt,minRange:s=0,maxRange:o=1,validateRange:i=!0}=t;if(!Number.isFinite(r))throw new TypeError(`${r} is not a number.`);if(!Number.isFinite(a))throw new TypeError(`${a} is not a number.`);if(!Number.isFinite(s))throw new TypeError(`${s} is not a number.`);if(!Number.isFinite(o))throw new TypeError(`${o} is not a number.`);const c=e.length;if(c<r||c>a)throw new Error(`Unexpected array length ${c}.`);let l=0;for(;l<c;){const u=e[l];if(Number.isFinite(u)){if(l<ve&&i&&(u<s||u>o))throw new RangeError(`${u} is not between ${s} and ${o}.`);if(l===ve&&(u<0||u>1))throw new RangeError(`${u} is not between 0 and 1.`)}else throw new TypeError(`${u} is not a number.`);l++}return n&&c===ve&&e.push(1),e},te=(e,t,n=!1)=>{if(Array.isArray(e)){if(e.length!==ve)throw new Error(`Unexpected array length ${e.length}.`);if(!n)for(let N of e)N=wt(N,{maxLength:ve,validateRange:!1})}else throw new TypeError(`${e} is not an array.`);const[[r,a,s],[o,i,c],[l,u,f]]=e;let d,b,p;n?[d,b,p]=t:[d,b,p]=wt(t,{maxLength:ve,validateRange:!1});const m=r*d+a*b+s*p,y=o*d+i*b+c*p,$=l*d+u*b+f*p;return[m,y,$]},Ur=(e,t,n=!1)=>{if(Array.isArray(e)){if(e.length!==zt)throw new Error(`Unexpected array length ${e.length}.`)}else throw new TypeError(`${e} is not an array.`);if(Array.isArray(t)){if(t.length!==zt)throw new Error(`Unexpected array length ${t.length}.`)}else throw new TypeError(`${t} is not an array.`);let r=0;for(;r<zt;)e[r]===g&&t[r]===g?(e[r]=0,t[r]=0):e[r]===g?e[r]=t[r]:t[r]===g&&(t[r]=e[r]),r++;if(n)return[e,t];const a=wt(e,{minLength:zt,validateRange:!1}),s=wt(t,{minLength:zt,validateRange:!1});return[a,s]},zr=e=>{if(Number.isFinite(e)){if(e=Math.round(e),e<0||e>T)throw new RangeError(`${e} is not between 0 and ${T}.`)}else throw new TypeError(`${e} is not a number.`);let t=e.toString(L);return t.length===1&&(t=`0${t}`),t},Ma=e=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const t=vt/400,n=vt/(Math.PI*bs),r=new RegExp(`^(${ze})(${Zn})?$`);if(!r.test(e))throw new SyntaxError(`Invalid property value: ${e}`);const[,a,s]=e.match(r);let o;switch(s){case"grad":o=parseFloat(a)*t;break;case"rad":o=parseFloat(a)*n;break;case"turn":o=parseFloat(a)*vt;break;default:o=parseFloat(a)}return o%=vt,o<0?o+=vt:Object.is(o,-0)&&(o=0),o},en=(e="")=>{if(k(e))if(e=e.trim(),!e)e="1";else if(e===g)e="0";else{let t;if(e.endsWith("%")?t=parseFloat(e)/q:t=parseFloat(e),!Number.isFinite(t))throw new TypeError(`${t} is not a finite number.`);t<vs?e="0":t>1?e="1":e=t.toFixed(ve)}else e="1";return parseFloat(e)},ni=e=>{if(k(e)){if(e==="")throw new SyntaxError("Invalid property value: (empty string)");e=e.trim()}else throw new TypeError(`${e} is not a string.`);let t=parseInt(e,L);if(t<=0)return 0;if(t>=T)return 1;const n=new Map;for(let r=1;r<q;r++)n.set(Math.round(r*T/q),r);return n.has(t)?t=n.get(t)/q:t=Math.round(t/T/vs)*vs,parseFloat(t.toFixed(ve))},ks=(e,t=!1)=>{let n,r,a;t?[n,r,a]=e:[n,r,a]=wt(e,{maxLength:ve,maxRange:T});let s=n/T,o=r/T,i=a/T;const c=.04045;return s>c?s=Math.pow((s+bt)/(1+bt),tr):s/=nr,o>c?o=Math.pow((o+bt)/(1+bt),tr):o/=nr,i>c?i=Math.pow((i+bt)/(1+bt),tr):i/=nr,[s,o,i]},Ss=(e,t=!1)=>(t||(e=wt(e,{maxLength:ve,maxRange:T})),e=ks(e,!0),te($s,e,!0)),ri=(e,t=!1)=>{let[n,r,a]=wt(e,{maxLength:ve});const s=809/258400;return n>s?n=Math.pow(n,1/tr)*(1+bt)-bt:n*=nr,n*=T,r>s?r=Math.pow(r,1/tr)*(1+bt)-bt:r*=nr,r*=T,a>s?a=Math.pow(a,1/tr)*(1+bt)-bt:a*=nr,a*=T,[t?Math.round(n):n,t?Math.round(r):r,t?Math.round(a):a]},or=(e,t=!1)=>{t||(e=wt(e,{maxLength:ve,validateRange:!1}));let[n,r,a]=te(Sa,e,!0);return[n,r,a]=ri([Math.min(Math.max(n,0),1),Math.min(Math.max(r,0),1),Math.min(Math.max(a,0),1)],!0),[n,r,a]},ai=(e,t=!1)=>{const[n,r,a]=or(e,t),s=n/T,o=r/T,i=a/T,c=Math.max(s,o,i),l=Math.min(s,o,i),u=c-l,f=(c+l)*Lr*q;let d,b;if(Math.round(f)===0||Math.round(f)===q)d=0,b=0;else if(b=u/(1-Math.abs(c+l-1))*q,b===0)d=0;else{switch(c){case s:d=(o-i)/u;break;case o:d=(i-s)/u+bs;break;case i:default:d=(s-o)/u+zt;break}d=d*bu%vt,d<0&&(d+=vt)}return[d,b,f]},ku=(e,t=!1)=>{const[n,r,a]=or(e,t),s=Math.min(n,r,a)/T,o=1-Math.max(n,r,a)/T;let i;return s+o===1?i=0:[i]=ai(e),[i,s*q,o*q]},si=(e,t=!1)=>{t||(e=wt(e,{maxLength:ve,validateRange:!1}));const n=te(wu,e,!0).map(i=>Math.cbrt(i));let[r,a,s]=te($u,n,!0);r=Math.min(Math.max(r,0),1);const o=Math.round(parseFloat(r.toFixed(zt))*q);return(o===0||o===q)&&(a=0,s=0),[r,a,s]},Su=(e,t=!1)=>{const[n,r,a]=si(e,t);let s,o;const i=Math.round(parseFloat(n.toFixed(zt))*q);return i===0||i===q?(s=0,o=0):(s=Math.max(Math.sqrt(Math.pow(r,Sn)+Math.pow(a,Sn)),0),parseFloat(s.toFixed(zt))===0?o=0:(o=Math.atan2(a,r)*er/Math.PI,o<0&&(o+=vt))),[n,s,o]},oi=(e,t=!1)=>{t||(e=wt(e,{maxLength:ve,validateRange:!1}));const n=te(Hr,e,!0);return or(n,!0)},ii=(e,t=!1)=>{t||(e=wt(e,{maxLength:ve,validateRange:!1}));const n=e.map((l,u)=>l/Yo[u]),[r,a,s]=n.map(l=>l>ws?Math.cbrt(l):(l*ka+L)/Tr),o=Math.min(Math.max(Tr*a-L,0),q);let i,c;return o===0||o===q?(i=0,c=0):(i=(r-a)*Xo,c=(a-s)*Ko),[o,i,c]},Mu=(e,t=!1)=>{const[n,r,a]=ii(e,t);let s,o;return n===0||n===q?(s=0,o=0):(s=Math.max(Math.sqrt(Math.pow(r,Sn)+Math.pow(a,Sn)),0),o=Math.atan2(a,r)*er/Math.PI,o<0&&(o+=vt)),[n,s,o]},li=e=>{const[t,n,r,a]=wt(e,{alpha:!0,maxRange:T}),s=zr(t),o=zr(n),i=zr(r),c=zr(a*T);let l;return c==="ff"?l=`#${s}${o}${i}`:l=`#${s}${o}${i}${c}`,l},Ms=e=>{if(k(e))e=e.toLowerCase().trim();else throw new TypeError(`${e} is not a string.`);if(!(/^#[\da-f]{6}$/.test(e)||/^#[\da-f]{3}$/.test(e)||/^#[\da-f]{8}$/.test(e)||/^#[\da-f]{4}$/.test(e)))throw new SyntaxError(`Invalid property value: ${e}`);const t=[];if(/^#[\da-f]{3}$/.test(e)){const[,n,r,a]=e.match(/^#([\da-f])([\da-f])([\da-f])$/);t.push(parseInt(`${n}${n}`,L),parseInt(`${r}${r}`,L),parseInt(`${a}${a}`,L),1)}else if(/^#[\da-f]{4}$/.test(e)){const[,n,r,a,s]=e.match(/^#([\da-f])([\da-f])([\da-f])([\da-f])$/);t.push(parseInt(`${n}${n}`,L),parseInt(`${r}${r}`,L),parseInt(`${a}${a}`,L),ni(`${s}${s}`))}else if(/^#[\da-f]{8}$/.test(e)){const[,n,r,a,s]=e.match(/^#([\da-f]{2})([\da-f]{2})([\da-f]{2})([\da-f]{2})$/);t.push(parseInt(n,L),parseInt(r,L),parseInt(a,L),ni(s))}else{const[,n,r,a]=e.match(/^#([\da-f]{2})([\da-f]{2})([\da-f]{2})$/);t.push(parseInt(n,L),parseInt(r,L),parseInt(a,L),1)}return t},Au=e=>{const[t,n,r,a]=Ms(e),[s,o,i]=ks([t,n,r],!0);return[s,o,i,a]},Pu=e=>{const[t,n,r,a]=Au(e),[s,o,i]=te($s,[t,n,r],!0);return[s,o,i,a]},ci=(e,t={})=>{if(k(e))e=e.toLowerCase().trim();else throw new TypeError(`${e} is not a string.`);const{format:n="",nullable:r=!1}=t,a=new RegExp(`^rgba?\\(\\s*(${Ca}|${Vo})\\s*\\)$`);if(!a.test(e)){const p=xt(n,r);return p instanceof A||k(p),p}const[,s]=e.match(a),[o,i,c,l=""]=s.replace(/[,/]/g," ").split(/\s+/);let u,f,d;o===g?u=0:(o.endsWith("%")?u=parseFloat(o)*T/q:u=parseFloat(o),u=Math.min(Math.max(R(u,Gt),0),T)),i===g?f=0:(i.endsWith("%")?f=parseFloat(i)*T/q:f=parseFloat(i),f=Math.min(Math.max(R(f,Gt),0),T)),c===g?d=0:(c.endsWith("%")?d=parseFloat(c)*T/q:d=parseFloat(c),d=Math.min(Math.max(R(d,Gt),0),T));const b=en(l);return["rgb",u,f,d,n===Z&&l===g?g:b]},Aa=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{format:n="",nullable:r=!1}=t;if(!ys.test(e)){const E=xt(n,r);return E instanceof A||k(E),E}const[,a]=e.match(ys),[s,o,i,c=""]=a.replace(/[,/]/g," ").split(/\s+/);let l,u,f;s===g?l=0:l=Ma(s),o===g?u=0:u=Math.min(Math.max(parseFloat(o),0),q),i===g?f=0:f=Math.min(Math.max(parseFloat(i),0),q);const d=en(c);if(n==="hsl")return[n,s===g?s:l,o===g?o:u,i===g?i:f,c===g?c:d];l=l/vt*Fa,f/=q;const b=u/q*Math.min(f,1-f),p=l%Fa,m=(8+l)%Fa,y=(4+l)%Fa,$=f-b*Math.max(-1,Math.min(p-ve,ve**Sn-p,1)),N=f-b*Math.max(-1,Math.min(m-ve,ve**Sn-m,1)),C=f-b*Math.max(-1,Math.min(y-ve,ve**Sn-y,1));return["rgb",Math.min(Math.max(R($*T,Gt),0),T),Math.min(Math.max(R(N*T,Gt),0),T),Math.min(Math.max(R(C*T,Gt),0),T),d]},As=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{format:n="",nullable:r=!1}=t;if(!Ns.test(e)){const $=xt(n,r);return $ instanceof A||k($),$}const[,a]=e.match(Ns),[s,o,i,c=""]=a.replace("/"," ").split(/\s+/);let l,u,f;s===g?l=0:l=Ma(s),o===g?u=0:u=Math.min(Math.max(parseFloat(o),0),q)/q,i===g?f=0:f=Math.min(Math.max(parseFloat(i),0),q)/q;const d=en(c);if(n==="hwb")return[n,s===g?s:l,o===g?o:u*q,i===g?i:f*q,c===g?c:d];if(u+f>=1){const $=R(u/(u+f)*T,Gt);return["rgb",$,$,$,d]}const b=(1-u-f)/T;let[,p,m,y]=Aa(`hsl(${l} 100 50)`);return p=R((p*b+u)*T,Gt),m=R((m*b+u)*T,Gt),y=R((y*b+u)*T,Gt),["rgb",Math.min(Math.max(p,0),T),Math.min(Math.max(m,0),T),Math.min(Math.max(y,0),T),d]},Gr=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{format:n="",nullable:r=!1}=t;if(!Es.test(e)){const O=xt(n,r);return O instanceof A||k(O),O}const a=1.25,s=8,[,o]=e.match(Es),[i,c,l,u=""]=o.replace("/"," ").split(/\s+/);let f,d,b;i===g?f=0:(i.endsWith("%")?(f=parseFloat(i),f>q&&(f=q)):f=parseFloat(i),f<0&&(f=0)),c===g?d=0:d=c.endsWith("%")?parseFloat(c)*a:parseFloat(c),l===g?b=0:b=l.endsWith("%")?parseFloat(l)*a:parseFloat(l);const p=en(u);if(Je.test(n))return["lab",i===g?i:R(f,L),c===g?c:R(d,L),l===g?l:R(b,L),u===g?u:p];const m=(f+L)/Tr,y=d/Xo+m,$=m-b/Ko,N=Math.pow(m,Ir),C=Math.pow(y,Ir),E=Math.pow($,Ir),J=[C>ws?C:(y*Tr-L)/ka,f>s?N:f/ka,E>ws?E:($*Tr-L)/ka],[P,B,M]=J.map((O,z)=>O*Yo[z]);return["xyz-d50",R(P,L),R(B,L),R(M,L),p]},Pa=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{format:n="",nullable:r=!1}=t;if(!Cs.test(e)){const C=xt(n,r);return C instanceof A||k(C),C}const a=1.5,[,s]=e.match(Cs),[o,i,c,l=""]=s.replace("/"," ").split(/\s+/);let u,f,d;o===g?u=0:(u=parseFloat(o),u<0&&(u=0)),i===g?f=0:f=i.endsWith("%")?parseFloat(i)*a:parseFloat(i),c===g?d=0:d=Ma(c);const b=en(l);if(Je.test(n))return["lch",o===g?o:R(u,L),i===g?i:R(f,L),c===g?c:R(d,L),l===g?l:b];const p=f*Math.cos(d*Math.PI/er),m=f*Math.sin(d*Math.PI/er),[,y,$,N]=Gr(`lab(${u} ${p} ${m})`);return["xyz-d50",R(y,L),R($,L),R(N,L),b]},Da=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{format:n="",nullable:r=!1}=t;if(!xs.test(e)){const N=xt(n,r);return N instanceof A||k(N),N}const a=.4,[,s]=e.match(xs),[o,i,c,l=""]=s.replace("/"," ").split(/\s+/);let u,f,d;o===g?u=0:(u=o.endsWith("%")?parseFloat(o)/q:parseFloat(o),u<0&&(u=0)),i===g?f=0:i.endsWith("%")?f=parseFloat(i)*a/q:f=parseFloat(i),c===g?d=0:c.endsWith("%")?d=parseFloat(c)*a/q:d=parseFloat(c);const b=en(l);if(Je.test(n))return["oklab",o===g?o:R(u,L),i===g?i:R(f,L),c===g?c:R(d,L),l===g?l:b];const p=te(Jo,[u,f,d]).map(N=>Math.pow(N,Ir)),[m,y,$]=te(Zo,p,!0);return["xyz-d65",R(m,L),R(y,L),R($,L),b]},Ba=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{format:n="",nullable:r=!1}=t;if(!Fs.test(e)){const E=xt(n,r);return E instanceof A||k(E),E}const a=.4,[,s]=e.match(Fs),[o,i,c,l=""]=s.replace("/"," ").split(/\s+/);let u,f,d;o===g?u=0:(u=o.endsWith("%")?parseFloat(o)/q:parseFloat(o),u<0&&(u=0)),i===g?f=0:(i.endsWith("%")?f=parseFloat(i)*a/q:f=parseFloat(i),f<0&&(f=0)),c===g?d=0:d=Ma(c);const b=en(l);if(Je.test(n))return["oklch",o===g?o:R(u,L),i===g?i:R(f,L),c===g?c:R(d,L),l===g?l:b];const p=f*Math.cos(d*Math.PI/er),m=f*Math.sin(d*Math.PI/er),y=te(Jo,[u,p,m]).map(E=>Math.pow(E,Ir)),[$,N,C]=te(Zo,y,!0);return["xyz-d65",R($,L),R(N,L),R(C,L),b]},$e=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{colorSpace:n="",d50:r=!1,format:a="",nullable:s=!1}=t;if(!ar.test(e)){const C=xt(a,s);return C instanceof A||k(C),C}const[,o]=e.match(ar);let[i,c,l,u,f=""]=o.replace("/"," ").split(/\s+/),d,b,p;i==="xyz"&&(i="xyz-d65"),c===g?d=0:d=c.endsWith("%")?parseFloat(c)/q:parseFloat(c),l===g?b=0:b=l.endsWith("%")?parseFloat(l)/q:parseFloat(l),u===g?p=0:p=u.endsWith("%")?parseFloat(u)/q:parseFloat(u);const m=en(f);if(Je.test(a)||a===Z&&i===n)return[i,c===g?c:R(d,_r),l===g?l:R(b,_r),u===g?u:R(p,_r),f===g?f:m];let y=0,$=0,N=0;if(i==="srgb-linear")[y,$,N]=te($s,[d,b,p]),r&&([y,$,N]=te(Et,[y,$,N],!0));else if(i==="display-p3"){const C=ks([d*T,b*T,p*T]);[y,$,N]=te(yu,C),r&&([y,$,N]=te(Et,[y,$,N],!0))}else if(i==="rec2020"){const C=1.09929682680944,E=.018053968510807,J=.45,P=[d,b,p].map(B=>{let M;return B<E*J*_r?M=B/(J*_r):M=Math.pow((B+C-1)/C,1/J),M});[y,$,N]=te(Nu,P),r&&([y,$,N]=te(Et,[y,$,N],!0))}else if(i==="a98-rgb"){const C=2.19921875,E=[d,b,p].map(J=>Math.pow(J,C));[y,$,N]=te(Eu,E),r&&([y,$,N]=te(Et,[y,$,N],!0))}else if(i==="prophoto-rgb"){const C=[d,b,p].map(E=>{let J;return E>1/(L*bs)?J=Math.pow(E,1.8):J=E/L,J});[y,$,N]=te(Cu,C),r||([y,$,N]=te(Hr,[y,$,N],!0))}else/^xyz(?:-d(?:50|65))?$/.test(i)?([y,$,N]=[d,b,p],i==="xyz-d50"?r||([y,$,N]=te(Hr,[y,$,N])):r&&([y,$,N]=te(Et,[y,$,N],!0))):([y,$,N]=Ss([d*T,b*T,p*T]),r&&([y,$,N]=te(Et,[y,$,N],!0)));return[r?"xyz-d50":"xyz-d65",R(y,L),R($,L),R(N,L),a===Z&&f===g?f:m]},Ce=(e,t={})=>{if(k(e))e=e.toLowerCase().trim();else throw new TypeError(`${e} is not a string.`);const{d50:n=!1,format:r="",nullable:a=!1}=t;if(!Qo.test(e)){const l=xt(r,a);return l instanceof A||k(l),l}let s=0,o=0,i=0,c=0;if(ye.test(e)){if(r===oe)return["rgb",0,0,0,0];if(r===Q)return e}else if(/^[a-z]+$/.test(e))if(Object.hasOwn(sr,e)){if(r===Q)return e;const[l,u,f]=sr[e];if(c=1,r===oe)return["rgb",l,u,f,c];[s,o,i]=Ss([l,u,f],!0),n&&([s,o,i]=te(Et,[s,o,i],!0))}else switch(r){case oe:return a&&e!=="transparent"?new A:["rgb",0,0,0,0];case Q:return e==="transparent"?e:"";case Z:return e==="transparent"?["rgb",0,0,0,0]:new A}else if(e[0]==="#"){if(Je.test(r))return["rgb",...Ms(e)];[s,o,i,c]=Pu(e),n&&([s,o,i]=te(Et,[s,o,i],!0))}else if(e.startsWith("lab")){if(Je.test(r))return Gr(e,t);[,s,o,i,c]=Gr(e),n||([s,o,i]=te(Hr,[s,o,i],!0))}else if(e.startsWith("lch")){if(Je.test(r))return Pa(e,t);[,s,o,i,c]=Pa(e),n||([s,o,i]=te(Hr,[s,o,i],!0))}else if(e.startsWith("oklab")){if(Je.test(r))return Da(e,t);[,s,o,i,c]=Da(e),n&&([s,o,i]=te(Et,[s,o,i],!0))}else if(e.startsWith("oklch")){if(Je.test(r))return Ba(e,t);[,s,o,i,c]=Ba(e),n&&([s,o,i]=te(Et,[s,o,i],!0))}else{let l,u,f;if(e.startsWith("hsl")?[,l,u,f,c]=Aa(e):e.startsWith("hwb")?[,l,u,f,c]=As(e):[,l,u,f,c]=ci(e,t),Je.test(r))return["rgb",Math.round(l),Math.round(u),Math.round(f),c];[s,o,i]=Ss([l,u,f]),n&&([s,o,i]=te(Et,[s,o,i],!0))}return[n?"xyz-d50":"xyz-d65",R(s,L),R(o,L),R(i,L),c]},Mn=(e,t={})=>{if(k(e))e=e.toLowerCase().trim();else throw new TypeError(`${e} is not a string.`);const{colorSpace:n="",format:r="",nullable:a=!1}=t,s=we({namespace:gs,name:"resolveColorValue",value:e},t),o=be(s);if(o instanceof le){if(o.isNull)return o;const b=o.item;return k(b),b}if(!Qo.test(e)){const b=xt(r,a);return b instanceof A?(x(s,null),b):(x(s,b),k(b),b)}let i="",c=0,l=0,u=0,f=0;if(ye.test(e)){if(r===Q)return x(s,e),e}else if(/^[a-z]+$/.test(e))if(Object.hasOwn(sr,e)){if(r===Q)return x(s,e),e;[c,l,u]=sr[e],f=1}else switch(r){case Q:{if(e==="transparent")return x(s,e),e;const b="";return x(s,b),b}case Z:{if(e==="transparent"){const b=["rgb",0,0,0,0];return x(s,b),b}return x(s,null),new A}case oe:default:{if(a&&e!=="transparent")return x(s,null),new A;const b=["rgb",0,0,0,0];return x(s,b),b}}else if(e[0]==="#")[c,l,u,f]=Ms(e);else if(e.startsWith("hsl"))[,c,l,u,f]=Aa(e,t);else if(e.startsWith("hwb"))[,c,l,u,f]=As(e,t);else if(/^l(?:ab|ch)/.test(e)){let b,p,m;if(e.startsWith("lab")?[i,b,p,m,f]=Gr(e,t):[i,b,p,m,f]=Pa(e,t),Je.test(r)){const y=[i,b,p,m,f];return x(s,y),y}[c,l,u]=oi([b,p,m])}else if(/^okl(?:ab|ch)/.test(e)){let b,p,m;if(e.startsWith("oklab")?[i,b,p,m,f]=Da(e,t):[i,b,p,m,f]=Ba(e,t),Je.test(r)){const y=[i,b,p,m,f];return x(s,y),y}[c,l,u]=or([b,p,m])}else[,c,l,u,f]=ci(e,t);if(r===Z&&n==="srgb"){const b=["srgb",c/T,l/T,u/T,f];return x(s,b),b}const d=["rgb",Math.round(c),Math.round(l),Math.round(u),f];return x(s,d),d},tn=(e,t={})=>{if(k(e))e=e.toLowerCase().trim();else throw new TypeError(`${e} is not a string.`);const{colorSpace:n="",format:r="",nullable:a=!1}=t,s=we({namespace:gs,name:"resolveColorFunc",value:e},t),o=be(s);if(o instanceof le){if(o.isNull)return o;const E=o.item;return k(E),E}if(!ar.test(e)){const E=xt(r,a);return E instanceof A?(x(s,null),E):(x(s,E),k(E),E)}const[i,c,l,u,f]=$e(e,t);if(Je.test(r)||r===Z&&i===n){const E=[i,c,l,u,f];return x(s,E),E}const d=parseFloat(`${c}`),b=parseFloat(`${l}`),p=parseFloat(`${u}`),m=en(`${f}`),[y,$,N]=or([d,b,p],!0),C=["rgb",y,$,N,m];return x(s,C),C},Ps=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{colorSpace:n="",format:r=""}=t;let a="",s,o,i,c,l,u,f;if(r===Z){let d;if(e.startsWith(pe)?d=$e(e,t):d=Ce(e,t),d instanceof A)return d;if([a,l,u,f,c]=d,a===n)return[l,u,f,c];[s,o,i]=te(Sa,[l,u,f],!0)}else if(e.startsWith(pe)){const[,d]=e.match(ar),[b]=d.replace("/"," ").split(/\s+/);b==="srgb-linear"?[,s,o,i,c]=tn(e,{format:oe}):([,l,u,f,c]=$e(e),[s,o,i]=te(Sa,[l,u,f],!0))}else[,l,u,f,c]=Ce(e),[s,o,i]=te(Sa,[l,u,f],!0);return[Math.min(Math.max(s,0),1),Math.min(Math.max(o,0),1),Math.min(Math.max(i,0),1),c]},jr=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{format:n=""}=t;let r,a,s,o;if(n===Z){let i;if(e.startsWith(pe)?i=tn(e,t):i=Mn(e,t),i instanceof A)return i;[,r,a,s,o]=i}else if(e.startsWith(pe)){const[,i]=e.match(ar),[c]=i.replace("/"," ").split(/\s+/);c==="srgb"?([,r,a,s,o]=tn(e,{format:oe}),r*=T,a*=T,s*=T):[,r,a,s,o]=tn(e)}else/^(?:ok)?l(?:ab|ch)/.test(e)?([r,a,s,o]=Ps(e),[r,a,s]=ri([r,a,s])):[,r,a,s,o]=Mn(e,{format:oe});return[r,a,s,o]},ui=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{d50:n=!1,format:r=""}=t;let a,s,o,i;if(r===Z){let c;if(e.startsWith(pe)?c=$e(e,t):c=Ce(e,t),c instanceof A)return c;[,a,s,o,i]=c}else if(e.startsWith(pe)){const[,c]=e.match(ar),[l]=c.replace("/"," ").split(/\s+/);n?l==="xyz-d50"?[,a,s,o,i]=tn(e,{format:oe}):[,a,s,o,i]=$e(e,t):/^xyz(?:-d65)?$/.test(l)?[,a,s,o,i]=tn(e,{format:oe}):[,a,s,o,i]=$e(e)}else[,a,s,o,i]=Ce(e,t);return[a,s,o,i]},Ds=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{format:n=""}=t;let r,a,s,o;if(ys.test(e))return[,r,a,s,o]=Aa(e,{format:"hsl"}),n==="hsl"?[Math.round(r),Math.round(a),Math.round(s),o]:[r,a,s,o];let i,c,l;if(n===Z){let u;if(e.startsWith(pe)?u=$e(e,t):u=Ce(e,t),u instanceof A)return u;[,i,c,l,o]=u}else e.startsWith(pe)?[,i,c,l,o]=$e(e):[,i,c,l,o]=Ce(e);return[r,a,s]=ai([i,c,l],!0),n==="hsl"?[Math.round(r),Math.round(a),Math.round(s),o]:[n===Z&&a===0?g:r,a,s,o]},Bs=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{format:n=""}=t;let r,a,s,o;if(Ns.test(e))return[,r,a,s,o]=As(e,{format:"hwb"}),n==="hwb"?[Math.round(r),Math.round(a),Math.round(s),o]:[r,a,s,o];let i,c,l;if(n===Z){let u;if(e.startsWith(pe)?u=$e(e,t):u=Ce(e,t),u instanceof A)return u;[,i,c,l,o]=u}else e.startsWith(pe)?[,i,c,l,o]=$e(e):[,i,c,l,o]=Ce(e);return[r,a,s]=ku([i,c,l],!0),n==="hwb"?[Math.round(r),Math.round(a),Math.round(s),o]:[n===Z&&a+s>=100?g:r,a,s,o]},Rs=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{format:n=""}=t;let r,a,s,o;if(Es.test(e))return[,r,a,s,o]=Gr(e,{format:oe}),[r,a,s,o];let i,c,l;if(n===Z){let u;if(t.d50=!0,e.startsWith(pe)?u=$e(e,t):u=Ce(e,t),u instanceof A)return u;[,i,c,l,o]=u}else e.startsWith(pe)?[,i,c,l,o]=$e(e,{d50:!0}):[,i,c,l,o]=Ce(e,{d50:!0});return[r,a,s]=ii([i,c,l],!0),[r,a,s,o]},Os=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{format:n=""}=t;let r,a,s,o;if(Cs.test(e))return[,r,a,s,o]=Pa(e,{format:oe}),[r,a,s,o];let i,c,l;if(n===Z){let u;if(t.d50=!0,e.startsWith(pe)?u=$e(e,t):u=Ce(e,t),u instanceof A)return u;[,i,c,l,o]=u}else e.startsWith(pe)?[,i,c,l,o]=$e(e,{d50:!0}):[,i,c,l,o]=Ce(e,{d50:!0});return[r,a,s]=Mu([i,c,l],!0),[r,a,n===Z&&a===0?g:s,o]},Ws=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{format:n=""}=t;let r,a,s,o;if(xs.test(e))return[,r,a,s,o]=Da(e,{format:oe}),[r,a,s,o];let i,c,l;if(n===Z){let u;if(e.startsWith(pe)?u=$e(e,t):u=Ce(e,t),u instanceof A)return u;[,i,c,l,o]=u}else e.startsWith(pe)?[,i,c,l,o]=$e(e):[,i,c,l,o]=Ce(e);return[r,a,s]=si([i,c,l],!0),[r,a,s,o]},Ls=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{format:n=""}=t;let r,a,s,o;if(Fs.test(e))return[,r,a,s,o]=Ba(e,{format:oe}),[r,a,s,o];let i,c,l;if(n===Z){let u;if(e.startsWith(pe)?u=$e(e,t):u=Ce(e,t),u instanceof A)return u;[,i,c,l,o]=u}else e.startsWith(pe)?[,i,c,l,o]=$e(e):[,i,c,l,o]=Ce(e);return[r,a,s]=Su([i,c,l],!0),[r,a,n===Z&&a===0?g:s,o]},Ra=(e,t={})=>{if(k(e))e=e.toLowerCase().trim();else throw new TypeError(`${e} is not a string.`);const{format:n="",nullable:r=!1}=t,a=we({namespace:gs,name:"resolveColorMix",value:e},t),s=be(a);if(s instanceof le){if(s.isNull)return s;const P=s.item;return k(P),P}const o=[];let i="",c="",l="",u="",f="",d="",b=!1;if(!ei.test(e))if(e.startsWith(vn)&&ti.test(e)){const P=new RegExp(`^(?:${hs}|${fs})$`),B=e.match(ti);for(const M of B)if(M){let O=Ra(M,{format:n===Q?n:oe});if(Array.isArray(O)){const[z,j,K,ee,ie]=O;if(j===0&&K===0&&ee===0&&ie===0){e="";break}P.test(z)?ie===1?O=`color(${z} ${j} ${K} ${ee})`:O=`color(${z} ${j} ${K} ${ee} / ${ie})`:ie===1?O=`${z}(${j} ${K} ${ee})`:O=`${z}(${j} ${K} ${ee} / ${ie})`}else if(!ei.test(O)){e="";break}o.push(O),e=e.replace(M,O)}if(!e)return Ct(a,n,r)}else if(e.startsWith(vn)&&e.endsWith(")")&&e.includes(Ea)){const P=new RegExp(`in\\s+(${Na})`),B=e.replace(vn,"").replace(/\)$/,""),[M="",O="",z=""]=Bn(B,{delimiter:","}),[j="",K=""]=Bn(O),[ee="",ie=""]=Bn(z),de=xe(j,{format:Q}),Se=xe(ee,{format:Q});if(P.test(M)&&de&&Se)if(n===Q){const[,Fe]=M.match(P);rr.test(Fe)?[,i,c]=Fe.match(rr):i=Fe,l=de,K&&(u=K),f=Se,ie&&(d=ie),e=e.replace(j,de).replace(ee,Se),b=!0}else{const Fe=xe(j,t),et=xe(ee,t);k(Fe)&&k(et)&&(e=e.replace(j,Fe).replace(ee,et))}else return Ct(a,n,r)}else return Ct(a,n,r);if(o.length&&n===Q){const P=new RegExp(`^color-mix\\(\\s*in\\s+(${Na})\\s*,`),[,B]=e.match(P);if(rr.test(B)?[,i,c]=B.match(rr):i=B,o.length===2){let[M,O]=o;M=M.replace(/(?=[()])/g,"\\"),O=O.replace(/(?=[()])/g,"\\");const z=new RegExp(`(${M})(?:\\s+(${Ge}))?`),j=new RegExp(`(${O})(?:\\s+(${Ge}))?`);[,l,u]=e.match(z),[,f,d]=e.match(j)}else{let[M]=o;M=M.replace(/(?=[()])/g,"\\");const O=`${M}(?:\\s+${Ge})?`,z=`(${M})(?:\\s+(${Ge}))?`,j=new RegExp(`^${z}$`),K=new RegExp(`${z}\\s*\\)$`),ee=new RegExp(`^(${Jn})(?:\\s+(${Ge}))?$`);if(K.test(e)){const ie=new RegExp(`(${Qn})\\s*,\\s*(${O})\\s*\\)$`),[,de,Se]=e.match(ie);[,l,u]=de.match(ee),[,f,d]=Se.match(j)}else{const ie=new RegExp(`(${O})\\s*,\\s*(${Qn})\\s*\\)$`),[,de,Se]=e.match(ie);[,l,u]=de.match(j),[,f,d]=Se.match(ee)}}}else if(!b){const[,P,B,M]=e.match(Fu),O=new RegExp(`^(${Jn})(?:\\s+(${Ge}))?$`);[,l,u]=B.match(O),[,f,d]=M.match(O),rr.test(P)?[,i,c]=P.match(rr):i=P}let p,m,y;if(u&&d){const P=parseFloat(u)/q,B=parseFloat(d)/q;if(P<0||P>1||B<0||B>1||P===0&&B===0)return Ct(a,n,r);const M=P+B;p=P/M,m=B/M,y=M<1?M:1}else{if(u){if(p=parseFloat(u)/q,p<0||p>1)return Ct(a,n,r);m=1-p}else if(d){if(m=parseFloat(d)/q,m<0||m>1)return Ct(a,n,r);p=1-m}else p=Lr,m=Lr;y=1}if(i==="xyz"&&(i="xyz-d65"),n===Q){let P="",B="";if(l.startsWith(vn)||l.startsWith(Ea))P=l;else if(l.startsWith(pe)){const[M,O,z,j,K]=$e(l,t);K===1?P=`color(${M} ${O} ${z} ${j})`:P=`color(${M} ${O} ${z} ${j} / ${K})`}else{const M=Ce(l,t);if(Array.isArray(M)){const[O,z,j,K,ee]=M;ee===1?O==="rgb"?P=`${O}(${z}, ${j}, ${K})`:P=`${O}(${z} ${j} ${K})`:O==="rgb"?P=`${O}a(${z}, ${j}, ${K}, ${ee})`:P=`${O}(${z} ${j} ${K} / ${ee})`}else{if(!k(M)||!M)return x(a,""),"";P=M}}if(f.startsWith(vn)||f.startsWith(Ea))B=f;else if(f.startsWith(pe)){const[M,O,z,j,K]=$e(f,t);K===1?B=`color(${M} ${O} ${z} ${j})`:B=`color(${M} ${O} ${z} ${j} / ${K})`}else{const M=Ce(f,t);if(Array.isArray(M)){const[O,z,j,K,ee]=M;ee===1?O==="rgb"?B=`${O}(${z}, ${j}, ${K})`:B=`${O}(${z} ${j} ${K})`:O==="rgb"?B=`${O}a(${z}, ${j}, ${K}, ${ee})`:B=`${O}(${z} ${j} ${K} / ${ee})`}else{if(!k(M)||!M)return x(a,""),"";B=M}}if(u&&d)P+=` ${parseFloat(u)}%`,B+=` ${parseFloat(d)}%`;else if(u){const M=parseFloat(u);M!==q*Lr&&(P+=` ${M}%`)}else if(d){const M=q-parseFloat(d);M!==q*Lr&&(P+=` ${M}%`)}if(c){const M=`color-mix(in ${i} ${c} hue, ${P}, ${B})`;return x(a,M),M}else{const M=`color-mix(in ${i}, ${P}, ${B})`;return x(a,M),M}}let $=0,N=0,C=0,E=0;if(/^srgb(?:-linear)?$/.test(i)){let P,B;if(i==="srgb"?(ye.test(l)?P=[g,g,g,g]:P=jr(l,{colorSpace:i,format:Z}),ye.test(f)?B=[g,g,g,g]:B=jr(f,{colorSpace:i,format:Z})):(ye.test(l)?P=[g,g,g,g]:P=Ps(l,{colorSpace:i,format:Z}),ye.test(f)?B=[g,g,g,g]:B=Ps(f,{colorSpace:i,format:Z})),P instanceof A||B instanceof A)return Ct(a,n,r);const[M,O,z,j]=P,[K,ee,ie,de]=B,Se=M===g&&K===g,Fe=O===g&&ee===g,et=z===g&&ie===g,At=j===g&&de===g,[[tt,Oe,We,Pt],[nt,Le,Be,Xt]]=Ur([M,O,z,j],[K,ee,ie,de],!0),me=Pt*p,ge=Xt*m;if(E=me+ge,E===0?($=tt*p+nt*m,N=Oe*p+Le*m,C=We*p+Be*m):($=(tt*me+nt*ge)/E,N=(Oe*me+Le*ge)/E,C=(We*me+Be*ge)/E,E=parseFloat(E.toFixed(3))),n===oe){const Ne=[i,Se?g:R($,L),Fe?g:R(N,L),et?g:R(C,L),At?g:E*y];return x(a,Ne),Ne}$*=T,N*=T,C*=T}else if(xu.test(i)){let P,B;if(ye.test(l)?P=[g,g,g,g]:P=ui(l,{colorSpace:i,d50:i==="xyz-d50",format:Z}),ye.test(f)?B=[g,g,g,g]:B=ui(f,{colorSpace:i,d50:i==="xyz-d50",format:Z}),P instanceof A||B instanceof A)return Ct(a,n,r);const[M,O,z,j]=P,[K,ee,ie,de]=B,Se=M===g&&K===g,Fe=O===g&&ee===g,et=z===g&&ie===g,At=j===g&&de===g,[[tt,Oe,We,Pt],[nt,Le,Be,Xt]]=Ur([M,O,z,j],[K,ee,ie,de],!0),me=Pt*p,ge=Xt*m;E=me+ge;let Ne,_e,Ie;if(E===0?(Ne=tt*p+nt*m,_e=Oe*p+Le*m,Ie=We*p+Be*m):(Ne=(tt*me+nt*ge)/E,_e=(Oe*me+Le*ge)/E,Ie=(We*me+Be*ge)/E,E=parseFloat(E.toFixed(3))),n===oe){const cn=[i,Se?g:R(Ne,L),Fe?g:R(_e,L),et?g:R(Ie,L),At?g:E*y];return x(a,cn),cn}i==="xyz-d50"?[$,N,C]=oi([Ne,_e,Ie],!0):[$,N,C]=or([Ne,_e,Ie],!0)}else if(/^h(?:sl|wb)$/.test(i)){let P,B;if(i==="hsl"?(ye.test(l)?P=[g,g,g,g]:P=Ds(l,{colorSpace:i,format:Z}),ye.test(f)?B=[g,g,g,g]:B=Ds(f,{colorSpace:i,format:Z})):(ye.test(l)?P=[g,g,g,g]:P=Bs(l,{colorSpace:i,format:Z}),ye.test(f)?B=[g,g,g,g]:B=Bs(f,{colorSpace:i,format:Z})),P instanceof A||B instanceof A)return Ct(a,n,r);const[M,O,z,j]=P,[K,ee,ie,de]=B,Se=j===g&&de===g;let[[Fe,et,At,tt],[Oe,We,Pt,nt]]=Ur([M,O,z,j],[K,ee,ie,de],!0);c&&([Fe,Oe]=Xi(Fe,Oe,c));const Le=tt*p,Be=nt*m;E=Le+Be;const Xt=(Fe*p+Oe*m)%vt;let me,ge;if(E===0?(me=et*p+We*m,ge=At*p+Pt*m):(me=(et*Le+We*Be)/E,ge=(At*Le+Pt*Be)/E,E=parseFloat(E.toFixed(3))),[$,N,C]=jr(`${i}(${Xt} ${me} ${ge})`),n===oe){const Ne=["srgb",R($/T,L),R(N/T,L),R(C/T,L),Se?g:E*y];return x(a,Ne),Ne}}else if(/^(?:ok)?lch$/.test(i)){let P,B;if(i==="lch"?(ye.test(l)?P=[g,g,g,g]:P=Os(l,{colorSpace:i,format:Z}),ye.test(f)?B=[g,g,g,g]:B=Os(f,{colorSpace:i,format:Z})):(ye.test(l)?P=[g,g,g,g]:P=Ls(l,{colorSpace:i,format:Z}),ye.test(f)?B=[g,g,g,g]:B=Ls(f,{colorSpace:i,format:Z})),P instanceof A||B instanceof A)return Ct(a,n,r);const[M,O,z,j]=P,[K,ee,ie,de]=B,Se=M===g&&K===g,Fe=O===g&&ee===g,et=z===g&&ie===g,At=j===g&&de===g;let[[tt,Oe,We,Pt],[nt,Le,Be,Xt]]=Ur([M,O,z,j],[K,ee,ie,de],!0);c&&([We,Be]=Xi(We,Be,c));const me=Pt*p,ge=Xt*m;E=me+ge;const Ne=(We*p+Be*m)%vt;let _e,Ie;if(E===0?(_e=tt*p+nt*m,Ie=Oe*p+Le*m):(_e=(tt*me+nt*ge)/E,Ie=(Oe*me+Le*ge)/E,E=parseFloat(E.toFixed(3))),n===oe){const cn=[i,Se?g:R(_e,L),Fe?g:R(Ie,L),et?g:R(Ne,L),At?g:E*y];return x(a,cn),cn}[,$,N,C]=Mn(`${i}(${_e} ${Ie} ${Ne})`)}else{let P,B;if(i==="lab"?(ye.test(l)?P=[g,g,g,g]:P=Rs(l,{colorSpace:i,format:Z}),ye.test(f)?B=[g,g,g,g]:B=Rs(f,{colorSpace:i,format:Z})):(ye.test(l)?P=[g,g,g,g]:P=Ws(l,{colorSpace:i,format:Z}),ye.test(f)?B=[g,g,g,g]:B=Ws(f,{colorSpace:i,format:Z})),P instanceof A||B instanceof A)return Ct(a,n,r);const[M,O,z,j]=P,[K,ee,ie,de]=B,Se=M===g&&K===g,Fe=O===g&&ee===g,et=z===g&&ie===g,At=j===g&&de===g,[[tt,Oe,We,Pt],[nt,Le,Be,Xt]]=Ur([M,O,z,j],[K,ee,ie,de],!0),me=Pt*p,ge=Xt*m;E=me+ge;let Ne,_e,Ie;if(E===0?(Ne=tt*p+nt*m,_e=Oe*p+Le*m,Ie=We*p+Be*m):(Ne=(tt*me+nt*ge)/E,_e=(Oe*me+Le*ge)/E,Ie=(We*me+Be*ge)/E,E=parseFloat(E.toFixed(3))),n===oe){const cn=[i,Se?g:R(Ne,L),Fe?g:R(_e,L),et?g:R(Ie,L),At?g:E*y];return x(a,cn),cn}[,$,N,C]=Mn(`${i}(${Ne} ${_e} ${Ie})`)}const J=["rgb",Math.round($),Math.round(N),Math.round(C),parseFloat((E*y).toFixed(3))];return x(a,J),J},{CloseParen:hi,Comment:Du,EOF:Bu,Ident:Ru,Whitespace:Ou}=v,Wu="css-var",fi=new RegExp($a),pi=new RegExp(Rr);function di(e,t={}){if(!Array.isArray(e))throw new TypeError(`${e} is not an array.`);const{customProperty:n={}}=t,r=[];for(;e.length;){const o=e.shift();if(!Array.isArray(o))throw new TypeError(`${o} is not an array.`);const[i,c]=o;if(i===hi)break;if(c===ps){const[l,u]=di(e,t);e=l,u&&r.push(u)}else if(i===Ru)if(c.startsWith("--")){let l;Object.hasOwn(n,c)?l=n[c]:typeof n.callback=="function"&&(l=n.callback(c)),l&&r.push(l)}else c&&r.push(c)}let a=!1;if(r.length>1){const o=r[r.length-1];a=wn(o)}let s="";for(let o of r){if(o=o.trim(),pi.test(o)){const i=qr(o,t);k(i)&&(a?wn(i)&&(s=i):s=i)}else fi.test(o)?(o=Jr(o,t),a?wn(o)&&(s=o):s=o):o&&!/^(?:inherit|initial|revert(?:-layer)?|unset)$/.test(o)&&(a?wn(o)&&(s=o):s=o);if(s)break}return[e,s]}function Lu(e,t={}){const n=[];for(;e.length;){const r=e.shift(),[a="",s=""]=r;if(s===ps){const[o,i]=di(e,t);if(!i)return new A;e=o,n.push(i)}else switch(a){case hi:{n.length&&n[n.length-1]===" "?n.splice(-1,1,s):n.push(s);break}case Ou:{if(n.length){const o=n[n.length-1];k(o)&&!o.endsWith("(")&&o!==" "&&n.push(s)}break}default:a!==Du&&a!==Bu&&n.push(s)}}return n}function qr(e,t={}){const{format:n=""}=t;if(k(e)){if(!pi.test(e)||n===Q)return e;e=e.trim()}else throw new TypeError(`${e} is not a string.`);const r=we({namespace:Wu,name:"resolveVar",value:e},t),a=be(r);if(a instanceof le)return a.isNull?a:a.item;const s=Zt({css:e}),o=Lu(s,t);if(Array.isArray(o)){let i=o.join("");return fi.test(i)&&(i=Jr(i,t)),x(r,i),i}else return x(r,null),new A}const _u=(e,t={})=>{const n=qr(e,t);return k(n)?n:""};function Re(e,t){return[e[0]*t[0]+e[1]*t[1]+e[2]*t[2],e[3]*t[0]+e[4]*t[1]+e[5]*t[2],e[6]*t[0]+e[7]*t[1]+e[8]*t[2]]}const Iu=[.955473421488075,-.02309845494876471,.06325924320057072,-.0283697093338637,1.0099953980813041,.021041441191917323,.012314014864481998,-.020507649298898964,1.330365926242124];/**
 * Bradford chromatic adaptation from D50 to D65
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Ft(e){return Re(Iu,e)}const Tu=[1.0479297925449969,.022946870601609652,-.05019226628920524,.02962780877005599,.9904344267538799,-.017073799063418826,-.009243040646204504,.015055191490298152,.7518742814281371];/**
 * Bradford chromatic adaptation from D65 to D50
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_ChromAdapt.html
 */function kt(e){return Re(Tu,e)}/**
 * @param {number} hue - Hue as degrees 0..360
 * @param {number} sat - Saturation as percentage 0..100
 * @param {number} light - Lightness as percentage 0..100
 * @return {number[]} Array of sRGB components; in-gamut colors in range [0..1]
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hslToRgb.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hslToRgb.js
 */function mi(e){let t=e[0]%360;const n=e[1]/100,r=e[2]/100;return t<0&&(t+=360),[_s(0,t,n,r),_s(8,t,n,r),_s(4,t,n,r)]}function _s(e,t,n,r){const a=(e+t/30)%12;return r-n*Math.min(r,1-r)*Math.max(-1,Math.min(a-3,9-a,1))}/**
 * @param {number} hue -  Hue as degrees 0..360
 * @param {number} white -  Whiteness as percentage 0..100
 * @param {number} black -  Blackness as percentage 0..100
 * @return {number[]} Array of RGB components 0..1
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hwbToRgb.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hwbToRgb.js
 */function Hu(e){const t=e[0],n=e[1]/100,r=e[2]/100;if(n+r>=1){const o=n/(n+r);return[o,o,o]}const a=mi([t,100,50]),s=1-n-r;return[a[0]*s+n,a[1]*s+n,a[2]*s+n]}/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Uu(e){const t=e[2]*Math.PI/180;return[e[0],e[1]*Math.cos(t),e[1]*Math.sin(t)]}/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function zu(e){const t=180*Math.atan2(e[2],e[1])/Math.PI;return[e[0],Math.sqrt(Math.pow(e[1],2)+Math.pow(e[2],2)),t>=0?t:t+360]}const ir=[.3457/.3585,1,.2958/.3585];/**
 * Convert Lab to D50-adapted XYZ
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 */function gi(e){const t=903.2962962962963,n=216/24389,r=(e[0]+16)/116,a=e[1]/500+r,s=r-e[2]/200;return[(Math.pow(a,3)>n?Math.pow(a,3):(116*a-16)/t)*ir[0],(e[0]>8?Math.pow((e[0]+16)/116,3):e[0]/t)*ir[1],(Math.pow(s,3)>n?Math.pow(s,3):(116*s-16)/t)*ir[2]]}/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */function vi(e){const t=e[2]*Math.PI/180;return[e[0],e[1]*Math.cos(t),e[1]*Math.sin(t)]}/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */function bi(e){const t=180*Math.atan2(e[2],e[1])/Math.PI;return[e[0],Math.sqrt(e[1]**2+e[2]**2),t>=0?t:t+360]}const Gu=[1.2268798758459243,-.5578149944602171,.2813910456659647,-.0405757452148008,1.112286803280317,-.0717110580655164,-.0763729366746601,-.4214933324022432,1.5869240198367816],ju=[1,.3963377773761749,.2158037573099136,1,-.1055613458156586,-.0638541728258133,1,-.0894841775298119,-1.2914855480194092];/**
 * Given OKLab, convert to XYZ relative to D65
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */function Is(e){const t=Re(ju,e);return Re(Gu,[t[0]**3,t[1]**3,t[2]**3])}/**
 * Assuming XYZ is relative to D50, convert to CIE Lab
 * from CIE standard, which now defines these as a rational fraction
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function wi(e){const t=Ts(e[0]/ir[0]),n=Ts(e[1]/ir[1]);return[116*n-16,500*(t-n),200*(n-Ts(e[2]/ir[2]))]}const qu=216/24389,Vu=24389/27;function Ts(e){return e>qu?Math.cbrt(e):(Vu*e+16)/116}const Xu=[.819022437996703,.3619062600528904,-.1288737815209879,.0329836539323885,.9292868615863434,.0361446663506424,.0481771893596242,.2642395317527308,.6335478284694309],Ku=[.210454268309314,.7936177747023054,-.0040720430116193,1.9779985324311684,-2.42859224204858,.450593709617411,.0259040424655478,.7827717124575296,-.8086757549230774];/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * XYZ <-> LMS matrices recalculated for consistent reference white
 * @see https://github.com/w3c/csswg-drafts/issues/6642#issuecomment-943521484
 */function Hs(e){const t=Re(Xu,e);return Re(Ku,[Math.cbrt(t[0]),Math.cbrt(t[1]),Math.cbrt(t[2])])}const Yu=[30757411/17917100,-6372589/17917100,-4539589/17917100,-.666684351832489,1.616481236634939,467509/29648200,792561/44930125,-1921689/44930125,.942103121235474];/**
 * Convert XYZ to linear-light rec2020
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const Zu=[446124/178915,-333277/357830,-72051/178915,-14852/17905,63121/35810,423/17905,11844/330415,-50337/660830,316169/330415];/**
 * Convert XYZ to linear-light P3
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function $i(e){return Re(Zu,e)}const Ju=[1.3457868816471583,-.25557208737979464,-.05110186497554526,-.5446307051249019,1.5082477428451468,.02052744743642139,0,0,1.2119675456389452];/**
 * Convert D50 XYZ to linear-light prophoto-rgb
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 */const Qu=[1829569/896150,-506331/896150,-308931/896150,-851781/878810,1648619/878810,36519/878810,16779/1248040,-147721/1248040,1266979/1248040];/**
 * Convert XYZ to linear-light a98-rgb
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const eh=[12831/3959,-329/214,-1974/3959,-851781/878810,1648619/878810,36519/878810,705/12673,-2585/12673,705/667];/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Vr(e){return Re(eh,e)}/**
 * Convert an array of linear-light rec2020 RGB  in the range 0.0-1.0
 * to gamma corrected form ITU-R BT.2020-2 p.4
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const yi=1.09929682680944,th=.018053968510807;function Us(e){const t=e<0?-1:1,n=Math.abs(e);return n>th?t*(yi*Math.pow(n,.45)-(yi-1)):4.5*e}/**
 * Convert an array of linear-light sRGB values in the range 0.0-1.0 to gamma corrected form
 * Extended transfer function:
 *  For negative values, linear portion extends on reflection
 *  of axis, then uses reflected pow below that
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://en.wikipedia.org/wiki/SRGB
 */function Xr(e){return[zs(e[0]),zs(e[1]),zs(e[2])]}function zs(e){const t=e<0?-1:1,n=Math.abs(e);return n>.0031308?t*(1.055*Math.pow(n,1/2.4)-.055):12.92*e}/**
 * Convert an array of linear-light display-p3 RGB in the range 0.0-1.0
 * to gamma corrected form
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function nh(e){return Xr(e)}/**
 * Convert an array of linear-light prophoto-rgb in the range 0.0-1.0
 * to gamma corrected form.
 * Transfer curve is gamma 1.8 with a small linear portion.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const rh=1/512;function Gs(e){const t=e<0?-1:1,n=Math.abs(e);return n>=rh?t*Math.pow(n,1/1.8):16*e}/**
 * Convert an array of linear-light a98-rgb in the range 0.0-1.0
 * to gamma corrected form. Negative values are also now accepted
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function js(e){const t=e<0?-1:1,n=Math.abs(e);return t*Math.pow(n,256/563)}/**
 * Convert an array of rec2020 RGB values in the range 0.0 - 1.0
 * to linear light (un-companded) form.
 * ITU-R BT.2020-2 p.4
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const Ni=1.09929682680944,ah=.018053968510807;function qs(e){const t=e<0?-1:1,n=Math.abs(e);return n<4.5*ah?e/4.5:t*Math.pow((n+Ni-1)/Ni,1/.45)}const sh=[63426534/99577255,20160776/139408157,47086771/278816314,26158966/99577255,.677998071518871,8267143/139408157,0,19567812/697040785,1.0609850577107909];/**
 * Convert an array of linear-light rec2020 values to CIE XYZ
 * using  D65 (no chromatic adaptation)
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 *//**
 * Convert an array of of sRGB values where in-gamut values are in the range
 * [0 - 1] to linear light (un-companded) form.
 * Extended transfer function:
 *  For negative values, linear portion is extended on reflection of axis,
 *  then reflected power function is used.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://en.wikipedia.org/wiki/SRGB
 */function Oa(e){return[Vs(e[0]),Vs(e[1]),Vs(e[2])]}function Vs(e){const t=e<0?-1:1,n=Math.abs(e);return n<=.04045?e/12.92:t*Math.pow((n+.055)/1.055,2.4)}/**
 * Convert an array of display-p3 RGB values in the range 0.0 - 1.0
 * to linear light (un-companded) form.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function oh(e){return Oa(e)}const ih=[608311/1250200,189793/714400,198249/1000160,35783/156275,247089/357200,198249/2500400,0,32229/714400,5220557/5000800];/**
 * Convert an array of linear-light display-p3 values to CIE XYZ
 * using D65 (no chromatic adaptation)
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 */function Ei(e){return Re(ih,e)}/**
 * Convert an array of prophoto-rgb values where in-gamut Colors are in the
 * range [0.0 - 1.0] to linear light (un-companded) form. Transfer curve is
 * gamma 1.8 with a small linear portion. Extended transfer function
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const lh=16/512;function Xs(e){const t=e<0?-1:1,n=Math.abs(e);return n<=lh?e/16:t*Math.pow(n,1.8)}const ch=[.7977666449006423,.13518129740053308,.0313477341283922,.2880748288194013,.711835234241873,8993693872564e-17,0,0,.8251046025104602];/**
 * Convert an array of linear-light prophoto-rgb values to CIE D50 XYZ.
 * Matrix cannot be expressed in rational form, but is calculated to 64 bit accuracy.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see see https://github.com/w3c/csswg-drafts/issues/7675
 */function Ks(e){const t=e<0?-1:1,n=Math.abs(e);return t*Math.pow(n,563/256)}const uh=[573536/994567,263643/1420810,187206/994567,591459/1989134,6239551/9945670,374412/4972835,53769/1989134,351524/4972835,4929758/4972835];/**
 * Convert an array of linear-light a98-rgb values to CIE XYZ
 * http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 * has greater numerical precision than section ******* of
 * https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf
 * but the values below were calculated from first principles
 * from the chromaticity coordinates of R G B W
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 * @see https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/matrixmaker.html
 */const hh=[506752/1228815,87881/245763,12673/70218,87098/409605,175762/245763,12673/175545,7918/409605,87881/737289,1001167/1053270];/**
 * Convert an array of linear-light sRGB values to CIE XYZ
 * using sRGB's own white, D65 (no chromatic adaptation)
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Kr(e){return Re(hh,e)}/**
 * Convert an array of gamma-corrected sRGB values in the 0.0 to 1.0 range to HSL.
 *
 * @param {Color} RGB [r, g, b]
 * - Red component 0..1
 * - Green component 0..1
 * - Blue component 0..1
 * @return {number[]} Array of HSL values: Hue as degrees 0..360, Saturation and Lightness as percentages 0..100
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/utilities.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/better-rgbToHsl.js
 */function fh(e){const t=e[0],n=e[1],r=e[2],a=Math.max(t,n,r),s=Math.min(t,n,r),o=(s+a)/2,i=a-s;let c=Number.NaN,l=0;if(Math.round(1e5*i)!==0){const u=Math.round(1e5*o);switch(l=u===0||u===1e5?0:(a-o)/Math.min(o,1-o),a){case t:c=(n-r)/i+(n<r?6:0);break;case n:c=(r-t)/i+2;break;case r:c=(t-n)/i+4}c*=60}return l<0&&(c+=180,l=Math.abs(l)),c>=360&&(c-=360),[c,100*l,100*o]}function ph(e){const t=e[0],n=e[1],r=e[2],a=Math.max(t,n,r),s=Math.min(t,n,r);let o=Number.NaN;const i=a-s;if(i!==0){switch(a){case t:o=(n-r)/i+(n<r?6:0);break;case n:o=(r-t)/i+2;break;case r:o=(t-n)/i+4}o*=60}return o>=360&&(o-=360),o}function dh(e){let t=e;return t=Oa(t),t=Kr(t),t=kt(t),t}function Ys(e){let t=e;return t=Ft(t),t=Vr(t),t=Xr(t),t}function mh(e){let t=e;return t=mi(t),t=Oa(t),t=Kr(t),t=kt(t),t}function gh(e){let t=e;return t=Ft(t),t=Vr(t),t=Xr(t),t=fh(t),t}function vh(e){let t=e;return t=Hu(t),t=Oa(t),t=Kr(t),t=kt(t),t}function bh(e){let t=e;t=Ft(t),t=Vr(t);const n=Xr(t),r=Math.min(n[0],n[1],n[2]),a=1-Math.max(n[0],n[1],n[2]);return[ph(n),100*r,100*a]}function wh(e){let t=e;return t=gi(t),t}function $h(e){let t=e;return t=wi(t),t}function yh(e){let t=e;return t=Uu(t),t=gi(t),t}function Nh(e){let t=e;return t=wi(t),t=zu(t),t}function Eh(e){let t=e;return t=Is(t),t=kt(t),t}function Ch(e){let t=e;return t=Ft(t),t=Hs(t),t}function xh(e){let t=e;return t=vi(t),t=Is(t),t=kt(t),t}function Ci(e){let t=e;return t=Ft(t),t=Hs(t),t=bi(t),t}function Fh(e){let t=e;return t=Kr(t),t=kt(t),t}function kh(e){let t=e;return t=Ft(t),t=Vr(t),t}function Sh(e){let t=e;/**
 * Convert an array of a98-rgb values in the range 0.0 - 1.0
 * to linear light (un-companded) form. Negative values are also now accepted
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */var n;return t=[Ks((n=t)[0]),Ks(n[1]),Ks(n[2])],t=Re(uh,t),t=kt(t),t}function Mh(e){let t=e;var n;return t=Ft(t),t=Re(Qu,t),t=[js((n=t)[0]),js(n[1]),js(n[2])],t}function Ah(e){let t=e;return t=oh(t),t=Ei(t),t=kt(t),t}function Ph(e){let t=e;return t=Ft(t),t=$i(t),t=nh(t),t}function Dh(e){let t=e;return t=Ei(t),t=kt(t),t}function Bh(e){let t=e;return t=Ft(t),t=$i(t),t}function Rh(e){let t=e;var n;return t=[qs((n=t)[0]),qs(n[1]),qs(n[2])],t=Re(sh,t),t=kt(t),t}function Oh(e){let t=e;var n;return t=Ft(t),t=Re(Yu,t),t=[Us((n=t)[0]),Us(n[1]),Us(n[2])],t}function Wh(e){let t=e;var n;return t=[Xs((n=t)[0]),Xs(n[1]),Xs(n[2])],t=Re(ch,t),t}function Lh(e){let t=e;var n;return t=Re(Ju,t),t=[Gs((n=t)[0]),Gs(n[1]),Gs(n[2])],t}function _h(e){let t=e;return t=kt(t),t}function Ih(e){let t=e;return t=Ft(t),t}function Th(e){return e[0]>=-1e-4&&e[0]<=1.0001&&e[1]>=-1e-4&&e[1]<=1.0001&&e[2]>=-1e-4&&e[2]<=1.0001}function xi(e){return[e[0]<0?0:e[0]>1?1:e[0],e[1]<0?0:e[1]>1?1:e[1],e[2]<0?0:e[2]>1?1:e[2]]}/**
 * @license MIT https://github.com/facelessuser/coloraide/blob/main/LICENSE.md
 */function Hh(e,t,n){const r=e[0],a=e[2];let s=t(e);const o=t([r,0,a]);for(let i=0;i<4;i++){if(i>0){const l=n(s);l[0]=r,l[2]=a,s=t(l)}const c=Uh(o,s);if(!c)break;s=c}return xi(s)}function Uh(e,t){let n=1/0,r=-1/0;const a=[0,0,0];for(let s=0;s<3;s++){const o=e[s],i=t[s]-o;a[s]=i;const c=0,l=1;if(i){const u=1/i,f=(c-o)*u,d=(l-o)*u;r=Math.max(Math.min(f,d),r),n=Math.min(Math.max(f,d),n)}else if(o<c||o>l)return!1}return!(r>n||n<0)&&(r<0&&(r=n),!!isFinite(r)&&[e[0]+a[0]*r,e[1]+a[1]*r,e[2]+a[2]*r])}const zh={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]};function Fi(e){const[t,n,r]=e.map(a=>a<=.03928?a/12.92:Math.pow((a+.055)/1.055,2.4));return .2126*t+.7152*n+.0722*r}function ki(e,t){const n=Fi(e),r=Fi(t);return(Math.max(n,r)+.05)/(Math.min(n,r)+.05)}var w,D;function De(e){return[Number.isNaN(e[0])?0:e[0],Number.isNaN(e[1])?0:e[1],Number.isNaN(e[2])?0:e[2]]}function Si(e){switch(e.colorNotation){case w.HEX:case w.RGB:case w.sRGB:return{...e,colorNotation:w.XYZ_D50,channels:dh(De(e.channels))};case w.Linear_sRGB:return{...e,colorNotation:w.XYZ_D50,channels:Fh(De(e.channels))};case w.Display_P3:return{...e,colorNotation:w.XYZ_D50,channels:Ah(De(e.channels))};case w.Linear_Display_P3:return{...e,colorNotation:w.XYZ_D50,channels:Dh(De(e.channels))};case w.Rec2020:return{...e,colorNotation:w.XYZ_D50,channels:Rh(De(e.channels))};case w.A98_RGB:return{...e,colorNotation:w.XYZ_D50,channels:Sh(De(e.channels))};case w.ProPhoto_RGB:return{...e,colorNotation:w.XYZ_D50,channels:Wh(De(e.channels))};case w.HSL:return{...e,colorNotation:w.XYZ_D50,channels:mh(De(e.channels))};case w.HWB:return{...e,colorNotation:w.XYZ_D50,channels:vh(De(e.channels))};case w.Lab:return{...e,colorNotation:w.XYZ_D50,channels:wh(De(e.channels))};case w.OKLab:return{...e,colorNotation:w.XYZ_D50,channels:Eh(De(e.channels))};case w.LCH:return{...e,colorNotation:w.XYZ_D50,channels:yh(De(e.channels))};case w.OKLCH:return{...e,colorNotation:w.XYZ_D50,channels:xh(De(e.channels))};case w.XYZ_D50:return{...e,colorNotation:w.XYZ_D50,channels:De(e.channels)};case w.XYZ_D65:return{...e,colorNotation:w.XYZ_D50,channels:_h(De(e.channels))};default:throw new Error("Unsupported color notation")}}(function(e){e.A98_RGB="a98-rgb",e.Display_P3="display-p3",e.Linear_Display_P3="display-p3-linear",e.HEX="hex",e.HSL="hsl",e.HWB="hwb",e.LCH="lch",e.Lab="lab",e.Linear_sRGB="srgb-linear",e.OKLCH="oklch",e.OKLab="oklab",e.ProPhoto_RGB="prophoto-rgb",e.RGB="rgb",e.sRGB="srgb",e.Rec2020="rec2020",e.XYZ_D50="xyz-d50",e.XYZ_D65="xyz-d65"})(w||(w={})),(function(e){e.ColorKeyword="color-keyword",e.HasAlpha="has-alpha",e.HasDimensionValues="has-dimension-values",e.HasNoneKeywords="has-none-keywords",e.HasNumberValues="has-number-values",e.HasPercentageAlpha="has-percentage-alpha",e.HasPercentageValues="has-percentage-values",e.HasVariableAlpha="has-variable-alpha",e.Hex="hex",e.LegacyHSL="legacy-hsl",e.LegacyRGB="legacy-rgb",e.NamedColor="named-color",e.RelativeColorSyntax="relative-color-syntax",e.ColorMix="color-mix",e.ColorMixVariadic="color-mix-variadic",e.ContrastColor="contrast-color",e.RelativeAlphaSyntax="relative-alpha-syntax",e.Experimental="experimental"})(D||(D={}));const Mi=new Set([w.A98_RGB,w.Display_P3,w.Linear_Display_P3,w.HEX,w.Linear_sRGB,w.ProPhoto_RGB,w.RGB,w.sRGB,w.Rec2020,w.XYZ_D50,w.XYZ_D65]);function bn(e,t){const n={...e};if(e.colorNotation!==t){const r=Si(n);switch(t){case w.HEX:case w.RGB:n.colorNotation=w.RGB,n.channels=Ys(r.channels);break;case w.sRGB:n.colorNotation=w.sRGB,n.channels=Ys(r.channels);break;case w.Linear_sRGB:n.colorNotation=w.Linear_sRGB,n.channels=kh(r.channels);break;case w.Display_P3:n.colorNotation=w.Display_P3,n.channels=Ph(r.channels);break;case w.Linear_Display_P3:n.colorNotation=w.Linear_Display_P3,n.channels=Bh(r.channels);break;case w.Rec2020:n.colorNotation=w.Rec2020,n.channels=Oh(r.channels);break;case w.ProPhoto_RGB:n.colorNotation=w.ProPhoto_RGB,n.channels=Lh(r.channels);break;case w.A98_RGB:n.colorNotation=w.A98_RGB,n.channels=Mh(r.channels);break;case w.HSL:n.colorNotation=w.HSL,n.channels=gh(r.channels);break;case w.HWB:n.colorNotation=w.HWB,n.channels=bh(r.channels);break;case w.Lab:n.colorNotation=w.Lab,n.channels=$h(r.channels);break;case w.LCH:n.colorNotation=w.LCH,n.channels=Nh(r.channels);break;case w.OKLCH:n.colorNotation=w.OKLCH,n.channels=Ci(r.channels);break;case w.OKLab:n.colorNotation=w.OKLab,n.channels=Ch(r.channels);break;case w.XYZ_D50:n.colorNotation=w.XYZ_D50,n.channels=r.channels;break;case w.XYZ_D65:n.colorNotation=w.XYZ_D65,n.channels=Ih(r.channels);break;default:throw new Error("Unsupported color notation")}}else n.channels=De(e.channels);if(t===e.colorNotation)n.channels=Qe(e.channels,[0,1,2],n.channels,[0,1,2]);else if(Mi.has(t)&&Mi.has(e.colorNotation))n.channels=Qe(e.channels,[0,1,2],n.channels,[0,1,2]);else switch(t){case w.HSL:switch(e.colorNotation){case w.HWB:n.channels=Qe(e.channels,[0],n.channels,[0]);break;case w.Lab:case w.OKLab:n.channels=Qe(e.channels,[2],n.channels,[0]);break;case w.LCH:case w.OKLCH:n.channels=Qe(e.channels,[0,1,2],n.channels,[2,1,0])}break;case w.HWB:switch(e.colorNotation){case w.HSL:n.channels=Qe(e.channels,[0],n.channels,[0]);break;case w.LCH:case w.OKLCH:n.channels=Qe(e.channels,[0],n.channels,[2])}break;case w.Lab:case w.OKLab:switch(e.colorNotation){case w.HSL:n.channels=Qe(e.channels,[0],n.channels,[2]);break;case w.Lab:case w.OKLab:n.channels=Qe(e.channels,[0,1,2],n.channels,[0,1,2]);break;case w.LCH:case w.OKLCH:n.channels=Qe(e.channels,[0],n.channels,[0])}break;case w.LCH:case w.OKLCH:switch(e.colorNotation){case w.HSL:n.channels=Qe(e.channels,[0,1,2],n.channels,[2,1,0]);break;case w.HWB:n.channels=Qe(e.channels,[0],n.channels,[2]);break;case w.Lab:case w.OKLab:n.channels=Qe(e.channels,[0],n.channels,[0]);break;case w.LCH:case w.OKLCH:n.channels=Qe(e.channels,[0,1,2],n.channels,[0,1,2])}}return n.channels=Gh(n.channels,t),n}function Gh(e,t){const n=[...e];switch(t){case w.HSL:!Number.isNaN(n[1])&&Yr(n[1],4)<=0&&(n[0]=Number.NaN);break;case w.HWB:Math.max(0,Yr(n[1],4))+Math.max(0,Yr(n[2],4))>=100&&(n[0]=Number.NaN);break;case w.LCH:!Number.isNaN(n[1])&&Yr(n[1],4)<=0&&(n[2]=Number.NaN);break;case w.OKLCH:!Number.isNaN(n[1])&&Yr(n[1],6)<=0&&(n[2]=Number.NaN)}return n}function Qe(e,t,n,r){const a=[...n];for(const s of t)Number.isNaN(e[t[s]])&&(a[r[s]]=Number.NaN);return a}function Zs(e){const t=new Map;switch(e.colorNotation){case w.RGB:case w.HEX:t.set("r",re(255*e.channels[0])),t.set("g",re(255*e.channels[1])),t.set("b",re(255*e.channels[2])),typeof e.alpha=="number"&&t.set("alpha",re(e.alpha));break;case w.HSL:t.set("h",re(e.channels[0])),t.set("s",re(e.channels[1])),t.set("l",re(e.channels[2])),typeof e.alpha=="number"&&t.set("alpha",re(e.alpha));break;case w.HWB:t.set("h",re(e.channels[0])),t.set("w",re(e.channels[1])),t.set("b",re(e.channels[2])),typeof e.alpha=="number"&&t.set("alpha",re(e.alpha));break;case w.Lab:case w.OKLab:t.set("l",re(e.channels[0])),t.set("a",re(e.channels[1])),t.set("b",re(e.channels[2])),typeof e.alpha=="number"&&t.set("alpha",re(e.alpha));break;case w.LCH:case w.OKLCH:t.set("l",re(e.channels[0])),t.set("c",re(e.channels[1])),t.set("h",re(e.channels[2])),typeof e.alpha=="number"&&t.set("alpha",re(e.alpha));break;case w.sRGB:case w.A98_RGB:case w.Display_P3:case w.Linear_Display_P3:case w.Rec2020:case w.Linear_sRGB:case w.ProPhoto_RGB:t.set("r",re(e.channels[0])),t.set("g",re(e.channels[1])),t.set("b",re(e.channels[2])),typeof e.alpha=="number"&&t.set("alpha",re(e.alpha));break;case w.XYZ_D50:case w.XYZ_D65:t.set("x",re(e.channels[0])),t.set("y",re(e.channels[1])),t.set("z",re(e.channels[2])),typeof e.alpha=="number"&&t.set("alpha",re(e.alpha))}return t}function Js(e){const t=new Map(e);for(const[n,r]of e)Number.isNaN(r[4].value)&&t.set(n,re(0));return t}function re(e){return Number.isNaN(e)?[v.Number,"none",-1,-1,{value:Number.NaN,type:S.Number}]:[v.Number,e.toString(),-1,-1,{value:e,type:S.Number}]}function Yr(e,t=7){if(Number.isNaN(e))return 0;const n=Math.pow(10,t);return Math.round(e*n)/n}function V(e,t,n,r){return Math.min(Math.max(e/t,n),r)}const jh=/[A-Z]/g;function ae(e){return e.replace(jh,t=>String.fromCharCode(t.charCodeAt(0)+32))}function lr(e,t,n){if(ue(e)&&ae(e[4].value)==="none")return n.syntaxFlags.add(D.HasNoneKeywords),[v.Number,"none",e[2],e[3],{value:Number.NaN,type:S.Number}];if(ne(e)){t!==3&&n.syntaxFlags.add(D.HasPercentageValues);let r=V(e[4].value,100,-2147483647,2147483647);return t===3&&(r=V(e[4].value,100,0,1)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}if(H(e)){t!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=V(e[4].value,1,-2147483647,2147483647);return t===3&&(r=V(e[4].value,1,0,1)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}return!1}const qh=new Set(["srgb","srgb-linear","display-p3","display-p3-linear","a98-rgb","prophoto-rgb","rec2020","xyz","xyz-d50","xyz-d65"]);function Vh(e,t){const n=[],r=[],a=[],s=[];let o,i,c=!1,l=!1;const u={colorNotation:w.sRGB,channels:[0,0,0],alpha:1,syntaxFlags:new Set([])};let f=n;for(let y=0;y<e.value.length;y++){let $=e.value[y];if(rt($)||at($))for(;rt(e.value[y+1])||at(e.value[y+1]);)y++;else if(f===n&&n.length&&(f=r),f===r&&r.length&&(f=a),I($)&&Nr($.value)&&$.value[4].value==="/"){if(f===s)return!1;f=s}else{if(Ve($)){if(f===s&&ae($.getName())==="var"){u.syntaxFlags.add(D.HasVariableAlpha),f.push($);continue}if(!Fr.has(ae($.getName())))return!1;const[[N]]=qn([[$]],{censorIntoStandardRepresentableValues:!0,globals:i,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!N||!I(N)||!fe(N.value))return!1;Number.isNaN(N.value[4].value)&&(N.value[4].value=0),$=N}if(f===n&&n.length===0&&I($)&&ue($.value)&&qh.has(ae($.value[4].value))){if(c)return!1;c=ae($.value[4].value),u.colorNotation=Xh(c),l&&(l.colorNotation!==u.colorNotation&&(l=bn(l,u.colorNotation)),o=Zs(l),i=Js(o))}else if(f===n&&n.length===0&&I($)&&ue($.value)&&ae($.value[4].value)==="from"){if(l||c)return!1;for(;rt(e.value[y+1])||at(e.value[y+1]);)y++;if(y++,$=e.value[y],l=t($),l===!1)return!1;l.syntaxFlags.has(D.Experimental)&&u.syntaxFlags.add(D.Experimental),u.syntaxFlags.add(D.RelativeColorSyntax)}else{if(!I($))return!1;if(ue($.value)&&o&&o.has(ae($.value[4].value))){f.push(new G(o.get(ae($.value[4].value))));continue}f.push($)}}}if(!c||f.length!==1||n.length!==1||r.length!==1||a.length!==1||!I(n[0])||!I(r[0])||!I(a[0])||o&&!o.has("alpha"))return!1;const d=lr(n[0].value,0,u);if(!d||!H(d))return!1;const b=lr(r[0].value,1,u);if(!b||!H(b))return!1;const p=lr(a[0].value,2,u);if(!p||!H(p))return!1;const m=[d,b,p];if(s.length===1)if(u.syntaxFlags.add(D.HasAlpha),I(s[0])){const y=lr(s[0].value,3,u);if(!y||!H(y))return!1;m.push(y)}else u.alpha=s[0];else if(o&&o.has("alpha")){const y=lr(o.get("alpha"),3,u);if(!y||!H(y))return!1;m.push(y)}return u.channels=[m[0][4].value,m[1][4].value,m[2][4].value],m.length===4&&(u.alpha=m[3][4].value),u}function Xh(e){switch(e){case"srgb":return w.sRGB;case"srgb-linear":return w.Linear_sRGB;case"display-p3":return w.Display_P3;case"display-p3-linear":return w.Linear_Display_P3;case"a98-rgb":return w.A98_RGB;case"prophoto-rgb":return w.ProPhoto_RGB;case"rec2020":return w.Rec2020;case"xyz":case"xyz-d65":return w.XYZ_D65;case"xyz-d50":return w.XYZ_D50;default:throw new Error("Unknown color space name: "+e)}}const Kh=new Set(["srgb","srgb-linear","display-p3","display-p3-linear","a98-rgb","prophoto-rgb","rec2020","lab","oklab","xyz","xyz-d50","xyz-d65"]),Qs=new Set(["hsl","hwb","lch","oklch"]),Yh=new Set(["shorter","longer","increasing","decreasing"]);function Zh(e,t){let n=null,r=null,a=null,s=!1;for(let o=0;o<e.value.length;o++){const i=e.value[o];if(!Jt(i)){if(!(n||I(i)&&ue(i.value)&&ae(i.value[4].value)==="in"))return Ai("oklab",Wa(e.value,t));if(I(i)&&ue(i.value)){if(!n&&ae(i.value[4].value)==="in"){n=i;continue}if(n&&!r){r=ae(i.value[4].value);continue}if(n&&r&&!a&&Qs.has(r)){a=ae(i.value[4].value);continue}if(n&&r&&a&&!s&&ae(i.value[4].value)==="hue"){s=!0;continue}return!1}return!(!I(i)||!Nt(i.value))&&!!r&&(a||s?!!(r&&a&&s&&Qs.has(r)&&Yh.has(a))&&Pi(r,a,Wa(e.value.slice(o+1),t)):Kh.has(r)?Ai(r,Wa(e.value.slice(o+1),t)):!!Qs.has(r)&&Pi(r,"shorter",Wa(e.value.slice(o+1),t)))}}return!1}function Wa(e,t){const n=[];let r=1,a=!1,s=!1;for(let l=0;l<e.length;l++){let u=e[l];if(!Jt(u)){if(!I(u)||!Nt(u.value)){if(!a){const f=t(u);if(f){a=f;continue}}if(!s){if(Ve(u)&&Fr.has(ae(u.getName()))){if([[u]]=qn([[u]],{censorIntoStandardRepresentableValues:!0,precision:-1,toCanonicalUnits:!0,rawPercentages:!0}),!u||!I(u)||!fe(u.value))return!1;Number.isNaN(u.value[4].value)&&(u.value[4].value=0)}if(I(u)&&ne(u.value)&&u.value[4].value>=0){s=u.value[4].value;continue}}return!1}if(!a)return!1;n.push({color:a,percentage:s}),a=!1,s=!1}}if(!a)return!1;n.push({color:a,percentage:s});let o=0,i=0;for(let l=0;l<n.length;l++){const u=n[l].percentage;if(u!==!1){if(u<0||u>100)return!1;o+=u}else i++}const c=Math.max(0,100-o);o=0;for(let l=0;l<n.length;l++)n[l].percentage===!1&&(n[l].percentage=c/i),o+=n[l].percentage;if(o===0)return{colors:[{color:{channels:[0,0,0],colorNotation:w.sRGB,alpha:0,syntaxFlags:new Set},percentage:0}],alphaMultiplier:0};if(o>100)for(let l=0;l<n.length;l++){let u=n[l].percentage;u=u/o*100,n[l].percentage=u}if(o<100){r=o/100;for(let l=0;l<n.length;l++){let u=n[l].percentage;u=u/o*100,n[l].percentage=u}}return{colors:n,alphaMultiplier:r}}function Ai(e,t){var n;if(!t||!t.colors.length)return!1;const r=t.colors.slice();r.reverse();let a=w.RGB;switch(e){case"srgb":a=w.RGB;break;case"srgb-linear":a=w.Linear_sRGB;break;case"display-p3":a=w.Display_P3;break;case"display-p3-linear":a=w.Linear_Display_P3;break;case"a98-rgb":a=w.A98_RGB;break;case"prophoto-rgb":a=w.ProPhoto_RGB;break;case"rec2020":a=w.Rec2020;break;case"lab":a=w.Lab;break;case"oklab":a=w.OKLab;break;case"xyz-d50":a=w.XYZ_D50;break;case"xyz":case"xyz-d65":a=w.XYZ_D65;break;default:return!1}if(r.length===1){const o=bn(r[0].color,a);return o.colorNotation=a,o.syntaxFlags.add(D.ColorMixVariadic),typeof o.alpha!="number"?!1:(o.alpha=o.alpha*t.alphaMultiplier,o)}for(;r.length>=2;){const o=r.pop(),i=r.pop();if(!o||!i)return!1;const c=Jh(a,o.color,o.percentage,i.color,i.percentage);if(!c)return!1;r.push({color:c,percentage:o.percentage+i.percentage})}const s=(n=r[0])==null?void 0:n.color;return!!s&&(t.colors.some(o=>o.color.syntaxFlags.has(D.Experimental))&&s.syntaxFlags.add(D.Experimental),typeof s.alpha=="number"&&(s.alpha=s.alpha*t.alphaMultiplier,t.colors.length!==2&&s.syntaxFlags.add(D.ColorMixVariadic),s))}function Jh(e,t,n,r,a){const s=n/(n+a);let o=t.alpha;if(typeof o!="number")return!1;let i=r.alpha;if(typeof i!="number")return!1;o=Number.isNaN(o)?i:o,i=Number.isNaN(i)?o:i;const c=bn(t,e).channels,l=bn(r,e).channels;c[0]=$t(c[0],l[0]),l[0]=$t(l[0],c[0]),c[1]=$t(c[1],l[1]),l[1]=$t(l[1],c[1]),c[2]=$t(c[2],l[2]),l[2]=$t(l[2],c[2]),c[0]=jt(c[0],o),c[1]=jt(c[1],o),c[2]=jt(c[2],o),l[0]=jt(l[0],i),l[1]=jt(l[1],i),l[2]=jt(l[2],i);const u=St(o,i,s);return{colorNotation:e,channels:[An(St(c[0],l[0],s),u),An(St(c[1],l[1],s),u),An(St(c[2],l[2],s),u)],alpha:u,syntaxFlags:new Set([D.ColorMix])}}function Pi(e,t,n){var r;if(!n||!n.colors.length)return!1;const a=n.colors.slice();a.reverse();let s=w.HSL;switch(e){case"hsl":s=w.HSL;break;case"hwb":s=w.HWB;break;case"lch":s=w.LCH;break;case"oklch":s=w.OKLCH;break;default:return!1}if(a.length===1){const i=bn(a[0].color,s);return i.colorNotation=s,i.syntaxFlags.add(D.ColorMixVariadic),typeof i.alpha!="number"?!1:(i.alpha=i.alpha*n.alphaMultiplier,i)}for(;a.length>=2;){const i=a.pop(),c=a.pop();if(!i||!c)return!1;const l=Qh(s,t,i.color,i.percentage,c.color,c.percentage);if(!l)return!1;a.push({color:l,percentage:i.percentage+c.percentage})}const o=(r=a[0])==null?void 0:r.color;return!!o&&(n.colors.some(i=>i.color.syntaxFlags.has(D.Experimental))&&o.syntaxFlags.add(D.Experimental),typeof o.alpha=="number"&&(o.alpha=o.alpha*n.alphaMultiplier,n.colors.length!==2&&o.syntaxFlags.add(D.ColorMixVariadic),o))}function Qh(e,t,n,r,a,s){const o=r/(r+s);let i=0,c=0,l=0,u=0,f=0,d=0,b=n.alpha;if(typeof b!="number")return!1;let p=a.alpha;if(typeof p!="number")return!1;b=Number.isNaN(b)?p:b,p=Number.isNaN(p)?b:p;const m=bn(n,e).channels,y=bn(a,e).channels;switch(e){case w.HSL:case w.HWB:i=m[0],c=y[0],l=m[1],u=y[1],f=m[2],d=y[2];break;case w.LCH:case w.OKLCH:l=m[0],u=y[0],f=m[1],d=y[1],i=m[2],c=y[2]}i=$t(i,c),Number.isNaN(i)&&(i=0),c=$t(c,i),Number.isNaN(c)&&(c=0),l=$t(l,u),u=$t(u,l),f=$t(f,d),d=$t(d,f);const $=c-i;switch(t){case"shorter":$>180?i+=360:$<-180&&(c+=360);break;case"longer":-180<$&&$<180&&($>0?i+=360:c+=360);break;case"increasing":$<0&&(c+=360);break;case"decreasing":$>0&&(i+=360);break;default:throw new Error("Unknown hue interpolation method")}l=jt(l,b),f=jt(f,b),u=jt(u,p),d=jt(d,p);let N=[0,0,0];const C=St(b,p,o);switch(e){case w.HSL:case w.HWB:N=[St(i,c,o),An(St(l,u,o),C),An(St(f,d,o),C)];break;case w.LCH:case w.OKLCH:N=[An(St(l,u,o),C),An(St(f,d,o),C),St(i,c,o)]}return{colorNotation:e,channels:N,alpha:C,syntaxFlags:new Set([D.ColorMix])}}function $t(e,t){return Number.isNaN(e)?t:e}function St(e,t,n){return e*n+t*(1-n)}function jt(e,t){return Number.isNaN(t)?e:Number.isNaN(e)?Number.NaN:e*t}function An(e,t){return t===0||Number.isNaN(t)?e:Number.isNaN(e)?Number.NaN:e/t}function e0(e){const t=ae(e[4].value);if(t.match(/[^a-f0-9]/))return!1;const n={colorNotation:w.HEX,channels:[0,0,0],alpha:1,syntaxFlags:new Set([D.Hex])},r=t.length;if(r===3){const a=t[0],s=t[1],o=t[2];return n.channels=[parseInt(a+a,16)/255,parseInt(s+s,16)/255,parseInt(o+o,16)/255],n}if(r===6){const a=t[0]+t[1],s=t[2]+t[3],o=t[4]+t[5];return n.channels=[parseInt(a,16)/255,parseInt(s,16)/255,parseInt(o,16)/255],n}if(r===4){const a=t[0],s=t[1],o=t[2],i=t[3];return n.channels=[parseInt(a+a,16)/255,parseInt(s+s,16)/255,parseInt(o+o,16)/255],n.alpha=parseInt(i+i,16)/255,n.syntaxFlags.add(D.HasAlpha),n}if(r===8){const a=t[0]+t[1],s=t[2]+t[3],o=t[4]+t[5],i=t[6]+t[7];return n.channels=[parseInt(a,16)/255,parseInt(s,16)/255,parseInt(o,16)/255],n.alpha=parseInt(i,16)/255,n.syntaxFlags.add(D.HasAlpha),n}return!1}function Zr(e){if(H(e))return e[4].value=e[4].value%360,e[1]=e[4].value.toString(),e;if(se(e)){let t=e[4].value;switch(ae(e[4].unit)){case"deg":break;case"rad":t=180*e[4].value/Math.PI;break;case"grad":t=.9*e[4].value;break;case"turn":t=360*e[4].value;break;default:return!1}return t%=360,[v.Number,t.toString(),e[2],e[3],{value:t,type:S.Number}]}return!1}function t0(e,t,n){if(t===0){const r=Zr(e);return r!==!1&&(se(e)&&n.syntaxFlags.add(D.HasDimensionValues),r)}if(ne(e)){t===3?n.syntaxFlags.add(D.HasPercentageAlpha):n.syntaxFlags.add(D.HasPercentageValues);let r=V(e[4].value,1,0,100);return t===3&&(r=V(e[4].value,100,0,1)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}if(H(e)){if(t!==3)return!1;let r=V(e[4].value,1,0,100);return t===3&&(r=V(e[4].value,1,0,1)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}return!1}function n0(e,t,n){if(ue(e)&&ae(e[4].value)==="none")return n.syntaxFlags.add(D.HasNoneKeywords),[v.Number,"none",e[2],e[3],{value:Number.NaN,type:S.Number}];if(t===0){const r=Zr(e);return r!==!1&&(se(e)&&n.syntaxFlags.add(D.HasDimensionValues),r)}if(ne(e)){t===3?n.syntaxFlags.add(D.HasPercentageAlpha):n.syntaxFlags.add(D.HasPercentageValues);let r=e[4].value;return t===3?r=V(e[4].value,100,0,1):t===1&&(r=V(e[4].value,1,0,2147483647)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}if(H(e)){t!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=e[4].value;return t===3?r=V(e[4].value,1,0,1):t===1&&(r=V(e[4].value,1,0,2147483647)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}return!1}function Di(e,t,n,r){const a=[],s=[],o=[],i=[],c={colorNotation:n,channels:[0,0,0],alpha:1,syntaxFlags:new Set(r)};let l=a;for(let p=0;p<e.value.length;p++){let m=e.value[p];if(!rt(m)&&!at(m)){if(I(m)&&Nt(m.value)){if(l===a){l=s;continue}if(l===s){l=o;continue}if(l===o){l=i;continue}if(l===i)return!1}if(Ve(m)){if(l===i&&m.getName().toLowerCase()==="var"){c.syntaxFlags.add(D.HasVariableAlpha),l.push(m);continue}if(!Fr.has(m.getName().toLowerCase()))return!1;const[[y]]=qn([[m]],{censorIntoStandardRepresentableValues:!0,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!y||!I(y)||!fe(y.value))return!1;Number.isNaN(y.value[4].value)&&(y.value[4].value=0),m=y}if(!I(m))return!1;l.push(m)}}if(l.length!==1||a.length!==1||s.length!==1||o.length!==1||!I(a[0])||!I(s[0])||!I(o[0]))return!1;const u=t(a[0].value,0,c);if(!u||!H(u))return!1;const f=t(s[0].value,1,c);if(!f||!H(f))return!1;const d=t(o[0].value,2,c);if(!d||!H(d))return!1;const b=[u,f,d];if(i.length===1)if(c.syntaxFlags.add(D.HasAlpha),I(i[0])){const p=t(i[0].value,3,c);if(!p||!H(p))return!1;b.push(p)}else c.alpha=i[0];return c.channels=[b[0][4].value,b[1][4].value,b[2][4].value],b.length===4&&(c.alpha=b[3][4].value),c}function Pn(e,t,n,r,a){const s=[],o=[],i=[],c=[];let l,u,f=!1;const d={colorNotation:n,channels:[0,0,0],alpha:1,syntaxFlags:new Set(r)};let b=s;for(let N=0;N<e.value.length;N++){let C=e.value[N];if(rt(C)||at(C))for(;rt(e.value[N+1])||at(e.value[N+1]);)N++;else if(b===s&&s.length&&(b=o),b===o&&o.length&&(b=i),I(C)&&Nr(C.value)&&C.value[4].value==="/"){if(b===c)return!1;b=c}else{if(Ve(C)){if(b===c&&C.getName().toLowerCase()==="var"){d.syntaxFlags.add(D.HasVariableAlpha),b.push(C);continue}if(!Fr.has(C.getName().toLowerCase()))return!1;const[[E]]=qn([[C]],{censorIntoStandardRepresentableValues:!0,globals:u,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!E||!I(E)||!fe(E.value))return!1;Number.isNaN(E.value[4].value)&&(E.value[4].value=0),C=E}if(b===s&&s.length===0&&I(C)&&ue(C.value)&&C.value[4].value.toLowerCase()==="from"){if(f)return!1;for(;rt(e.value[N+1])||at(e.value[N+1]);)N++;if(N++,C=e.value[N],f=a(C),f===!1)return!1;f.syntaxFlags.has(D.Experimental)&&d.syntaxFlags.add(D.Experimental),d.syntaxFlags.add(D.RelativeColorSyntax),f.colorNotation!==n&&(f=bn(f,n)),l=Zs(f),u=Js(l)}else{if(!I(C))return!1;if(ue(C.value)&&l){const E=C.value[4].value.toLowerCase();if(l.has(E)){b.push(new G(l.get(E)));continue}}b.push(C)}}}if(b.length!==1||s.length!==1||o.length!==1||i.length!==1||!I(s[0])||!I(o[0])||!I(i[0])||l&&!l.has("alpha"))return!1;const p=t(s[0].value,0,d);if(!p||!H(p))return!1;const m=t(o[0].value,1,d);if(!m||!H(m))return!1;const y=t(i[0].value,2,d);if(!y||!H(y))return!1;const $=[p,m,y];if(c.length===1)if(d.syntaxFlags.add(D.HasAlpha),I(c[0])){const N=t(c[0].value,3,d);if(!N||!H(N))return!1;$.push(N)}else d.alpha=c[0];else if(l&&l.has("alpha")){const N=t(l.get("alpha"),3,d);if(!N||!H(N))return!1;$.push(N)}return d.channels=[$[0][4].value,$[1][4].value,$[2][4].value],$.length===4&&(d.alpha=$[3][4].value),d}function r0(e,t){if(e.value.some(n=>I(n)&&Nt(n.value))){const n=a0(e);if(n!==!1)return n}{const n=s0(e,t);if(n!==!1)return n}return!1}function a0(e){return Di(e,t0,w.HSL,[D.LegacyHSL])}function s0(e,t){return Pn(e,n0,w.HSL,[],t)}function o0(e,t,n){if(ue(e)&&ae(e[4].value)==="none")return n.syntaxFlags.add(D.HasNoneKeywords),[v.Number,"none",e[2],e[3],{value:Number.NaN,type:S.Number}];if(t===0){const r=Zr(e);return r!==!1&&(se(e)&&n.syntaxFlags.add(D.HasDimensionValues),r)}if(ne(e)){t===3?n.syntaxFlags.add(D.HasPercentageAlpha):n.syntaxFlags.add(D.HasPercentageValues);let r=e[4].value;return t===3&&(r=V(e[4].value,100,0,1)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}if(H(e)){t!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=e[4].value;return t===3&&(r=V(e[4].value,1,0,1)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}return!1}function i0(e,t,n){if(ue(e)&&ae(e[4].value)==="none")return n.syntaxFlags.add(D.HasNoneKeywords),[v.Number,"none",e[2],e[3],{value:Number.NaN,type:S.Number}];if(ne(e)){t!==3&&n.syntaxFlags.add(D.HasPercentageValues);let r=V(e[4].value,1,0,100);return t===1||t===2?r=V(e[4].value,.8,-2147483647,2147483647):t===3&&(r=V(e[4].value,100,0,1)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}if(H(e)){t!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=V(e[4].value,1,0,100);return t===1||t===2?r=V(e[4].value,1,-2147483647,2147483647):t===3&&(r=V(e[4].value,1,0,1)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}return!1}function l0(e,t){return Pn(e,i0,w.Lab,[],t)}function c0(e,t,n){if(ue(e)&&ae(e[4].value)==="none")return n.syntaxFlags.add(D.HasNoneKeywords),[v.Number,"none",e[2],e[3],{value:Number.NaN,type:S.Number}];if(t===2){const r=Zr(e);return r!==!1&&(se(e)&&n.syntaxFlags.add(D.HasDimensionValues),r)}if(ne(e)){t!==3&&n.syntaxFlags.add(D.HasPercentageValues);let r=V(e[4].value,1,0,100);return t===1?r=V(e[4].value,100/150,0,2147483647):t===3&&(r=V(e[4].value,100,0,1)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}if(H(e)){t!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=V(e[4].value,1,0,100);return t===1?r=V(e[4].value,1,0,2147483647):t===3&&(r=V(e[4].value,1,0,1)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}return!1}function u0(e,t){return Pn(e,c0,w.LCH,[],t)}const Bi=new Map;for(const[e,t]of Object.entries(zh))Bi.set(e,t);function h0(e){const t=Bi.get(ae(e));return!!t&&{colorNotation:w.RGB,channels:[t[0]/255,t[1]/255,t[2]/255],alpha:1,syntaxFlags:new Set([D.ColorKeyword,D.NamedColor])}}function f0(e,t,n){if(ue(e)&&ae(e[4].value)==="none")return n.syntaxFlags.add(D.HasNoneKeywords),[v.Number,"none",e[2],e[3],{value:Number.NaN,type:S.Number}];if(ne(e)){t!==3&&n.syntaxFlags.add(D.HasPercentageValues);let r=V(e[4].value,100,0,1);return t===1||t===2?r=V(e[4].value,250,-2147483647,2147483647):t===3&&(r=V(e[4].value,100,0,1)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}if(H(e)){t!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=V(e[4].value,1,0,1);return t===1||t===2?r=V(e[4].value,1,-2147483647,2147483647):t===3&&(r=V(e[4].value,1,0,1)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}return!1}function p0(e,t){return Pn(e,f0,w.OKLab,[],t)}function d0(e,t,n){if(ue(e)&&ae(e[4].value)==="none")return n.syntaxFlags.add(D.HasNoneKeywords),[v.Number,"none",e[2],e[3],{value:Number.NaN,type:S.Number}];if(t===2){const r=Zr(e);return r!==!1&&(se(e)&&n.syntaxFlags.add(D.HasDimensionValues),r)}if(ne(e)){t!==3&&n.syntaxFlags.add(D.HasPercentageValues);let r=V(e[4].value,100,0,1);return t===1?r=V(e[4].value,250,0,2147483647):t===3&&(r=V(e[4].value,100,0,1)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}if(H(e)){t!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=V(e[4].value,1,0,1);return t===1?r=V(e[4].value,1,0,2147483647):t===3&&(r=V(e[4].value,1,0,1)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}return!1}function m0(e,t){return Pn(e,d0,w.OKLCH,[],t)}function g0(e,t,n){if(ne(e)){t===3?n.syntaxFlags.add(D.HasPercentageAlpha):n.syntaxFlags.add(D.HasPercentageValues);const r=V(e[4].value,100,0,1);return[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}if(H(e)){t!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=V(e[4].value,255,0,1);return t===3&&(r=V(e[4].value,1,0,1)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}return!1}function v0(e,t,n){if(ue(e)&&e[4].value.toLowerCase()==="none")return n.syntaxFlags.add(D.HasNoneKeywords),[v.Number,"none",e[2],e[3],{value:Number.NaN,type:S.Number}];if(ne(e)){t!==3&&n.syntaxFlags.add(D.HasPercentageValues);let r=V(e[4].value,100,-2147483647,2147483647);return t===3&&(r=V(e[4].value,100,0,1)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}if(H(e)){t!==3&&n.syntaxFlags.add(D.HasNumberValues);let r=V(e[4].value,255,-2147483647,2147483647);return t===3&&(r=V(e[4].value,1,0,1)),[v.Number,r.toString(),e[2],e[3],{value:r,type:S.Number}]}return!1}function b0(e,t){if(e.value.some(n=>I(n)&&Nt(n.value))){const n=w0(e);if(n!==!1)return(!n.syntaxFlags.has(D.HasNumberValues)||!n.syntaxFlags.has(D.HasPercentageValues))&&n}else{const n=$0(e,t);if(n!==!1)return n}return!1}function w0(e){return Di(e,g0,w.RGB,[D.LegacyRGB])}function $0(e,t){return Pn(e,v0,w.RGB,[],t)}function y0(e){const t=Ys(e);if(Th(t))return xi(t);let n=e;return n=Ci(n),n[0]<1e-6&&(n=[0,0,0]),n[0]>.999999&&(n=[1,0,0]),Xr(Hh(n,N0,E0))}function N0(e){return e=vi(e),e=Is(e),Vr(e)}function E0(e){return e=Kr(e),e=Hs(e),bi(e)}function C0(e,t){let n=!1;for(let o=0;o<e.value.length;o++){const i=e.value[o];if(!rt(i)&&!at(i)&&(n||(n=t(i),!n)))return!1}if(!n)return!1;n.channels=De(n.channels),n.channels=y0(Si(n).channels),n.colorNotation=w.sRGB;const r={colorNotation:w.sRGB,channels:[0,0,0],alpha:1,syntaxFlags:new Set([D.ContrastColor,D.Experimental])},a=ki(n.channels,[1,1,1]),s=ki(n.channels,[0,0,0]);return r.channels=a>s?[1,1,1]:[0,0,0],r}function x0(e,t){let n,r,a=!1,s=!1,o=!1;const i={colorNotation:w.sRGB,channels:[0,0,0],alpha:1,syntaxFlags:new Set([])};for(let c=0;c<e.value.length;c++){let l=e.value[c];if(rt(l)||at(l))for(;rt(e.value[c+1])||at(e.value[c+1]);)c++;else if(o&&!a&&!s&&I(l)&&Nr(l.value)&&l.value[4].value==="/")a=!0;else{if(Ve(l)&&Fr.has(ae(l.getName()))){const[[u]]=qn([[l]],{censorIntoStandardRepresentableValues:!0,globals:r,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!u||!I(u)||!fe(u.value))return!1;Number.isNaN(u.value[4].value)&&(u.value[4].value=0),l=u}if(a||s||!I(l)||!ue(l.value)||ae(l.value[4].value)!=="from"){if(!a||s)return!1;if(I(l)){if(ue(l.value)&&ae(l.value[4].value)==="alpha"&&n&&n.has("alpha")){i.alpha=n.get("alpha")[4].value,s=!0;continue}const u=lr(l.value,3,i);if(!u||!H(u))return!1;i.alpha=new G(u),s=!0;continue}if(Ve(l)){const u=bo([[l]],f=>{if(I(f)&&ue(f.value)&&ae(f.value[4].value)==="alpha"&&n&&n.has("alpha"))return new G(n.get("alpha"))});i.alpha=u[0][0],s=!0;continue}return!1}if(o)return!1;for(;rt(e.value[c+1])||at(e.value[c+1]);)c++;if(c++,l=e.value[c],o=t(l),o===!1)return!1;n=Zs(o),r=Js(n),i.syntaxFlags=new Set(o.syntaxFlags),i.syntaxFlags.add(D.RelativeAlphaSyntax),i.channels=[...o.channels],i.colorNotation=o.colorNotation,i.alpha=o.alpha}}return!!n&&i}function yt(e){if(Ve(e))switch(ae(e.getName())){case"rgb":case"rgba":return b0(e,yt);case"hsl":case"hsla":return r0(e,yt);case"hwb":return t=yt,Pn(e,o0,w.HWB,[],t);case"lab":return l0(e,yt);case"lch":return u0(e,yt);case"oklab":return p0(e,yt);case"oklch":return m0(e,yt);case"color":return Vh(e,yt);case"color-mix":return Zh(e,yt);case"contrast-color":return C0(e,yt);case"alpha":return x0(e,yt)}var t;if(I(e)){if(Tl(e.value))return e0(e.value);if(ue(e.value)){const n=h0(e.value[4].value);return n!==!1?n:ae(e.value[4].value)==="transparent"&&{colorNotation:w.RGB,channels:[0,0,0],alpha:0,syntaxFlags:new Set([D.ColorKeyword])}}}return!1}const{CloseParen:Ri,Comment:Oi,Dimension:F0,EOF:Wi,Function:Li,Ident:k0,Number:S0,OpenParen:_i,Percentage:M0,Whitespace:Ii}=v,{HasNoneKeywords:eo}=D,Ti="relative-color",A0=8,cr=10,to=16,P0=100,no=255,Hi=new RegExp(`^${Wr}(${Jn}|${xa})\\s+`),D0=/(?:hsla?|hwb)$/,B0=new RegExp(`^(?:${Uo}|${pu})$`),R0=new RegExp(Oo),O0=new RegExp(Wr),Ui=new RegExp(`^${mu}`),zi=new RegExp(`^${Wr}`),W0=new RegExp(Rr);function Gi(e,t={}){if(!Array.isArray(e))throw new TypeError(`${e} is not an array.`);const{colorSpace:n="",format:r=""}=t,a=new Map([["color",["r","g","b","alpha"]],["hsl",["h","s","l","alpha"]],["hsla",["h","s","l","alpha"]],["hwb",["h","w","b","alpha"]],["lab",["l","a","b","alpha"]],["lch",["l","c","h","alpha"]],["oklab",["l","a","b","alpha"]],["oklch",["l","c","h","alpha"]],["rgb",["r","g","b","alpha"]],["rgba",["r","g","b","alpha"]]]).get(n);if(!a)return new A;const s=new Set,o=[[],[],[],[]];let i=0,c=0,l=!1;for(;e.length;){const f=e.shift();if(!Array.isArray(f))throw new TypeError(`${f} is not an array.`);const[d,b,,,p]=f,m=o[i];if(Array.isArray(m))switch(d){case F0:{const y=rl(f,t);k(y)?m.push(y):m.push(b);break}case Li:{m.push(b),l=!0,c++,R0.test(b)&&s.add(c);break}case k0:{if(!a.includes(b))return new A;m.push(b),l||i++;break}case S0:{m.push(Number(p?.value)),l||i++;break}case _i:{m.push(b),c++;break}case Ri:{l&&(m[m.length-1]===" "?m.splice(-1,1,b):m.push(b),s.has(c)&&s.delete(c),c--,c===0&&(l=!1,i++));break}case M0:{m.push(Number(p?.value)/P0),l||i++;break}case Ii:{if(m.length&&l){const y=m[m.length-1];(typeof y=="number"||k(y)&&!y.endsWith("(")&&y!==" ")&&m.push(b)}break}default:d!==Oi&&d!==Wi&&l&&m.push(b)}}const u=[];for(const f of o)if(f.length===1){const[d]=f;Yn(d)&&u.push(d)}else if(f.length){const d=nl(f.join(""),{format:r});u.push(d)}return u}function L0(e,t={}){const{colorScheme:n="normal",currentColor:r="",format:a=""}=t;if(k(e)){if(e=e.toLowerCase().trim(),!e)return new A;if(!zi.test(e))return e}else return new A;const s=we({namespace:Ti,name:"extractOriginColor",value:e},t),o=be(s);if(o instanceof le)return o.isNull?o:o.item;if(/currentcolor/.test(e))if(r)e=e.replace(/currentcolor/g,r);else return x(s,null),new A;let i="";if(Ui.test(e)&&([,i]=e.match(Ui)),t.colorSpace=i,e.includes(Ea)){const c=e.replace(new RegExp(`^${i}\\(`),"").replace(/\)$/,""),[,l=""]=Bn(c),u=xe(l,{colorScheme:n,format:Q});if(u==="")return x(s,null),new A;if(a===Q)e=e.replace(l,u);else{const f=xe(u,t);k(f)&&(e=e.replace(l,f))}}if(Hi.test(e)){const[,c]=e.match(Hi),[,l]=e.split(c);if(/^[a-z]+$/.test(c)){if(!/^transparent$/.test(c)&&!Object.hasOwn(sr,c))return x(s,null),new A}else if(a===Q){const u=xe(c,t);k(u)&&(e=e.replace(c,u))}if(a===Q){const u=Zt({css:l}),f=Gi(u,t);if(f instanceof A)return x(s,null),f;const[d,b,p,m]=f;let y="";Yn(m)?y=` ${d} ${b} ${p} / ${m})`:y=` ${f.join(" ")})`,l!==y&&(e=e.replace(l,y))}}else{const[,c]=e.split(zi),l=Zt({css:c}),u=[];let f=0;for(;l.length;){const[C,E]=l.shift();switch(C){case Li:case _i:{u.push(E),f++;break}case Ri:{const J=u[u.length-1];J===" "?u.splice(-1,1,E):k(J)&&u.push(E),f--;break}case Ii:{const J=u[u.length-1];k(J)&&!J.endsWith("(")&&J!==" "&&u.push(E);break}default:C!==Oi&&C!==Wi&&u.push(E)}if(f===0)break}const d=ro(u.join("").trim(),t);if(d instanceof A)return x(s,null),d;const b=Gi(l,t);if(b instanceof A)return x(s,null),b;const[p,m,y,$]=b;let N="";Yn($)?N=` ${p} ${m} ${y} / ${$})`:N=` ${b.join(" ")})`,e=e.replace(c,`${d}${N}`)}return x(s,e),e}function ro(e,t={}){const{format:n=""}=t;if(k(e)){if(W0.test(e)){if(n===Q)return e;throw new SyntaxError(`Unexpected token ${ps} found.`)}else if(!O0.test(e))return e;e=e.toLowerCase().trim()}else throw new TypeError(`${e} is not a string.`);const r=we({namespace:Ti,name:"resolveRelativeColor",value:e},t),a=be(r);if(a instanceof le)return a.isNull?a:a.item;const s=L0(e,t);if(s instanceof A)return x(r,null),s;if(e=s,n===Q)return e.startsWith("rgba(")?e=e.replace(/^rgba\(/,"rgb("):e.startsWith("hsla(")&&(e=e.replace(/^hsla\(/,"hsl(")),e;const o=Zt({css:e}),i=Vl(o),c=yt(i);if(!c)return x(r,null),new A;const{alpha:l,channels:u,colorNotation:f,syntaxFlags:d}=c;let b;Number.isNaN(Number(l))?d instanceof Set&&d.has(eo)?b=g:b=0:b=R(Number(l),A0);let p,m,y;[p,m,y]=u;let $;if(B0.test(f)){const N=d instanceof Set&&d.has(eo);Number.isNaN(p)?N?p=g:p=0:p=R(p,to),Number.isNaN(m)?N?m=g:m=0:m=R(m,to),Number.isNaN(y)?N?y=g:y=0:y=R(y,to),b===1?$=`${f}(${p} ${m} ${y})`:$=`${f}(${p} ${m} ${y} / ${b})`}else if(D0.test(f)){Number.isNaN(p)&&(p=0),Number.isNaN(m)&&(m=0),Number.isNaN(y)&&(y=0);let[N,C,E]=jr(`${f}(${p} ${m} ${y} / ${b})`);N=R(N/no,cr),C=R(C/no,cr),E=R(E/no,cr),b===1?$=`color(srgb ${N} ${C} ${E})`:$=`color(srgb ${N} ${C} ${E} / ${b})`}else{const N=f==="rgb"?"srgb":f,C=d instanceof Set&&d.has(eo);Number.isNaN(p)?C?p=g:p=0:p=R(p,cr),Number.isNaN(m)?C?m=g:m=0:m=R(m,cr),Number.isNaN(y)?C?y=g:y=0:y=R(y,cr),b===1?$=`color(${N} ${p} ${m} ${y})`:$=`color(${N} ${p} ${m} ${y} / ${b})`}return x(r,$),$}const _0="resolve",Dn="rgba(0, 0, 0, 0)",I0=new RegExp($a),ji=new RegExp(gu),T0=new RegExp(jo),H0=new RegExp(Rr),xe=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{colorScheme:n="normal",currentColor:r="",format:a=oe,nullable:s=!1}=t,o=we({namespace:_0,name:"resolve",value:e},t),i=be(o);if(i instanceof le)return i.isNull?i:i.item;if(H0.test(e)){if(a===Q)return x(o,e),e;const p=qr(e,t);if(p instanceof A)switch(a){case"hex":case"hexAlpha":return x(o,p),p;default:{if(s)return x(o,p),p;const m=Dn;return x(o,m),m}}else e=p}if(t.format!==a&&(t.format=a),e=e.toLowerCase(),ji.test(e)&&e.endsWith(")")){const p=e.replace(ji,"").replace(/\)$/,""),[m="",y=""]=Bn(p,{delimiter:","});if(m&&y){if(a===Q){const C=xe(m,t),E=xe(y,t);let J;return C&&E?J=`light-dark(${C}, ${E})`:J="",x(o,J),J}let $;n==="dark"?$=xe(y,t):$=xe(m,t);let N;return $ instanceof A?s?N=$:N=Dn:N=$,x(o,N),N}switch(a){case Q:return x(o,""),"";case"hex":case"hexAlpha":return x(o,null),new A;case oe:default:{const $=Dn;return x(o,$),$}}}if(T0.test(e)){const p=ro(e,t);if(a===oe){let m;return p instanceof A?s?m=p:m=Dn:m=p,x(o,m),m}if(a===Q){let m="";return p instanceof A?m="":m=p,x(o,m),m}p instanceof A?e="":e=p}I0.test(e)&&(e=Jr(e,t));let c="",l=NaN,u=NaN,f=NaN,d=NaN;if(e==="transparent")switch(a){case Q:return x(o,e),e;case"hex":return x(o,null),new A;case"hexAlpha":{const p="#00000000";return x(o,p),p}case oe:default:{const p=Dn;return x(o,p),p}}else if(e==="currentcolor"){if(a===Q)return x(o,e),e;if(r){let p;if(r.startsWith(vn)?p=Ra(r,t):r.startsWith(pe)?p=tn(r,t):p=Mn(r,t),p instanceof A)return x(o,p),p;[c,l,u,f,d]=p}else if(a===oe){const p=Dn;return x(o,p),p}}else if(a===Q)if(e.startsWith(vn)){const p=Ra(e,t);return x(o,p),p}else if(e.startsWith(pe)){const[p,m,y,$,N]=tn(e,t);let C="";return N===1?C=`color(${p} ${m} ${y} ${$})`:C=`color(${p} ${m} ${y} ${$} / ${N})`,x(o,C),C}else{const p=Mn(e,t);if(k(p))return x(o,p),p;const[m,y,$,N,C]=p;let E="";return m==="rgb"?C===1?E=`${m}(${y}, ${$}, ${N})`:E=`${m}a(${y}, ${$}, ${N}, ${C})`:C===1?E=`${m}(${y} ${$} ${N})`:E=`${m}(${y} ${$} ${N} / ${C})`,x(o,E),E}else if(e.startsWith(vn)){/currentcolor/.test(e)&&r&&(e=e.replace(/currentcolor/g,r)),/transparent/.test(e)&&(e=e.replace(/transparent/g,Dn));const p=Ra(e,t);if(p instanceof A)return x(o,p),p;[c,l,u,f,d]=p}else if(e.startsWith(pe)){const p=tn(e,t);if(p instanceof A)return x(o,p),p;[c,l,u,f,d]=p}else if(e){const p=Mn(e,t);if(p instanceof A)return x(o,p),p;[c,l,u,f,d]=p}let b="";switch(a){case"hex":{if(Number.isNaN(l)||Number.isNaN(u)||Number.isNaN(f)||Number.isNaN(d)||d===0)return x(o,null),new A;b=li([l,u,f,1]);break}case"hexAlpha":{if(Number.isNaN(l)||Number.isNaN(u)||Number.isNaN(f)||Number.isNaN(d))return x(o,null),new A;b=li([l,u,f,d]);break}case oe:default:switch(c){case"rgb":{d===1?b=`${c}(${l}, ${u}, ${f})`:b=`${c}a(${l}, ${u}, ${f}, ${d})`;break}case"lab":case"lch":case"oklab":case"oklch":{d===1?b=`${c}(${l} ${u} ${f})`:b=`${c}(${l} ${u} ${f} / ${d})`;break}default:d===1?b=`color(${c} ${l} ${u} ${f})`:b=`color(${c} ${l} ${u} ${f} / ${d})`}}return x(o,b),b},U0=(e,t={})=>{t.nullable=!1;const n=xe(e,t);return n instanceof A?null:n},{CloseParen:z0,Comma:G0,Comment:j0,Delim:q0,EOF:V0,Function:X0,Ident:K0,OpenParen:Y0,Whitespace:Z0}=v,qi="util",J0=10,La=16,ur=360,_a=180,Q0=new RegExp(`^(?:${Jn})$`),e1=/^(?:(?:ok)?l(?:ab|ch)|color(?:-mix)?|hsla?|hwb|rgba?|var)\(/,t1=new RegExp(xa),Bn=(e,t={})=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const{delimiter:n=" ",preserveComment:r=!1}=t,a=we({namespace:qi,name:"splitValue",value:e},{delimiter:n,preserveComment:r}),s=be(a);if(s instanceof le)return s.item;let o;n===","?o=/^,$/:n==="/"?o=/^\/$/:o=/^\s+$/;const i=Zt({css:e});let c=0,l="";const u=[];for(;i.length;){const[f,d]=i.shift();switch(f){case G0:{o.test(d)&&c===0?(u.push(l.trim()),l=""):l+=d;break}case q0:{o.test(d)&&c===0?(u.push(l.trim()),l=""):l+=d;break}case j0:{r&&(n===","||n==="/")&&(l+=d);break}case X0:case Y0:{l+=d,c++;break}case z0:{l+=d,c--;break}case Z0:{o.test(d)?c===0?l&&(u.push(l.trim()),l=""):l+=" ":l.endsWith(" ")||(l+=" ");break}default:f===V0?(u.push(l.trim()),l=""):l+=d}}return x(a,u),u},n1=e=>{if(k(e))e=e.trim();else throw new TypeError(`${e} is not a string.`);const t=we({namespace:qi,name:"extractDashedIdent",value:e}),n=be(t);if(n instanceof le)return n.item;const r=Zt({css:e}),a=new Set;for(;r.length;){const[o,i]=r.shift();o===K0&&i.startsWith("--")&&a.add(i)}const s=[...a];return x(t,s),s},wn=(e,t={})=>{if(k(e)&&(e=e.toLowerCase().trim(),e&&k(e))){if(/^[a-z]+$/.test(e)){if(/^(?:currentcolor|transparent)$/.test(e)||Object.hasOwn(sr,e))return!0}else if(Q0.test(e)||t1.test(e)||e1.test(e)&&(t.nullable=!0,t.format||(t.format=Q),xe(e,t)))return!0}return!1},Vi=(e,t=!1)=>typeof e>"u"?"":JSON.stringify(e,(n,r)=>{let a;return typeof r>"u"?a=null:typeof r=="function"?t?a=r.toString().replace(/\s/g,"").substring(0,La):a=r.name:r instanceof Map||r instanceof Set?a=[...r]:typeof r=="bigint"?a=r.toString():a=r,a}),R=(e,t=0)=>{if(!Number.isFinite(e))throw new TypeError(`${e} is not a finite number.`);if(Number.isFinite(t)){if(t<0||t>La)throw new RangeError(`${t} is not between 0 and ${La}.`)}else throw new TypeError(`${t} is not a finite number.`);if(t===0)return Math.round(e);let n;return t===La?n=e.toPrecision(6):t<J0?n=e.toPrecision(4):n=e.toPrecision(5),parseFloat(n)},Xi=(e,t,n="shorter")=>{if(!Number.isFinite(e))throw new TypeError(`${e} is not a finite number.`);if(!Number.isFinite(t))throw new TypeError(`${t} is not a finite number.`);switch(n){case"decreasing":{t>e&&(e+=ur);break}case"increasing":{t<e&&(t+=ur);break}case"longer":{t>e&&t<e+_a?e+=ur:t>e+_a*-1&&t<=e&&(t+=ur);break}case"shorter":default:t>e+_a?e+=ur:t<e+_a*-1&&(t+=ur)}return[e,t]},r1=4096;var Ia,Ta;class le{constructor(t,n=!1){U(this,Ia),U(this,Ta),F(this,Ta,t),F(this,Ia,!!n)}get item(){return h(this,Ta)}get isNull(){return h(this,Ia)}}Ia=new WeakMap,Ta=new WeakMap;class A extends le{constructor(){super(Symbol("null"),!0)}}const hr=new nu({max:r1}),x=(e,t)=>{e&&(t===null?hr.set(e,new A):t instanceof le?hr.set(e,t):hr.set(e,new le(t)))},be=e=>{if(e&&hr.has(e)){const t=hr.get(e);return t instanceof le?t:(hr.delete(e),!1)}return!1},we=(e,t={})=>{const{customProperty:n={},dimension:r={}}=t;let a="";return e&&Object.keys(e).length&&typeof n.callback!="function"&&typeof r.callback!="function"&&(e.opt=Vi(t),a=Vi(e)),a},{CloseParen:a1,Comment:Ki,Dimension:s1,EOF:o1,Function:i1,OpenParen:l1,Whitespace:Yi}=v,Zi="css-calc",c1=3,Ha=16,Ji=100,u1=new RegExp($a),Qi=new RegExp(`^calc\\((${ze})\\)$`),h1=new RegExp(Oo),f1=new RegExp(Rr),Ua=new RegExp(cu),el=/\s[*+/-]\s/,za=new RegExp(`^(${ze})(${Zn}|${Br})$`),fr=new RegExp(`^(${ze})(${Zn}|${Br}|%)$`),$n=new RegExp(`^(${ze})%$`);var yn,pr,dr,nn,mr,gr,qt,Rn,On,rn,an,Nn,En,sn,Wn,Ln;class p1{constructor(){U(this,yn),U(this,pr),U(this,dr),U(this,nn),U(this,mr),U(this,gr),U(this,qt),U(this,Rn),U(this,On),U(this,rn),U(this,an),U(this,Nn),U(this,En),U(this,sn),U(this,Wn),U(this,Ln),F(this,yn,!1),F(this,pr,[]),F(this,dr,[]),F(this,nn,!1),F(this,mr,[]),F(this,gr,[]),F(this,qt,!1),F(this,Rn,[]),F(this,On,[]),F(this,rn,[]),F(this,an,[]),F(this,Nn,!1),F(this,En,[]),F(this,sn,[]),F(this,Wn,[]),F(this,Ln,[])}get hasNum(){return h(this,yn)}set hasNum(t){F(this,yn,!!t)}get numSum(){return h(this,pr)}get numMul(){return h(this,dr)}get hasPct(){return h(this,nn)}set hasPct(t){F(this,nn,!!t)}get pctSum(){return h(this,mr)}get pctMul(){return h(this,gr)}get hasDim(){return h(this,qt)}set hasDim(t){F(this,qt,!!t)}get dimSum(){return h(this,Rn)}get dimSub(){return h(this,On)}get dimMul(){return h(this,rn)}get dimDiv(){return h(this,an)}get hasEtc(){return h(this,Nn)}set hasEtc(t){F(this,Nn,!!t)}get etcSum(){return h(this,En)}get etcSub(){return h(this,sn)}get etcMul(){return h(this,Wn)}get etcDiv(){return h(this,Ln)}clear(){F(this,yn,!1),F(this,pr,[]),F(this,dr,[]),F(this,nn,!1),F(this,mr,[]),F(this,gr,[]),F(this,qt,!1),F(this,Rn,[]),F(this,On,[]),F(this,rn,[]),F(this,an,[]),F(this,Nn,!1),F(this,En,[]),F(this,sn,[]),F(this,Wn,[]),F(this,Ln,[])}sort(t=[]){const n=[...t];return n.length>1&&n.sort((r,a)=>{let s;if(fr.test(r)&&fr.test(a)){const[,o,i]=r.match(fr),[,c,l]=a.match(fr);i===l?Number(o)===Number(c)?s=0:Number(o)>Number(c)?s=1:s=-1:i>l?s=1:s=-1}else r===a?s=0:r>a?s=1:s=-1;return s}),n}multiply(){const t=[];let n;if(h(this,yn)){n=1;for(const r of h(this,dr))if(n*=r,n===0||!Number.isFinite(n)||Number.isNaN(n))break;!h(this,nn)&&!h(this,qt)&&!this.hasEtc&&(Number.isFinite(n)&&(n=R(n,Ha)),t.push(n))}if(h(this,nn)){typeof n!="number"&&(n=1);for(const r of h(this,gr))if(n*=r,n===0||!Number.isFinite(n)||Number.isNaN(n))break;Number.isFinite(n)&&(n=`${R(n,Ha)}%`),!h(this,qt)&&!this.hasEtc&&t.push(n)}if(h(this,qt)){let r="",a="",s="";h(this,rn).length&&(h(this,rn).length===1?[a]=h(this,rn):a=`${this.sort(h(this,rn)).join(" * ")}`),h(this,an).length&&(h(this,an).length===1?[s]=h(this,an):s=`${this.sort(h(this,an)).join(" * ")}`),Number.isFinite(n)?(a?s?s.includes("*")?r=He(`calc(${n} * ${a} / (${s}))`,{toCanonicalUnits:!0}):r=He(`calc(${n} * ${a} / ${s})`,{toCanonicalUnits:!0}):r=He(`calc(${n} * ${a})`,{toCanonicalUnits:!0}):s.includes("*")?r=He(`calc(${n} / (${s}))`,{toCanonicalUnits:!0}):r=He(`calc(${n} / ${s})`,{toCanonicalUnits:!0}),t.push(r.replace(/^calc/,""))):(!t.length&&n!==void 0&&t.push(n),a?(s?s.includes("*")?r=He(`calc(${a} / (${s}))`,{toCanonicalUnits:!0}):r=He(`calc(${a} / ${s})`,{toCanonicalUnits:!0}):r=He(`calc(${a})`,{toCanonicalUnits:!0}),t.length?t.push("*",r.replace(/^calc/,"")):t.push(r.replace(/^calc/,""))):(r=He(`calc(${s})`,{toCanonicalUnits:!0}),t.length?t.push("/",r.replace(/^calc/,"")):t.push("1","/",r.replace(/^calc/,""))))}if(h(this,Nn)){if(h(this,Wn).length){!t.length&&n!==void 0&&t.push(n);const r=this.sort(h(this,Wn)).join(" * ");t.length?t.push(`* ${r}`):t.push(`${r}`)}if(h(this,Ln).length){const r=this.sort(h(this,Ln)).join(" * ");r.includes("*")?t.length?t.push(`/ (${r})`):t.push(`1 / (${r})`):t.length?t.push(`/ ${r}`):t.push(`1 / ${r}`)}}return t.length?t.join(" "):""}sum(){const t=[];if(h(this,yn)){let n=0;for(const r of h(this,pr))if(n+=r,!Number.isFinite(n)||Number.isNaN(n))break;t.push(n)}if(h(this,nn)){let n=0;for(const r of h(this,mr))if(n+=r,!Number.isFinite(n))break;Number.isFinite(n)&&(n=`${n}%`),t.length?t.push(`+ ${n}`):t.push(n)}if(h(this,qt)){let n,r,a;h(this,Rn).length&&(r=this.sort(h(this,Rn)).join(" + ")),h(this,On).length&&(a=this.sort(h(this,On)).join(" + ")),r?a?a.includes("-")?n=He(`calc(${r} - (${a}))`,{toCanonicalUnits:!0}):n=He(`calc(${r} - ${a})`,{toCanonicalUnits:!0}):n=He(`calc(${r})`,{toCanonicalUnits:!0}):n=He(`calc(-1 * (${a}))`,{toCanonicalUnits:!0}),t.length?t.push("+",n.replace(/^calc/,"")):t.push(n.replace(/^calc/,""))}if(h(this,Nn)){if(h(this,En).length){const n=this.sort(h(this,En)).map(r=>{let a;return el.test(r)&&!r.startsWith("(")&&!r.endsWith(")")?a=`(${r})`:a=r,a}).join(" + ");t.length?h(this,En).length>1?t.push(`+ (${n})`):t.push(`+ ${n}`):t.push(`${n}`)}if(h(this,sn).length){const n=this.sort(h(this,sn)).map(r=>{let a;return el.test(r)&&!r.startsWith("(")&&!r.endsWith(")")?a=`(${r})`:a=r,a}).join(" + ");t.length?h(this,sn).length>1?t.push(`- (${n})`):t.push(`- ${n}`):h(this,sn).length>1?t.push(`-1 * (${n})`):t.push(`-1 * ${n}`)}}return t.length?t.join(" "):""}}yn=new WeakMap,pr=new WeakMap,dr=new WeakMap,nn=new WeakMap,mr=new WeakMap,gr=new WeakMap,qt=new WeakMap,Rn=new WeakMap,On=new WeakMap,rn=new WeakMap,an=new WeakMap,Nn=new WeakMap,En=new WeakMap,sn=new WeakMap,Wn=new WeakMap,Ln=new WeakMap;const tl=(e=[],t=!1)=>{if(e.length<c1)throw new Error(`Unexpected array length ${e.length}.`);const n=e.shift();if(!k(n)||!n.endsWith("("))throw new Error(`Unexpected token ${n}.`);const r=e.pop();if(r!==")")throw new Error(`Unexpected token ${r}.`);if(e.length===1){const[l]=e;if(!Yn(l))throw new Error(`Unexpected token ${l}.`);return`${n}${l}${r}`}const a=[],s=new p1;let o="";const i=e.length;for(let l=0;l<i;l++){const u=e[l];if(!Yn(u))throw new Error(`Unexpected token ${u}.`);if(u==="*"||u==="/")o=u;else if(u==="+"||u==="-"){const f=s.multiply();f&&a.push(f,u),s.clear(),o=""}else{const f=Number(u),d=`${u}`;switch(o){case"/":{if(Number.isFinite(f))s.hasNum=!0,s.numMul.push(1/f);else if($n.test(d)){const[,b]=d.match($n);s.hasPct=!0,s.pctMul.push(Ji*Ji/Number(b))}else za.test(d)?(s.hasDim=!0,s.dimDiv.push(d)):(s.hasEtc=!0,s.etcDiv.push(d));break}case"*":default:if(Number.isFinite(f))s.hasNum=!0,s.numMul.push(f);else if($n.test(d)){const[,b]=d.match($n);s.hasPct=!0,s.pctMul.push(Number(b))}else za.test(d)?(s.hasDim=!0,s.dimMul.push(d)):(s.hasEtc=!0,s.etcMul.push(d))}}if(l===i-1){const f=s.multiply();f&&a.push(f),s.clear(),o=""}}let c="";if(t&&(a.includes("+")||a.includes("-"))){const l=[];s.clear(),o="";const u=a.length;for(let f=0;f<u;f++){const d=a[f];if(Yn(d))if(d==="+"||d==="-")o=d;else{const b=Number(d),p=`${d}`;switch(o){case"-":{if(Number.isFinite(b))s.hasNum=!0,s.numSum.push(-1*b);else if($n.test(p)){const[,m]=p.match($n);s.hasPct=!0,s.pctSum.push(-1*Number(m))}else za.test(p)?(s.hasDim=!0,s.dimSub.push(p)):(s.hasEtc=!0,s.etcSub.push(p));break}case"+":default:if(Number.isFinite(b))s.hasNum=!0,s.numSum.push(b);else if($n.test(p)){const[,m]=p.match($n);s.hasPct=!0,s.pctSum.push(Number(m))}else za.test(p)?(s.hasDim=!0,s.dimSum.push(p)):(s.hasEtc=!0,s.etcSum.push(p))}}if(f===u-1){const b=s.sum();b&&l.push(b),s.clear(),o=""}}c=l.join(" ").replace(/\+\s-/g,"- ")}else c=a.join(" ").replace(/\+\s-/g,"- ");return c.startsWith("(")&&c.endsWith(")")&&c.lastIndexOf("(")===0&&c.indexOf(")")===c.length-1&&(c=c.replace(/^\(/,"").replace(/\)$/,"")),`${n}${c}${r}`},nl=(e,t={})=>{const{format:n=""}=t;if(k(e)){if(!Ua.test(e)||n!==Q)return e;e=e.toLowerCase().trim()}else throw new TypeError(`${e} is not a string.`);const r=we({namespace:Zi,name:"serializeCalc",value:e},t),a=be(r);if(a instanceof le)return a.item;const s=Zt({css:e}).map(c=>{const[l,u]=c;let f="";return l!==Yi&&l!==Ki&&(f=u),f}).filter(c=>c);let o=s.findLastIndex(c=>/\($/.test(c));for(;o;){const c=s.findIndex((f,d)=>f===")"&&d>o),l=s.slice(o,c+1);let u=tl(l);Ua.test(u)&&(u=He(u,{toCanonicalUnits:!0})),s.splice(o,c-o+1,u),o=s.findLastIndex(f=>/\($/.test(f))}const i=tl(s,!0);return x(r,i),i},rl=(e,t={})=>{if(!Array.isArray(e))throw new TypeError(`${e} is not an array.`);const[,,,,n={}]=e,{unit:r,value:a}=n,{dimension:s={}}=t;if(r==="px")return`${a}${r}`;const o=Number(a);if(r&&Number.isFinite(o)){let i;if(Object.hasOwn(s,r)?i=s[r]:typeof s.callback=="function"&&(i=s.callback(r)),i=Number(i),Number.isFinite(i))return`${o*i}px`}return new A},d1=(e,t={})=>{if(!Array.isArray(e))throw new TypeError(`${e} is not an array.`);const{format:n=""}=t,r=new Set;let a=0;const s=[];for(;e.length;){const o=e.shift();if(!Array.isArray(o))throw new TypeError(`${o} is not an array.`);const[i="",c=""]=o;switch(i){case s1:{if(n===Q&&!r.has(a))s.push(c);else{const l=rl(o,t);k(l)?s.push(l):s.push(c)}break}case i1:case l1:{s.push(c),a++,h1.test(c)&&r.add(a);break}case a1:{s.length&&s[s.length-1]===" "?s.splice(-1,1,c):s.push(c),r.has(a)&&r.delete(a),a--;break}case Yi:{if(s.length){const l=s[s.length-1];k(l)&&!l.endsWith("(")&&l!==" "&&s.push(c)}break}default:i!==Ki&&i!==o1&&s.push(c)}}return s},Jr=(e,t={})=>{const{format:n=""}=t;if(k(e)){if(f1.test(e)){if(n===Q)return e;{const c=qr(e,t);return k(c)?c:""}}else if(!u1.test(e))return e;e=e.toLowerCase().trim()}else throw new TypeError(`${e} is not a string.`);const r=we({namespace:Zi,name:"cssCalc",value:e},t),a=be(r);if(a instanceof le)return a.item;const s=Zt({css:e}),o=d1(s,t);let i=He(o.join(""),{toCanonicalUnits:!0});if(Ua.test(e)){if(fr.test(i)){const[,c,l]=i.match(fr);i=`${R(Number(c),Ha)}${l}`}i&&!Ua.test(i)&&n===Q&&(i=`calc(${i})`)}if(n===Q){if(/\s[-+*/]\s/.test(i)&&!i.includes("NaN"))i=nl(i,t);else if(Qi.test(i)){const[,c]=i.match(Qi);i=`calc(${R(Number(c),Ha)})`}}return x(r,i),i},m1="css-gradient",Ga=`${ze}(?:${Zn})`,al=`${Ga}|${Ge}`,g1=`${ze}(?:${Br})|0`,je=`${g1}|${Ge}`,sl=`${Ro}(?:${Br}|%)|0`,v1=`${Ro}(?:${Br})|0`,it="center",ao="left|right",so="top|bottom",Cn="start|end",vr=`${ao}|x-(?:${Cn})`,br=`${so}|y-(?:${Cn})`,Qr=`block-(?:${Cn})`,ea=`inline-(?:${Cn})`,b1=`${it}|${vr}|${br}|${Qr}|${ea}|${je}`,w1=[`(?:${it}|${vr})\\s+(?:${it}|${br})`,`(?:${it}|${br})\\s+(?:${it}|${vr})`,`(?:${it}|${vr}|${je})\\s+(?:${it}|${br}|${je})`,`(?:${it}|${Qr})\\s+(?:${it}|${ea})`,`(?:${it}|${ea})\\s+(?:${it}|${Qr})`,`(?:${it}|${Cn})\\s+(?:${it}|${Cn})`].join("|"),$1=[`(?:${vr})\\s+(?:${je})\\s+(?:${br})\\s+(?:${je})`,`(?:${br})\\s+(?:${je})\\s+(?:${vr})\\s+(?:${je})`,`(?:${Qr})\\s+(?:${je})\\s+(?:${ea})\\s+(?:${je})`,`(?:${ea})\\s+(?:${je})\\s+(?:${Qr})\\s+(?:${je})`,`(?:${Cn})\\s+(?:${je})\\s+(?:${Cn})\\s+(?:${je})`].join("|"),ol="(?:clos|farth)est-(?:corner|side)",ja=[`${ol}(?:\\s+${ol})?`,`${v1}`,`(?:${sl})\\s+(?:${sl})`].join("|"),qa="circle|ellipse",il=`from\\s+${Ga}`,on=`at\\s+(?:${b1}|${w1}|${$1})`,ll=`to\\s+(?:(?:${ao})(?:\\s(?:${so}))?|(?:${so})(?:\\s(?:${ao}))?)`,Mt=`in\\s+(?:${zo}|${Ho})`,cl=/^(?:repeating-)?(?:conic|linear|radial)-gradient\(/,y1=/^((?:repeating-)?(?:conic|linear|radial)-gradient)\(/,N1=e=>{if(k(e)&&(e=e.trim(),cl.test(e))){const[,t]=e.match(y1);return t}return""},E1=(e,t)=>{if(k(e)&&k(t)){e=e.trim(),t=t.trim();let n="";const r=[];if(/^(?:repeating-)?linear-gradient$/.test(t)?(n=[`(?:${Ga}|${ll})(?:\\s+${Mt})?`,`${Mt}(?:\\s+(?:${Ga}|${ll}))?`].join("|"),r.push(/to\s+bottom/)):/^(?:repeating-)?radial-gradient$/.test(t)?(n=[`(?:${qa})(?:\\s+(?:${ja}))?(?:\\s+${on})?(?:\\s+${Mt})?`,`(?:${ja})(?:\\s+(?:${qa}))?(?:\\s+${on})?(?:\\s+${Mt})?`,`${on}(?:\\s+${Mt})?`,`${Mt}(?:\\s+${qa})(?:\\s+(?:${ja}))?(?:\\s+${on})?`,`${Mt}(?:\\s+${ja})(?:\\s+(?:${qa}))?(?:\\s+${on})?`,`${Mt}(?:\\s+${on})?`].join("|"),r.push(/ellipse/,/farthest-corner/,/at\s+center/)):/^(?:repeating-)?conic-gradient$/.test(t)&&(n=[`${il}(?:\\s+${on})?(?:\\s+${Mt})?`,`${on}(?:\\s+${Mt})?`,`${Mt}(?:\\s+${il})?(?:\\s+${on})?`].join("|"),r.push(/at\s+center/)),n){const a=new RegExp(`^(?:${n})$`).test(e);if(a){let s=e;for(const o of r)s=s.replace(o,"");return s=s.replace(/\s{2,}/g," ").trim(),{line:s,valid:a}}return{valid:a,line:e}}}return{line:e,valid:!1}},ul=(e,t,n={})=>{if(Array.isArray(e)&&e.length>1){const r=/^(?:repeating-)?conic-gradient$/.test(t)?al:je,a=new RegExp(`^(?:${r})$`),s=new RegExp(`(?:\\s+(?:${r})){1,2}$`),o=[],i=[];for(const c of e)if(k(c))if(a.test(c))o.push("hint"),i.push(c);else{const l=c.replace(s,"");if(wn(l,{format:Q})){const u=xe(l,n);o.push("color"),i.push(c.replace(l,u))}else return{colorStops:e,valid:!1}}return{valid:/^color(?:,(?:hint,)?color)+$/.test(o.join(",")),colorStops:i}}return{colorStops:e,valid:!1}},hl=(e,t={})=>{if(k(e)){e=e.trim();const n=we({namespace:m1,name:"parseGradient",value:e},t),r=be(n);if(r instanceof le)return r.isNull?null:r.item;const a=N1(e),s=e.replace(cl,"").replace(/\)$/,"");if(a&&s){const[o="",...i]=Bn(s,{delimiter:","}),c=/^(?:repeating-)?conic-gradient$/.test(a)?al:je,l=new RegExp(`(?:\\s+(?:${c})){1,2}$`);let u="";if(l.test(o)){const f=o.replace(l,"");if(wn(f,{format:Q})){const d=xe(f,t);u=o.replace(f,d)}}else wn(o,{format:Q})&&(u=xe(o,t));if(u){i.unshift(u);const{colorStops:f,valid:d}=ul(i,a,t);if(d){const b={value:e,type:a,colorStopList:f};return x(n,b),b}}else if(i.length>1){const{line:f,valid:d}=E1(o,a),{colorStops:b,valid:p}=ul(i,a,t);if(d&&p){const m={value:e,type:a,gradientLine:f,colorStopList:b};return x(n,m),m}}}return x(n,null),null}return null},C1=(e,t={})=>{const{format:n=oe}=t,r=hl(e,t);if(r){const{type:a="",gradientLine:s="",colorStopList:o=[]}=r;if(a&&Array.isArray(o)&&o.length>1)return s?`${a}(${s}, ${o.join(", ")})`:`${a}(${o.join(", ")})`}return n===Q?"":"none"},x1=(e,t={})=>hl(e,t)!==null,Vt="convert",F1=new RegExp($a),k1=new RegExp(jo),S1=new RegExp(Rr),ln=(e,t={})=>{if(k(e)){if(e=e.trim(),!e)return new A}else return new A;const n=we({namespace:Vt,name:"preProcess",value:e},t),r=be(n);if(r instanceof le)return r.isNull?r:r.item;if(S1.test(e)){const a=qr(e,t);if(k(a))e=a;else return x(n,null),new A}if(k1.test(e)){const a=ro(e,t);if(k(a))e=a;else return x(n,null),new A}else F1.test(e)&&(e=Jr(e,t));if(e.startsWith("color-mix")){const a=structuredClone(t);a.format=oe,a.nullable=!0;const s=xe(e,a);return x(n,s),s}return x(n,e),e},M1=e=>zr(e),A1=(e,t={})=>{if(k(e)){const o=ln(e,t);if(o instanceof A)return null;e=o.toLowerCase()}else throw new TypeError(`${e} is not a string.`);const{alpha:n=!1}=t,r=we({namespace:Vt,name:"colorToHex",value:e},t),a=be(r);if(a instanceof le)return a.isNull?null:a.item;let s;return t.nullable=!0,n?(t.format="hexAlpha",s=xe(e,t)):(t.format="hex",s=xe(e,t)),k(s)?(x(r,s),s):(x(r,null),null)},P1=(e,t={})=>{if(k(e)){const s=ln(e,t);if(s instanceof A)return[0,0,0,0];e=s.toLowerCase()}else throw new TypeError(`${e} is not a string.`);const n=we({namespace:Vt,name:"colorToHsl",value:e},t),r=be(n);if(r instanceof le)return r.item;t.format="hsl";const a=Ds(e,t);return x(n,a),a},D1=(e,t={})=>{if(k(e)){const s=ln(e,t);if(s instanceof A)return[0,0,0,0];e=s.toLowerCase()}else throw new TypeError(`${e} is not a string.`);const n=we({namespace:Vt,name:"colorToHwb",value:e},t),r=be(n);if(r instanceof le)return r.item;t.format="hwb";const a=Bs(e,t);return x(n,a),a},B1=(e,t={})=>{if(k(e)){const s=ln(e,t);if(s instanceof A)return[0,0,0,0];e=s.toLowerCase()}else throw new TypeError(`${e} is not a string.`);const n=we({namespace:Vt,name:"colorToLab",value:e},t),r=be(n);if(r instanceof le)return r.item;const a=Rs(e,t);return x(n,a),a},R1=(e,t={})=>{if(k(e)){const s=ln(e,t);if(s instanceof A)return[0,0,0,0];e=s.toLowerCase()}else throw new TypeError(`${e} is not a string.`);const n=we({namespace:Vt,name:"colorToLch",value:e},t),r=be(n);if(r instanceof le)return r.item;const a=Os(e,t);return x(n,a),a},O1=(e,t={})=>{if(k(e)){const s=ln(e,t);if(s instanceof A)return[0,0,0,0];e=s.toLowerCase()}else throw new TypeError(`${e} is not a string.`);const n=we({namespace:Vt,name:"colorToOklab",value:e},t),r=be(n);if(r instanceof le)return r.item;const a=Ws(e,t);return x(n,a),a},W1=(e,t={})=>{if(k(e)){const s=ln(e,t);if(s instanceof A)return[0,0,0,0];e=s.toLowerCase()}else throw new TypeError(`${e} is not a string.`);const n=we({namespace:Vt,name:"colorToOklch",value:e},t),r=be(n);if(r instanceof le)return r.item;const a=Ls(e,t);return x(n,a),a},L1=(e,t={})=>{if(k(e)){const s=ln(e,t);if(s instanceof A)return[0,0,0,0];e=s.toLowerCase()}else throw new TypeError(`${e} is not a string.`);const n=we({namespace:Vt,name:"colorToRgb",value:e},t),r=be(n);if(r instanceof le)return r.item;const a=jr(e,t);return x(n,a),a},fl=(e,t={})=>{if(k(e)){const s=ln(e,t);if(s instanceof A)return[0,0,0,0];e=s.toLowerCase()}else throw new TypeError(`${e} is not a string.`);const n=we({namespace:Vt,name:"colorToXyz",value:e},t),r=be(n);if(r instanceof le)return r.item;let a;return e.startsWith("color(")?[,...a]=$e(e,t):[,...a]=Ce(e,t),x(n,a),a},_1=(e,t={})=>(t.d50=!0,fl(e,t)),I1={colorToHex:A1,colorToHsl:P1,colorToHwb:D1,colorToLab:B1,colorToLch:R1,colorToOklab:O1,colorToOklch:W1,colorToRgb:L1,colorToXyz:fl,colorToXyzD50:_1,numberToHex:M1};/*!
 * CSS color - Resolve, parse, convert CSS color.
 * @license MIT
 * @copyright asamuzaK (Kazz)
 * @see {@link https://github.com/asamuzaK/cssColor/blob/main/LICENSE}
 */const T1={cssCalc:Jr,cssVar:_u,extractDashedIdent:n1,isColor:wn,isGradient:x1,resolveGradient:C1,splitValue:Bn};export{I1 as convert,U0 as resolve,T1 as utils};

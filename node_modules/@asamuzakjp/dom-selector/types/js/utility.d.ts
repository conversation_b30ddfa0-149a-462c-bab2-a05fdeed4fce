export function getType(o: object): string;
export function verifyArray(arr: any[], type: string): any[];
export function generateException(msg: string, name: string, globalObject?: object): DOMException;
export function findNestedHas(leaf: object): object | null;
export function findLogicalWithNestedHas(leaf: object): object | null;
export function filterNodesByAnB(nodes: Array<object>, anb: {
    a: number;
    b: number;
    reverse?: boolean;
}): Array<object>;
export function resolveContent(node: object): Array<object | boolean>;
export function traverseNode(node: object, walker: object, force?: boolean): object | null;
export function isCustomElement(node: object, opt?: object): boolean;
export function getSlottedTextContent(node: object): string | null;
export function getDirectionality(node: object): string | null;
export function findLangAttribute(node: object): string | null;
export function getCaseSensitivity(astFlags: string | null, contentType: string): boolean;
export function isContentEditable(node: object): boolean;
export function isVisible(node: object): boolean;
export function isFocusVisible(node: object): boolean;
export function isFocusableArea(node: object): boolean;
export function isFocusable(node: object): boolean;
export function getNamespaceURI(ns: string, node: object): string | null;
export function isNamespaceDeclared(ns?: string, node?: object): boolean;
export function isPreceding(nodeA: object, nodeB: object): boolean;
export function compareNodes(a: object, b: object): number;
export function sortNodes(nodes?: Array<object> | Set<object>): Array<object>;
export function concatNestedSelectors(selectors: Array<Array<string>>): string;
export function extractNestedSelectors(css: string): Array<Array<string>>;
export function findAttributeValues(node: object, opt?: object): Array<string>;
export function isValidShadowHostSelector(astName?: string, branches?: Array<Array<object>>): boolean;
export function initNwsapi(window: object, document: object): object;
export function filterSelector(selector: string, target: string): boolean;

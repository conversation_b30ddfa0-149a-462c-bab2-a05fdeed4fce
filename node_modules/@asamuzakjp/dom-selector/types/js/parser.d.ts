export function unescapeSelector(selector?: string): string;
export function preprocess(value: string): string;
export function parseSelector(sel: string): object;
export function walkAST(ast?: object): {
    branches: Array<object>;
    info: object;
};
export function compareASTNodes(a: object, b: object): number;
export function sortAST(asts: Array<object>): Array<object>;
export function parseAstName(selector: string): {
    prefix: string;
    localName: string;
};
export { find as findAST, generate as generateCSS } from "css-tree";

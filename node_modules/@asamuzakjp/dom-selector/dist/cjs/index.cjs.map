{"version": 3, "sources": ["../../src/index.js", "../../src/js/parser.js", "../../src/js/utility.js", "../../src/js/constant.js", "../../src/js/matcher.js", "../../src/js/finder.js"], "sourcesContent": ["/*!\n * DOM Selector - A CSS selector engine.\n * @license MIT\n * @copyright asamuzaK (Kazz)\n * @see {@link https://github.com/asamuzaK/domSelector/blob/main/LICENSE}\n */\n\n/* import */\nimport { LRUCache } from 'lru-cache';\nimport { Finder } from './js/finder.js';\nimport { filterSelector, getType, initNwsapi } from './js/utility.js';\n\n/* constants */\nimport {\n  DOCUMENT_NODE,\n  ELEMENT_NODE,\n  TARGET_ALL,\n  TARGET_FIRST,\n  TARGET_LINEAL,\n  TARGET_SELF\n} from './js/constant.js';\nconst MAX_CACHE = 1024;\n\n/* DOMSelector */\nexport class DOMSelector {\n  /* private fields */\n  #window;\n  #document;\n  #finder;\n  #idlUtils;\n  #nwsapi;\n  #cache;\n\n  /**\n   * construct\n   * @param {object} window - window\n   * @param {object} document - document\n   * @param {object} [opt] - options\n   */\n  constructor(window, document, opt = {}) {\n    const { idlUtils } = opt;\n    this.#window = window;\n    this.#document = document ?? window.document;\n    this.#finder = new Finder(window);\n    this.#idlUtils = idlUtils;\n    this.#nwsapi = initNwsapi(window, document);\n    this.#cache = new LRUCache({\n      max: MAX_CACHE\n    });\n  }\n\n  /**\n   * @typedef CheckResult\n   * @type {object}\n   * @property {boolean} match - match result excluding pseudo-element selector\n   * @property {string?} pseudoElement - pseudo-element selector\n   */\n\n  /**\n   * check\n   * @param {string} selector - CSS selector\n   * @param {object} node - Element node\n   * @param {object} [opt] - options\n   * @returns {CheckResult} - check result\n   */\n  check(selector, node, opt = {}) {\n    if (!node?.nodeType) {\n      const e = new this.#window.TypeError(`Unexpected type ${getType(node)}`);\n      return this.#finder.onError(e, opt);\n    } else if (node.nodeType !== ELEMENT_NODE) {\n      const e = new this.#window.TypeError(`Unexpected node ${node.nodeName}`);\n      return this.#finder.onError(e, opt);\n    }\n    if (this.#idlUtils) {\n      node = this.#idlUtils.wrapperForImpl(node);\n    }\n    const document = node.ownerDocument;\n    if (\n      document === this.#document &&\n      document.contentType === 'text/html' &&\n      document.documentElement &&\n      node.parentNode\n    ) {\n      const cacheKey = `check_${selector}`;\n      let filterMatches = false;\n      if (this.#cache.has(cacheKey)) {\n        filterMatches = this.#cache.get(cacheKey);\n      } else {\n        filterMatches = filterSelector(selector, TARGET_SELF);\n        this.#cache.set(cacheKey, filterMatches);\n      }\n      if (filterMatches) {\n        try {\n          const match = this.#nwsapi.match(selector, node);\n          return {\n            match,\n            pseudoElement: null\n          };\n        } catch (e) {\n          // fall through\n        }\n      }\n    }\n    let res;\n    try {\n      opt.check = true;\n      opt.noexept = true;\n      opt.warn = false;\n      this.#finder.setup(selector, node, opt);\n      res = this.#finder.find(TARGET_SELF);\n    } catch (e) {\n      this.#finder.onError(e, opt);\n    }\n    return res;\n  }\n\n  /**\n   * matches\n   * @param {string} selector - CSS selector\n   * @param {object} node - Element node\n   * @param {object} [opt] - options\n   * @returns {boolean} - `true` if matched `false` otherwise\n   */\n  matches(selector, node, opt = {}) {\n    if (!node?.nodeType) {\n      const e = new this.#window.TypeError(`Unexpected type ${getType(node)}`);\n      return this.#finder.onError(e, opt);\n    } else if (node.nodeType !== ELEMENT_NODE) {\n      const e = new this.#window.TypeError(`Unexpected node ${node.nodeName}`);\n      return this.#finder.onError(e, opt);\n    }\n    if (this.#idlUtils) {\n      node = this.#idlUtils.wrapperForImpl(node);\n    }\n    const document = node.ownerDocument;\n    if (\n      document === this.#document &&\n      document.contentType === 'text/html' &&\n      document.documentElement &&\n      node.parentNode\n    ) {\n      const cacheKey = `matches_${selector}`;\n      let filterMatches = false;\n      if (this.#cache.has(cacheKey)) {\n        filterMatches = this.#cache.get(cacheKey);\n      } else {\n        filterMatches = filterSelector(selector, TARGET_SELF);\n        this.#cache.set(cacheKey, filterMatches);\n      }\n      if (filterMatches) {\n        try {\n          const res = this.#nwsapi.match(selector, node);\n          return res;\n        } catch (e) {\n          // fall through\n        }\n      }\n    }\n    let res;\n    try {\n      this.#finder.setup(selector, node, opt);\n      const nodes = this.#finder.find(TARGET_SELF);\n      res = nodes.size;\n    } catch (e) {\n      this.#finder.onError(e, opt);\n    }\n    return !!res;\n  }\n\n  /**\n   * closest\n   * @param {string} selector - CSS selector\n   * @param {object} node - Element node\n   * @param {object} [opt] - options\n   * @returns {?object} - matched node\n   */\n  closest(selector, node, opt = {}) {\n    if (!node?.nodeType) {\n      const e = new this.#window.TypeError(`Unexpected type ${getType(node)}`);\n      return this.#finder.onError(e, opt);\n    } else if (node.nodeType !== ELEMENT_NODE) {\n      const e = new this.#window.TypeError(`Unexpected node ${node.nodeName}`);\n      return this.#finder.onError(e, opt);\n    }\n    if (this.#idlUtils) {\n      node = this.#idlUtils.wrapperForImpl(node);\n    }\n    const document = node.ownerDocument;\n    if (\n      document === this.#document &&\n      document.contentType === 'text/html' &&\n      document.documentElement &&\n      node.parentNode\n    ) {\n      const cacheKey = `closest_${selector}`;\n      let filterMatches = false;\n      if (this.#cache.has(cacheKey)) {\n        filterMatches = this.#cache.get(cacheKey);\n      } else {\n        filterMatches = filterSelector(selector, TARGET_LINEAL);\n        this.#cache.set(cacheKey, filterMatches);\n      }\n      if (filterMatches) {\n        try {\n          const res = this.#nwsapi.closest(selector, node);\n          return res;\n        } catch (e) {\n          // fall through\n        }\n      }\n    }\n    let res;\n    try {\n      this.#finder.setup(selector, node, opt);\n      const nodes = this.#finder.find(TARGET_LINEAL);\n      if (nodes.size) {\n        let refNode = node;\n        while (refNode) {\n          if (nodes.has(refNode)) {\n            res = refNode;\n            break;\n          }\n          refNode = refNode.parentNode;\n        }\n      }\n    } catch (e) {\n      this.#finder.onError(e, opt);\n    }\n    return res ?? null;\n  }\n\n  /**\n   * query selector\n   * @param {string} selector - CSS selector\n   * @param {object} node - Document, DocumentFragment, Element node\n   * @param {object} [opt] - options\n   * @returns {?object} - matched node\n   */\n  querySelector(selector, node, opt = {}) {\n    if (!node?.nodeType) {\n      const e = new this.#window.TypeError(`Unexpected type ${getType(node)}`);\n      return this.#finder.onError(e, opt);\n    }\n    if (this.#idlUtils) {\n      node = this.#idlUtils.wrapperForImpl(node);\n    }\n    let res;\n    try {\n      this.#finder.setup(selector, node, opt);\n      const nodes = this.#finder.find(TARGET_FIRST);\n      if (nodes.size) {\n        [res] = [...nodes];\n      }\n    } catch (e) {\n      this.#finder.onError(e, opt);\n    }\n    return res ?? null;\n  }\n\n  /**\n   * query selector all\n   * NOTE: returns Array, not NodeList\n   * @param {string} selector - CSS selector\n   * @param {object} node - Document, DocumentFragment, Element node\n   * @param {object} [opt] - options\n   * @returns {Array.<object|undefined>} - collection of matched nodes\n   */\n  querySelectorAll(selector, node, opt = {}) {\n    if (!node?.nodeType) {\n      const e = new this.#window.TypeError(`Unexpected type ${getType(node)}`);\n      return this.#finder.onError(e, opt);\n    }\n    if (this.#idlUtils) {\n      node = this.#idlUtils.wrapperForImpl(node);\n    }\n    let document;\n    if (node.nodeType === DOCUMENT_NODE) {\n      document = node;\n    } else {\n      document = node.ownerDocument;\n    }\n    if (\n      document === this.#document &&\n      document.contentType === 'text/html' &&\n      document.documentElement\n    ) {\n      const cacheKey = `querySelectorAll_${selector}`;\n      let filterMatches = false;\n      if (this.#cache.has(cacheKey)) {\n        filterMatches = this.#cache.get(cacheKey);\n      } else {\n        filterMatches = filterSelector(selector, TARGET_ALL);\n        this.#cache.set(cacheKey, filterMatches);\n      }\n      if (filterMatches) {\n        try {\n          const res = this.#nwsapi.select(selector, node);\n          return res;\n        } catch (e) {\n          // fall through\n        }\n      }\n    }\n    let res;\n    try {\n      this.#finder.setup(selector, node, opt);\n      const nodes = this.#finder.find(TARGET_ALL);\n      if (nodes.size) {\n        res = [...nodes];\n      }\n    } catch (e) {\n      this.#finder.onError(e, opt);\n    }\n    return res ?? [];\n  }\n}\n", "/**\n * parser.js\n */\n\n/* import */\nimport * as cssTree from 'css-tree';\nimport { getType } from './utility.js';\n\n/* constants */\nimport {\n  ATTR_SELECTOR,\n  BIT_01,\n  BIT_02,\n  BIT_04,\n  BIT_08,\n  BIT_16,\n  BIT_32,\n  BIT_FFFF,\n  CLASS_SELECTOR,\n  DUO,\n  HEX,\n  ID_SELECTOR,\n  KEY_LOGICAL,\n  KEY_PS_CLASS_STATE,\n  KEY_SHADOW_HOST,\n  NTH,\n  PS_CLASS_SELECTOR,\n  PS_ELEMENT_SELECTOR,\n  SELECTOR,\n  SYNTAX_ERR,\n  TYPE_SELECTOR\n} from './constant.js';\nconst AST_SORT_ORDER = new Map([\n  [PS_ELEMENT_SELECTOR, BIT_01],\n  [ID_SELECTOR, BIT_02],\n  [CLASS_SELECTOR, BIT_04],\n  [TYPE_SELECTOR, BIT_08],\n  [ATTR_SELECTOR, BIT_16],\n  [PS_CLASS_SELECTOR, BIT_32]\n]);\nconst REG_EMPTY_PS_FUNC =\n  /(?<=:(?:dir|has|host(?:-context)?|is|lang|not|nth-(?:last-)?(?:child|of-type)|where))\\(\\s+\\)/g;\nconst REG_SHADOW_PS_ELEMENT = /^part|slotted$/;\nconst U_FFFD = '\\uFFFD';\n\n/**\n * Unescapes a CSS selector string.\n * @param {string} selector - The CSS selector to unescape.\n * @returns {string} The unescaped selector string.\n */\nexport const unescapeSelector = (selector = '') => {\n  if (typeof selector === 'string' && selector.indexOf('\\\\', 0) >= 0) {\n    const arr = selector.split('\\\\');\n    const selectorItems = [arr[0]];\n    const l = arr.length;\n    for (let i = 1; i < l; i++) {\n      const item = arr[i];\n      if (item === '' && i === l - 1) {\n        selectorItems.push(U_FFFD);\n      } else {\n        const hexExists = /^([\\da-f]{1,6}\\s?)/i.exec(item);\n        if (hexExists) {\n          const [, hex] = hexExists;\n          let str;\n          try {\n            const low = parseInt('D800', HEX);\n            const high = parseInt('DFFF', HEX);\n            const deci = parseInt(hex, HEX);\n            if (deci === 0 || (deci >= low && deci <= high)) {\n              str = U_FFFD;\n            } else {\n              str = String.fromCodePoint(deci);\n            }\n          } catch (e) {\n            str = U_FFFD;\n          }\n          let postStr = '';\n          if (item.length > hex.length) {\n            postStr = item.substring(hex.length);\n          }\n          selectorItems.push(`${str}${postStr}`);\n          // whitespace\n        } else if (/^[\\n\\r\\f]/.test(item)) {\n          selectorItems.push(`\\\\${item}`);\n        } else {\n          selectorItems.push(item);\n        }\n      }\n    }\n    return selectorItems.join('');\n  }\n  return selector;\n};\n\n/**\n * Preprocesses a selector string according to the specification.\n * @see https://drafts.csswg.org/css-syntax-3/#input-preprocessing\n * @param {string} value - The value to preprocess.\n * @returns {string} The preprocessed selector string.\n */\nexport const preprocess = value => {\n  // Non-string values will be converted to string.\n  if (typeof value !== 'string') {\n    if (value === undefined || value === null) {\n      return getType(value).toLowerCase();\n    } else if (Array.isArray(value)) {\n      return value.join(',');\n    } else if (Object.hasOwn(value, 'toString')) {\n      return value.toString();\n    } else {\n      throw new DOMException(`Invalid selector ${value}`, SYNTAX_ERR);\n    }\n  }\n  let selector = value;\n  let index = 0;\n  while (index >= 0) {\n    // @see https://drafts.csswg.org/selectors/#id-selectors\n    index = selector.indexOf('#', index);\n    if (index < 0) {\n      break;\n    }\n    const preHash = selector.substring(0, index + 1);\n    let postHash = selector.substring(index + 1);\n    const codePoint = postHash.codePointAt(0);\n    if (codePoint > BIT_FFFF) {\n      const str = `\\\\${codePoint.toString(HEX)} `;\n      if (postHash.length === DUO) {\n        postHash = str;\n      } else {\n        postHash = `${str}${postHash.substring(DUO)}`;\n      }\n    }\n    selector = `${preHash}${postHash}`;\n    index++;\n  }\n  return selector\n    .replace(/\\f|\\r\\n?/g, '\\n')\n    .replace(/[\\0\\uD800-\\uDFFF]|\\\\$/g, U_FFFD)\n    .replace(/\\x26/g, ':scope');\n};\n\n/**\n * Creates an Abstract Syntax Tree (AST) from a CSS selector string.\n * @param {string} sel - The CSS selector string.\n * @returns {object} The parsed AST object.\n */\nexport const parseSelector = sel => {\n  const selector = preprocess(sel);\n  // invalid selectors\n  if (/^$|^\\s*>|,\\s*$/.test(selector)) {\n    throw new DOMException(`Invalid selector ${selector}`, SYNTAX_ERR);\n  }\n  try {\n    const ast = cssTree.parse(selector, {\n      context: 'selectorList',\n      parseCustomProperty: true\n    });\n    return cssTree.toPlainObject(ast);\n  } catch (e) {\n    const { message } = e;\n    if (\n      /^(?:\"\\]\"|Attribute selector [()\\s,=~^$*|]+) is expected$/.test(\n        message\n      ) &&\n      !selector.endsWith(']')\n    ) {\n      const index = selector.lastIndexOf('[');\n      const selPart = selector.substring(index);\n      if (selPart.includes('\"')) {\n        const quotes = selPart.match(/\"/g).length;\n        if (quotes % 2) {\n          return parseSelector(`${selector}\"]`);\n        }\n        return parseSelector(`${selector}]`);\n      }\n      return parseSelector(`${selector}]`);\n    } else if (message === '\")\" is expected') {\n      // workaround for https://github.com/csstree/csstree/issues/283\n      if (REG_EMPTY_PS_FUNC.test(selector)) {\n        return parseSelector(`${selector.replaceAll(REG_EMPTY_PS_FUNC, '()')}`);\n      } else if (!selector.endsWith(')')) {\n        return parseSelector(`${selector})`);\n      } else {\n        throw new DOMException(`Invalid selector ${selector}`, SYNTAX_ERR);\n      }\n    } else {\n      throw new DOMException(`Invalid selector ${selector}`, SYNTAX_ERR);\n    }\n  }\n};\n\n/**\n * Walks the provided AST to collect selector branches and gather information\n * about its contents.\n * @param {object} ast - The AST to traverse.\n * @returns {{branches: Array<object>, info: object}} An object containing the selector branches and info.\n */\nexport const walkAST = (ast = {}) => {\n  const branches = new Set();\n  const info = {\n    hasHasPseudoFunc: false,\n    hasLogicalPseudoFunc: false,\n    hasNthChildOfSelector: false,\n    hasNestedSelector: false,\n    hasStatePseudoClass: false\n  };\n  const opt = {\n    enter(node) {\n      switch (node.type) {\n        case CLASS_SELECTOR: {\n          if (/^-?\\d/.test(node.name)) {\n            throw new DOMException(\n              `Invalid selector .${node.name}`,\n              SYNTAX_ERR\n            );\n          }\n          break;\n        }\n        case ID_SELECTOR: {\n          if (/^-?\\d/.test(node.name)) {\n            throw new DOMException(\n              `Invalid selector #${node.name}`,\n              SYNTAX_ERR\n            );\n          }\n          break;\n        }\n        case PS_CLASS_SELECTOR: {\n          if (KEY_LOGICAL.has(node.name)) {\n            info.hasNestedSelector = true;\n            info.hasLogicalPseudoFunc = true;\n            if (node.name === 'has') {\n              info.hasHasPseudoFunc = true;\n            }\n          } else if (KEY_PS_CLASS_STATE.has(node.name)) {\n            info.hasStatePseudoClass = true;\n          } else if (\n            KEY_SHADOW_HOST.has(node.name) &&\n            Array.isArray(node.children) &&\n            node.children.length\n          ) {\n            info.hasNestedSelector = true;\n          }\n          break;\n        }\n        case PS_ELEMENT_SELECTOR: {\n          if (REG_SHADOW_PS_ELEMENT.test(node.name)) {\n            info.hasNestedSelector = true;\n          }\n          break;\n        }\n        case NTH: {\n          if (node.selector) {\n            info.hasNestedSelector = true;\n            info.hasNthChildOfSelector = true;\n          }\n          break;\n        }\n        case SELECTOR: {\n          branches.add(node.children);\n          break;\n        }\n        default:\n      }\n    }\n  };\n  cssTree.walk(ast, opt);\n  if (info.hasNestedSelector === true) {\n    cssTree.findAll(ast, (node, item, list) => {\n      if (list) {\n        if (node.type === PS_CLASS_SELECTOR && KEY_LOGICAL.has(node.name)) {\n          const itemList = list.filter(i => {\n            const { name, type } = i;\n            return type === PS_CLASS_SELECTOR && KEY_LOGICAL.has(name);\n          });\n          for (const { children } of itemList) {\n            // SelectorList\n            for (const { children: grandChildren } of children) {\n              // Selector\n              for (const { children: greatGrandChildren } of grandChildren) {\n                if (branches.has(greatGrandChildren)) {\n                  branches.delete(greatGrandChildren);\n                }\n              }\n            }\n          }\n        } else if (\n          node.type === PS_CLASS_SELECTOR &&\n          KEY_SHADOW_HOST.has(node.name) &&\n          Array.isArray(node.children) &&\n          node.children.length\n        ) {\n          const itemList = list.filter(i => {\n            const { children, name, type } = i;\n            const res =\n              type === PS_CLASS_SELECTOR &&\n              KEY_SHADOW_HOST.has(name) &&\n              Array.isArray(children) &&\n              children.length;\n            return res;\n          });\n          for (const { children } of itemList) {\n            // Selector\n            for (const { children: grandChildren } of children) {\n              if (branches.has(grandChildren)) {\n                branches.delete(grandChildren);\n              }\n            }\n          }\n        } else if (\n          node.type === PS_ELEMENT_SELECTOR &&\n          REG_SHADOW_PS_ELEMENT.test(node.name)\n        ) {\n          const itemList = list.filter(i => {\n            const { name, type } = i;\n            const res =\n              type === PS_ELEMENT_SELECTOR && REG_SHADOW_PS_ELEMENT.test(name);\n            return res;\n          });\n          for (const { children } of itemList) {\n            // Selector\n            for (const { children: grandChildren } of children) {\n              if (branches.has(grandChildren)) {\n                branches.delete(grandChildren);\n              }\n            }\n          }\n        } else if (node.type === NTH && node.selector) {\n          const itemList = list.filter(i => {\n            const { selector, type } = i;\n            const res = type === NTH && selector;\n            return res;\n          });\n          for (const { selector } of itemList) {\n            const { children } = selector;\n            // Selector\n            for (const { children: grandChildren } of children) {\n              if (branches.has(grandChildren)) {\n                branches.delete(grandChildren);\n              }\n            }\n          }\n        }\n      }\n    });\n  }\n  return {\n    info,\n    branches: [...branches]\n  };\n};\n\n/**\n * Comparison function for sorting AST nodes based on specificity.\n * @param {object} a - The first AST node.\n * @param {object} b - The second AST node.\n * @returns {number} -1, 0 or 1, depending on the sort order.\n */\nexport const compareASTNodes = (a, b) => {\n  const bitA = AST_SORT_ORDER.get(a.type);\n  const bitB = AST_SORT_ORDER.get(b.type);\n  if (bitA === bitB) {\n    return 0;\n  } else if (bitA > bitB) {\n    return 1;\n  } else {\n    return -1;\n  }\n};\n\n/**\n * Sorts a collection of AST nodes based on CSS specificity rules.\n * @param {Array<object>} asts - A collection of AST nodes to sort.\n * @returns {Array<object>} A new array containing the sorted AST nodes.\n */\nexport const sortAST = asts => {\n  const arr = [...asts];\n  if (arr.length > 1) {\n    arr.sort(compareASTNodes);\n  }\n  return arr;\n};\n\n/**\n * Parses a type selector's name, which may include a namespace prefix.\n * @param {string} selector - The type selector name (e.g., 'ns|E' or 'E').\n * @returns {{prefix: string, localName: string}} An object with `prefix` and\n * `localName` properties.\n */\nexport const parseAstName = selector => {\n  let prefix;\n  let localName;\n  if (selector && typeof selector === 'string') {\n    if (selector.indexOf('|') > -1) {\n      [prefix, localName] = selector.split('|');\n    } else {\n      prefix = '*';\n      localName = selector;\n    }\n  } else {\n    throw new DOMException(`Invalid selector ${selector}`, SYNTAX_ERR);\n  }\n  return {\n    prefix,\n    localName\n  };\n};\n\n/* Re-exported from css-tree. */\nexport { find as findAST, generate as generateCSS } from 'css-tree';\n", "/**\n * utility.js\n */\n\n/* import */\nimport nwsapi from '@asamuzakjp/nwsapi';\nimport bidiFactory from 'bidi-js';\nimport * as cssTree from 'css-tree';\nimport isCustomElementName from 'is-potential-custom-element-name';\n\n/* constants */\nimport {\n  ATRULE,\n  COMBO,\n  COMPOUND_I,\n  DESCEND,\n  DOCUMENT_FRAGMENT_NODE,\n  DOCUMENT_NODE,\n  DOCUMENT_POSITION_CONTAINS,\n  DOCUMENT_POSITION_PRECEDING,\n  ELEMENT_NODE,\n  HAS_COMPOUND,\n  INPUT_BUTTON,\n  INPUT_EDIT,\n  INPUT_LTR,\n  INPUT_TEXT,\n  KEY_LOGICAL,\n  LOGIC_COMPLEX,\n  LOGIC_COMPOUND,\n  N_TH,\n  PSEUDO_CLASS,\n  PS_CLASS_SELECTOR,\n  RULE,\n  SCOPE,\n  SELECTOR_LIST,\n  SIBLING,\n  SUB_CLASS,\n  TARGET_ALL,\n  TARGET_FIRST,\n  TEXT_NODE,\n  TYPE_FROM,\n  TYPE_TO\n} from './constant.js';\nconst KEY_DIR_AUTO = new Set([...INPUT_BUTTON, ...INPUT_TEXT, 'hidden']);\nconst KEY_DIR_LTR = new Set(INPUT_LTR);\nconst KEY_INPUT_EDIT = new Set(INPUT_EDIT);\nconst KEY_NODE_DIR_EXCLUDE = new Set(['bdi', 'script', 'style', 'textarea']);\nconst KEY_NODE_FOCUSABLE = new Set(['button', 'select', 'textarea']);\nconst KEY_NODE_FOCUSABLE_SVG = new Set([\n  'clipPath',\n  'defs',\n  'desc',\n  'linearGradient',\n  'marker',\n  'mask',\n  'metadata',\n  'pattern',\n  'radialGradient',\n  'script',\n  'style',\n  'symbol',\n  'title'\n]);\nconst REG_EXCLUDE_BASIC =\n  /[|\\\\]|::|[^\\u0021-\\u007F\\s]|\\[\\s*[\\w$*=^|~-]+(?:(?:\"[\\w$*=^|~\\s'-]+\"|'[\\w$*=^|~\\s\"-]+')?(?:\\s+[\\w$*=^|~-]+)+|\"[^\"\\]]{1,255}|'[^'\\]]{1,255})\\s*\\]|:(?:is|where)\\(\\s*\\)/;\nconst REG_SIMPLE_CLASS = new RegExp(`^${SUB_CLASS}$`);\nconst REG_COMPLEX = new RegExp(`${COMPOUND_I}${COMBO}${COMPOUND_I}`, 'i');\nconst REG_DESCEND = new RegExp(`${COMPOUND_I}${DESCEND}${COMPOUND_I}`, 'i');\nconst REG_SIBLING = new RegExp(`${COMPOUND_I}${SIBLING}${COMPOUND_I}`, 'i');\nconst REG_LOGIC_COMPLEX = new RegExp(\n  `:(?!${PSEUDO_CLASS}|${N_TH}|${LOGIC_COMPLEX})`\n);\nconst REG_LOGIC_COMPOUND = new RegExp(\n  `:(?!${PSEUDO_CLASS}|${N_TH}|${LOGIC_COMPOUND})`\n);\nconst REG_LOGIC_HAS_COMPOUND = new RegExp(\n  `:(?!${PSEUDO_CLASS}|${N_TH}|${LOGIC_COMPOUND}|${HAS_COMPOUND})`\n);\nconst REG_END_WITH_HAS = new RegExp(`:${HAS_COMPOUND}$`);\nconst REG_WO_LOGICAL = new RegExp(`:(?!${PSEUDO_CLASS}|${N_TH})`);\n\n/**\n * Manages state for extracting nested selectors from a CSS AST.\n */\nclass SelectorExtractor {\n  constructor() {\n    this.selectors = [];\n    this.isScoped = false;\n  }\n\n  /**\n   * Walker enter function.\n   * @param {object} node - The AST node.\n   */\n  enter(node) {\n    switch (node.type) {\n      case ATRULE: {\n        if (node.name === 'scope') {\n          this.isScoped = true;\n        }\n        break;\n      }\n      case SCOPE: {\n        const { children, type } = node.root;\n        const arr = [];\n        if (type === SELECTOR_LIST) {\n          for (const child of children) {\n            const selector = cssTree.generate(child);\n            arr.push(selector);\n          }\n          this.selectors.push(arr);\n        }\n        break;\n      }\n      case RULE: {\n        const { children, type } = node.prelude;\n        const arr = [];\n        if (type === SELECTOR_LIST) {\n          let hasAmp = false;\n          for (const child of children) {\n            const selector = cssTree.generate(child);\n            if (this.isScoped && !hasAmp) {\n              hasAmp = /\\x26/.test(selector);\n            }\n            arr.push(selector);\n          }\n          if (this.isScoped) {\n            if (hasAmp) {\n              this.selectors.push(arr);\n              /* FIXME:\n              } else {\n                this.selectors = arr;\n                this.isScoped = false;\n              */\n            }\n          } else {\n            this.selectors.push(arr);\n          }\n        }\n      }\n    }\n  }\n\n  /**\n   * Walker leave function.\n   * @param {object} node - The AST node.\n   */\n  leave(node) {\n    if (node.type === ATRULE) {\n      if (node.name === 'scope') {\n        this.isScoped = false;\n      }\n    }\n  }\n}\n\n/**\n * Get type of an object.\n * @param {object} o - Object to check.\n * @returns {string} - Type of the object.\n */\nexport const getType = o =>\n  Object.prototype.toString.call(o).slice(TYPE_FROM, TYPE_TO);\n\n/**\n * Verify array contents.\n * @param {Array} arr - The array.\n * @param {string} type - Expected type, e.g. 'String'.\n * @throws {TypeError} - Throws if array or its items are of unexpected type.\n * @returns {Array} - The verified array.\n */\nexport const verifyArray = (arr, type) => {\n  if (!Array.isArray(arr)) {\n    throw new TypeError(`Unexpected type ${getType(arr)}`);\n  }\n  if (typeof type !== 'string') {\n    throw new TypeError(`Unexpected type ${getType(type)}`);\n  }\n  for (const item of arr) {\n    if (getType(item) !== type) {\n      throw new TypeError(`Unexpected type ${getType(item)}`);\n    }\n  }\n  return arr;\n};\n\n/**\n * Generate a DOMException.\n * @param {string} msg - The error message.\n * @param {string} name - The error name.\n * @param {object} globalObject - The global object (e.g., window).\n * @returns {DOMException} The generated DOMException object.\n */\nexport const generateException = (msg, name, globalObject = globalThis) => {\n  return new globalObject.DOMException(msg, name);\n};\n\n/**\n * Find a nested :has() pseudo-class.\n * @param {object} leaf - The AST leaf to check.\n * @returns {?object} The leaf if it's :has, otherwise null.\n */\nexport const findNestedHas = leaf => {\n  return leaf.name === 'has';\n};\n\n/**\n * Find a logical pseudo-class that contains a nested :has().\n * @param {object} leaf - The AST leaf to check.\n * @returns {?object} The leaf if it matches, otherwise null.\n */\nexport const findLogicalWithNestedHas = leaf => {\n  if (KEY_LOGICAL.has(leaf.name) && cssTree.find(leaf, findNestedHas)) {\n    return leaf;\n  }\n  return null;\n};\n\n/**\n * Filter a list of nodes based on An+B logic\n * @param {Array.<object>} nodes - array of nodes to filter\n * @param {object} anb - An+B options\n * @param {number} anb.a - a\n * @param {number} anb.b - b\n * @param {boolean} [anb.reverse] - reverse order\n * @returns {Array.<object>} - array of matched nodes\n */\nexport const filterNodesByAnB = (nodes, anb) => {\n  const { a, b, reverse } = anb;\n  const processedNodes = reverse ? [...nodes].reverse() : nodes;\n  const l = nodes.length;\n  const matched = [];\n  if (a === 0) {\n    if (b > 0 && b <= l) {\n      matched.push(processedNodes[b - 1]);\n    }\n    return matched;\n  }\n  let startIndex = b - 1;\n  if (a > 0) {\n    while (startIndex < 0) {\n      startIndex += a;\n    }\n    for (let i = startIndex; i < l; i += a) {\n      matched.push(processedNodes[i]);\n    }\n  } else if (startIndex >= 0) {\n    for (let i = startIndex; i >= 0; i += a) {\n      matched.push(processedNodes[i]);\n    }\n    return matched.reverse();\n  }\n  return matched;\n};\n\n/**\n * Resolve content document, root node, and check if it's in a shadow DOM.\n * @param {object} node - Document, DocumentFragment, or Element node.\n * @returns {Array.<object|boolean>} - [document, root, isInShadow].\n */\nexport const resolveContent = node => {\n  if (!node?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(node)}`);\n  }\n  let document;\n  let root;\n  let shadow;\n  switch (node.nodeType) {\n    case DOCUMENT_NODE: {\n      document = node;\n      root = node;\n      break;\n    }\n    case DOCUMENT_FRAGMENT_NODE: {\n      const { host, mode, ownerDocument } = node;\n      document = ownerDocument;\n      root = node;\n      shadow = host && (mode === 'close' || mode === 'open');\n      break;\n    }\n    case ELEMENT_NODE: {\n      document = node.ownerDocument;\n      let refNode = node;\n      while (refNode) {\n        const { host, mode, nodeType, parentNode } = refNode;\n        if (nodeType === DOCUMENT_FRAGMENT_NODE) {\n          shadow = host && (mode === 'close' || mode === 'open');\n          break;\n        } else if (parentNode) {\n          refNode = parentNode;\n        } else {\n          break;\n        }\n      }\n      root = refNode;\n      break;\n    }\n    default: {\n      throw new TypeError(`Unexpected node ${node.nodeName}`);\n    }\n  }\n  return [document, root, !!shadow];\n};\n\n/**\n * Traverse node tree with a TreeWalker.\n * @param {object} node - The target node.\n * @param {object} walker - The TreeWalker instance.\n * @param {boolean} [force] - Traverse only to the next node.\n * @returns {?object} - The current node if found, otherwise null.\n */\nexport const traverseNode = (node, walker, force = false) => {\n  if (!node?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(node)}`);\n  }\n  if (!walker) {\n    return null;\n  }\n  let refNode = walker.currentNode;\n  if (refNode === node) {\n    return refNode;\n  } else if (force || refNode.contains(node)) {\n    refNode = walker.nextNode();\n    while (refNode) {\n      if (refNode === node) {\n        break;\n      }\n      refNode = walker.nextNode();\n    }\n    return refNode;\n  } else {\n    if (refNode !== walker.root) {\n      let bool;\n      while (refNode) {\n        if (refNode === node) {\n          bool = true;\n          break;\n        } else if (refNode === walker.root || refNode.contains(node)) {\n          break;\n        }\n        refNode = walker.parentNode();\n      }\n      if (bool) {\n        return refNode;\n      }\n    }\n    if (node.nodeType === ELEMENT_NODE) {\n      let bool;\n      while (refNode) {\n        if (refNode === node) {\n          bool = true;\n          break;\n        }\n        refNode = walker.nextNode();\n      }\n      if (bool) {\n        return refNode;\n      }\n    }\n  }\n  return null;\n};\n\n/**\n * Check if a node is a custom element.\n * @param {object} node - The Element node.\n * @param {object} [opt] - Options.\n * @returns {boolean} - True if it's a custom element.\n */\nexport const isCustomElement = (node, opt = {}) => {\n  if (!node?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(node)}`);\n  }\n  if (node.nodeType !== ELEMENT_NODE) {\n    return false;\n  }\n  const { localName, ownerDocument } = node;\n  const { formAssociated } = opt;\n  const window = ownerDocument.defaultView;\n  let elmConstructor;\n  const attr = node.getAttribute('is');\n  if (attr) {\n    elmConstructor =\n      isCustomElementName(attr) && window.customElements.get(attr);\n  } else {\n    elmConstructor =\n      isCustomElementName(localName) && window.customElements.get(localName);\n  }\n  if (elmConstructor) {\n    if (formAssociated) {\n      return !!elmConstructor.formAssociated;\n    }\n    return true;\n  }\n  return false;\n};\n\n/**\n * Get slotted text content.\n * @param {object} node - The Element node (likely a <slot>).\n * @returns {?string} - The text content.\n */\nexport const getSlottedTextContent = node => {\n  if (!node?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(node)}`);\n  }\n  if (typeof node.assignedNodes !== 'function') {\n    return null;\n  }\n  const nodes = node.assignedNodes();\n  if (nodes.length) {\n    let text = '';\n    const l = nodes.length;\n    for (let i = 0; i < l; i++) {\n      const item = nodes[i];\n      text = item.textContent.trim();\n      if (text) {\n        break;\n      }\n    }\n    return text;\n  }\n  return node.textContent.trim();\n};\n\n/**\n * Get directionality of a node.\n * @see https://html.spec.whatwg.org/multipage/dom.html#the-dir-attribute\n * @param {object} node - The Element node.\n * @returns {?string} - 'ltr' or 'rtl'.\n */\nexport const getDirectionality = node => {\n  if (!node?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(node)}`);\n  }\n  if (node.nodeType !== ELEMENT_NODE) {\n    return null;\n  }\n  const { dir: dirAttr, localName, parentNode } = node;\n  const { getEmbeddingLevels } = bidiFactory();\n  if (dirAttr === 'ltr' || dirAttr === 'rtl') {\n    return dirAttr;\n  } else if (dirAttr === 'auto') {\n    let text = '';\n    switch (localName) {\n      case 'input': {\n        if (!node.type || KEY_DIR_AUTO.has(node.type)) {\n          text = node.value;\n        } else if (KEY_DIR_LTR.has(node.type)) {\n          return 'ltr';\n        }\n        break;\n      }\n      case 'slot': {\n        text = getSlottedTextContent(node);\n        break;\n      }\n      case 'textarea': {\n        text = node.value;\n        break;\n      }\n      default: {\n        const items = [].slice.call(node.childNodes);\n        for (const item of items) {\n          const {\n            dir: itemDir,\n            localName: itemLocalName,\n            nodeType: itemNodeType,\n            textContent: itemTextContent\n          } = item;\n          if (itemNodeType === TEXT_NODE) {\n            text = itemTextContent.trim();\n          } else if (\n            itemNodeType === ELEMENT_NODE &&\n            !KEY_NODE_DIR_EXCLUDE.has(itemLocalName) &&\n            (!itemDir || (itemDir !== 'ltr' && itemDir !== 'rtl'))\n          ) {\n            if (itemLocalName === 'slot') {\n              text = getSlottedTextContent(item);\n            } else {\n              text = itemTextContent.trim();\n            }\n          }\n          if (text) {\n            break;\n          }\n        }\n      }\n    }\n    if (text) {\n      const {\n        paragraphs: [{ level }]\n      } = getEmbeddingLevels(text);\n      if (level % 2 === 1) {\n        return 'rtl';\n      }\n    } else if (parentNode) {\n      const { nodeType: parentNodeType } = parentNode;\n      if (parentNodeType === ELEMENT_NODE) {\n        return getDirectionality(parentNode);\n      }\n    }\n  } else if (localName === 'input' && node.type === 'tel') {\n    return 'ltr';\n  } else if (localName === 'bdi') {\n    const text = node.textContent.trim();\n    if (text) {\n      const {\n        paragraphs: [{ level }]\n      } = getEmbeddingLevels(text);\n      if (level % 2 === 1) {\n        return 'rtl';\n      }\n    }\n  } else if (parentNode) {\n    if (localName === 'slot') {\n      const text = getSlottedTextContent(node);\n      if (text) {\n        const {\n          paragraphs: [{ level }]\n        } = getEmbeddingLevels(text);\n        if (level % 2 === 1) {\n          return 'rtl';\n        }\n        return 'ltr';\n      }\n    }\n    const { nodeType: parentNodeType } = parentNode;\n    if (parentNodeType === ELEMENT_NODE) {\n      return getDirectionality(parentNode);\n    }\n  }\n  return 'ltr';\n};\n\n/**\n * Finds the effective language attribute by traversing up the DOM tree.\n * @param {object} node - The element node to start from.\n * @returns {?string} The language attribute string, or null if not found.\n */\nexport const findLangAttribute = node => {\n  if (!node?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(node)}`);\n  }\n  if (node.nodeType !== ELEMENT_NODE) {\n    return null;\n  }\n  const { contentType } = node.ownerDocument;\n  const isHtml = /^(?:application\\/xhtml\\+x|text\\/ht)ml$/.test(contentType);\n  const isXml =\n    /^(?:application\\/(?:[\\w\\-.]+\\+)?|image\\/[\\w\\-.]+\\+|text\\/)xml$/.test(\n      contentType\n    );\n  let current = node;\n  while (current) {\n    let lang = null;\n    // Check for lang attributes based on document type.\n    if (isHtml && current.hasAttribute('lang')) {\n      lang = current.getAttribute('lang');\n    } else if (isXml && current.hasAttribute('xml:lang')) {\n      lang = current.getAttribute('xml:lang');\n    }\n    // If a language attribute is found, its value determines the language.\n    // Inheritance stops here, even if the value is an empty string.\n    if (lang !== null) {\n      return lang;\n    }\n    if (current.parentElement) {\n      current = current.parentElement;\n    } else {\n      break;\n    }\n  }\n  return null;\n};\n\n/**\n * Determines case sensitivity for an attribute match.\n * @param {?string} astFlags - The flags from the AST ('i' or 's').\n * @param {string} contentType - The document's content type.\n * @returns {boolean} True if the match should be case-sensitive.\n */\nexport const getCaseSensitivity = (astFlags, contentType) => {\n  if (contentType === 'text/html') {\n    return typeof astFlags === 'string' && /^s$/i.test(astFlags);\n  }\n  return !(typeof astFlags === 'string' && /^i$/i.test(astFlags));\n};\n\n/**\n * Check if content is editable.\n * NOTE: Not implemented in jsdom https://github.com/jsdom/jsdom/issues/1670\n * @param {object} node - The Element node.\n * @returns {boolean} - True if content is editable.\n */\nexport const isContentEditable = node => {\n  if (!node?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(node)}`);\n  }\n  if (node.nodeType !== ELEMENT_NODE) {\n    return false;\n  }\n  if (typeof node.isContentEditable === 'boolean') {\n    return node.isContentEditable;\n  } else if (node.ownerDocument.designMode === 'on') {\n    return true;\n  } else {\n    let attr;\n    if (node.hasAttribute('contenteditable')) {\n      attr = node.getAttribute('contenteditable');\n    } else {\n      attr = 'inherit';\n    }\n    switch (attr) {\n      case '':\n      case 'true': {\n        return true;\n      }\n      case 'plaintext-only': {\n        // FIXME:\n        // @see https://github.com/w3c/editing/issues/470\n        // @see https://github.com/whatwg/html/issues/10651\n        return true;\n      }\n      case 'false': {\n        return false;\n      }\n      default: {\n        if (node?.parentNode?.nodeType === ELEMENT_NODE) {\n          return isContentEditable(node.parentNode);\n        }\n        return false;\n      }\n    }\n  }\n};\n\n/**\n * Check if a node is visible.\n * @param {object} node - The Element node.\n * @returns {boolean} - True if the node is visible.\n */\nexport const isVisible = node => {\n  if (node?.nodeType !== ELEMENT_NODE) {\n    return false;\n  }\n  const window = node.ownerDocument.defaultView;\n  const { display, visibility } = window.getComputedStyle(node);\n  if (display !== 'none' && visibility === 'visible') {\n    return true;\n  }\n  return false;\n};\n\n/**\n * Check if focus is visible on the node.\n * @param {object} node - The Element node.\n * @returns {boolean} - True if focus is visible.\n */\nexport const isFocusVisible = node => {\n  if (node?.nodeType !== ELEMENT_NODE) {\n    return false;\n  }\n  const { localName, type } = node;\n  switch (localName) {\n    case 'input': {\n      if (!type || KEY_INPUT_EDIT.has(type)) {\n        return true;\n      }\n      return false;\n    }\n    case 'textarea': {\n      return true;\n    }\n    default: {\n      return isContentEditable(node);\n    }\n  }\n};\n\n/**\n * Check if an area is focusable.\n * @param {object} node - The Element node.\n * @returns {boolean} - True if the area is focusable.\n */\nexport const isFocusableArea = node => {\n  if (node?.nodeType !== ELEMENT_NODE) {\n    return false;\n  }\n  if (!node.isConnected) {\n    return false;\n  }\n  const window = node.ownerDocument.defaultView;\n  if (node instanceof window.HTMLElement) {\n    if (Number.isInteger(parseInt(node.getAttribute('tabindex')))) {\n      return true;\n    }\n    if (isContentEditable(node)) {\n      return true;\n    }\n    const { localName, parentNode } = node;\n    switch (localName) {\n      case 'a': {\n        if (node.href || node.hasAttribute('href')) {\n          return true;\n        }\n        return false;\n      }\n      case 'iframe': {\n        return true;\n      }\n      case 'input': {\n        if (\n          node.disabled ||\n          node.hasAttribute('disabled') ||\n          node.hidden ||\n          node.hasAttribute('hidden')\n        ) {\n          return false;\n        }\n        return true;\n      }\n      case 'summary': {\n        if (parentNode.localName === 'details') {\n          let child = parentNode.firstElementChild;\n          let bool = false;\n          while (child) {\n            if (child.localName === 'summary') {\n              bool = child === node;\n              break;\n            }\n            child = child.nextElementSibling;\n          }\n          return bool;\n        }\n        return false;\n      }\n      default: {\n        if (\n          KEY_NODE_FOCUSABLE.has(localName) &&\n          !(node.disabled || node.hasAttribute('disabled'))\n        ) {\n          return true;\n        }\n      }\n    }\n  } else if (node instanceof window.SVGElement) {\n    if (Number.isInteger(parseInt(node.getAttributeNS(null, 'tabindex')))) {\n      const ns = 'http://www.w3.org/2000/svg';\n      let bool;\n      let refNode = node;\n      while (refNode.namespaceURI === ns) {\n        bool = KEY_NODE_FOCUSABLE_SVG.has(refNode.localName);\n        if (bool) {\n          break;\n        }\n        if (refNode?.parentNode?.namespaceURI === ns) {\n          refNode = refNode.parentNode;\n        } else {\n          break;\n        }\n      }\n      if (bool) {\n        return false;\n      }\n      return true;\n    }\n    if (\n      node.localName === 'a' &&\n      (node.href || node.hasAttributeNS(null, 'href'))\n    ) {\n      return true;\n    }\n  }\n  return false;\n};\n\n/**\n * Check if a node is focusable.\n * NOTE: Not applied, needs fix in jsdom itself.\n * @see https://github.com/whatwg/html/pull/8392\n * @see https://phabricator.services.mozilla.com/D156219\n * @see https://github.com/jsdom/jsdom/issues/3029\n * @see https://github.com/jsdom/jsdom/issues/3464\n * @param {object} node - The Element node.\n * @returns {boolean} - True if the node is focusable.\n */\nexport const isFocusable = node => {\n  if (node?.nodeType !== ELEMENT_NODE) {\n    return false;\n  }\n  const window = node.ownerDocument.defaultView;\n  let refNode = node;\n  let res = true;\n  while (refNode) {\n    if (refNode.disabled || refNode.hasAttribute('disabled')) {\n      res = false;\n      break;\n    }\n    if (refNode.hidden || refNode.hasAttribute('hidden')) {\n      res = false;\n    }\n    const { contentVisibility, display, visibility } =\n      window.getComputedStyle(refNode);\n    if (\n      display === 'none' ||\n      visibility !== 'visible' ||\n      (contentVisibility === 'hidden' && refNode !== node)\n    ) {\n      res = false;\n    } else {\n      res = true;\n    }\n    if (res && refNode?.parentNode?.nodeType === ELEMENT_NODE) {\n      refNode = refNode.parentNode;\n    } else {\n      break;\n    }\n  }\n  return res;\n};\n\n/**\n * Get namespace URI.\n * @param {string} ns - The namespace prefix.\n * @param {object} node - The Element node.\n * @returns {?string} - The namespace URI.\n */\nexport const getNamespaceURI = (ns, node) => {\n  if (typeof ns !== 'string') {\n    throw new TypeError(`Unexpected type ${getType(ns)}`);\n  } else if (!node?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(node)}`);\n  }\n  if (!ns || node.nodeType !== ELEMENT_NODE) {\n    return null;\n  }\n  const { attributes } = node;\n  let res;\n  for (const attr of attributes) {\n    const { name, namespaceURI, prefix, value } = attr;\n    if (name === `xmlns:${ns}`) {\n      res = value;\n    } else if (prefix === ns) {\n      res = namespaceURI;\n    }\n    if (res) {\n      break;\n    }\n  }\n  return res ?? null;\n};\n\n/**\n * Check if a namespace is declared.\n * @param {string} ns - The namespace.\n * @param {object} node - The Element node.\n * @returns {boolean} - True if the namespace is declared.\n */\nexport const isNamespaceDeclared = (ns = '', node = {}) => {\n  if (!ns || typeof ns !== 'string' || node?.nodeType !== ELEMENT_NODE) {\n    return false;\n  }\n  if (node.lookupNamespaceURI(ns)) {\n    return true;\n  }\n  const root = node.ownerDocument.documentElement;\n  let parent = node;\n  let res;\n  while (parent) {\n    res = getNamespaceURI(ns, parent);\n    if (res || parent === root) {\n      break;\n    }\n    parent = parent.parentNode;\n  }\n  return !!res;\n};\n\n/**\n * Check if nodeA precedes and/or contains nodeB.\n * @param {object} nodeA - The first Element node.\n * @param {object} nodeB - The second Element node.\n * @returns {boolean} - True if nodeA precedes nodeB.\n */\nexport const isPreceding = (nodeA, nodeB) => {\n  if (!nodeA?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(nodeA)}`);\n  } else if (!nodeB?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(nodeB)}`);\n  }\n  if (nodeA.nodeType !== ELEMENT_NODE || nodeB.nodeType !== ELEMENT_NODE) {\n    return false;\n  }\n  const posBit = nodeB.compareDocumentPosition(nodeA);\n  const res =\n    posBit & DOCUMENT_POSITION_PRECEDING || posBit & DOCUMENT_POSITION_CONTAINS;\n  return !!res;\n};\n\n/**\n * Comparison function for sorting nodes based on document position.\n * @param {object} a - The first node.\n * @param {object} b - The second node.\n * @returns {number} - Sort order.\n */\nexport const compareNodes = (a, b) => {\n  if (isPreceding(b, a)) {\n    return 1;\n  }\n  return -1;\n};\n\n/**\n * Sort a collection of nodes.\n * @param {Array.<object>|Set.<object>} nodes - Collection of nodes.\n * @returns {Array.<object>} - Collection of sorted nodes.\n */\nexport const sortNodes = (nodes = []) => {\n  const arr = [...nodes];\n  if (arr.length > 1) {\n    arr.sort(compareNodes);\n  }\n  return arr;\n};\n\n/**\n * Concat an array of nested selectors into an equivalent single selector.\n * @param {Array.<Array.<string>>} selectors - [parents, children, ...].\n * @returns {string} - The concatenated selector.\n */\nexport const concatNestedSelectors = selectors => {\n  if (!Array.isArray(selectors)) {\n    throw new TypeError(`Unexpected type ${getType(selectors)}`);\n  }\n  let selector = '';\n  if (selectors.length) {\n    const revSelectors = selectors.toReversed();\n    let child = verifyArray(revSelectors.shift(), 'String');\n    if (child.length === 1) {\n      [child] = child;\n    }\n    while (revSelectors.length) {\n      const parentArr = verifyArray(revSelectors.shift(), 'String');\n      if (!parentArr.length) {\n        continue;\n      }\n      let parent;\n      if (parentArr.length === 1) {\n        [parent] = parentArr;\n        if (!/^[>~+]/.test(parent) && /[\\s>~+]/.test(parent)) {\n          parent = `:is(${parent})`;\n        }\n      } else {\n        parent = `:is(${parentArr.join(', ')})`;\n      }\n      if (selector.includes('\\x26')) {\n        selector = selector.replace(/\\x26/g, parent);\n      }\n      if (Array.isArray(child)) {\n        const items = [];\n        for (let item of child) {\n          if (item.includes('\\x26')) {\n            if (/^[>~+]/.test(item)) {\n              item = `${parent} ${item.replace(/\\x26/g, parent)} ${selector}`;\n            } else {\n              item = `${item.replace(/\\x26/g, parent)} ${selector}`;\n            }\n          } else {\n            item = `${parent} ${item} ${selector}`;\n          }\n          items.push(item.trim());\n        }\n        selector = items.join(', ');\n      } else if (revSelectors.length) {\n        selector = `${child} ${selector}`;\n      } else {\n        if (child.includes('\\x26')) {\n          if (/^[>~+]/.test(child)) {\n            selector = `${parent} ${child.replace(/\\x26/g, parent)} ${selector}`;\n          } else {\n            selector = `${child.replace(/\\x26/g, parent)} ${selector}`;\n          }\n        } else {\n          selector = `${parent} ${child} ${selector}`;\n        }\n      }\n      selector = selector.trim();\n      if (revSelectors.length) {\n        child = parentArr.length > 1 ? parentArr : parent;\n      } else {\n        break;\n      }\n    }\n    selector = selector.replace(/\\x26/g, ':scope').trim();\n  }\n  return selector;\n};\n\n/**\n * Extract nested selectors from CSSRule.cssText.\n * @param {string} css - CSSRule.cssText.\n * @returns {Array.<Array.<string>>} - Array of nested selectors.\n */\nexport const extractNestedSelectors = css => {\n  const ast = cssTree.parse(css, {\n    context: 'rule'\n  });\n  const extractor = new SelectorExtractor();\n  cssTree.walk(ast, {\n    enter: extractor.enter.bind(extractor),\n    leave: extractor.leave.bind(extractor)\n  });\n  return extractor.selectors;\n};\n\n/**\n * Collects relevant attribute values from a node that match the selector's\n * attribute name.\n * @param {object} node - The element node.\n * @param {object} [opt] - Options.\n * @returns {Array.<string>} An array of matching attribute values.\n */\nexport const findAttributeValues = (node, opt = {}) => {\n  if (!node?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(node)}`);\n  }\n  const { attributes } = node;\n  const {\n    astAttrName = '',\n    astLocalName = '',\n    astPrefix = '',\n    caseSensitive = false\n  } = opt;\n  if (!attributes.length || !astAttrName) {\n    return [];\n  }\n  const attrValues = new Set();\n  const hasNamespace = astAttrName.includes('|');\n  for (const attr of attributes) {\n    let { name: itemName, value: itemValue } = attr;\n    if (!caseSensitive) {\n      itemName = itemName.toLowerCase();\n      itemValue = itemValue.toLowerCase();\n    }\n    if (itemName === 'xml:lang') {\n      continue;\n    }\n    const [itemPrefix, itemLocalName] = itemName.includes(':')\n      ? itemName.split(':')\n      : ['', itemName];\n    if (hasNamespace) {\n      if (astLocalName === itemLocalName) {\n        if (astPrefix === '*' || (astPrefix === '' && itemPrefix === '')) {\n          attrValues.add(itemValue);\n        } else if (\n          astPrefix === itemPrefix &&\n          isNamespaceDeclared(astPrefix, node)\n        ) {\n          attrValues.add(itemValue);\n        }\n      }\n    } else if (astAttrName === itemLocalName) {\n      attrValues.add(itemValue);\n    }\n  }\n  return [...attrValues];\n};\n\n/**\n * Check for valid shadow host selector for :is(), :not(), :where().\n * @param {string} astName - pseudo-class name.\n * @param {Array.<Array.<object>>} branches - AST branches.\n * @returns {boolean} - True if valid\n */\nexport const isValidShadowHostSelector = (astName = '', branches = []) => {\n  if (!/^(?:is|not|where)$/.test(astName)) {\n    return false;\n  }\n  const [branch] = branches;\n  if (Array.isArray(branch) && branch.length === 1) {\n    const [ast] = branch;\n    if (!ast || !Object.hasOwn(ast, 'type')) {\n      return false;\n    }\n    if (astName === 'not') {\n      const { type } = ast;\n      return type === PS_CLASS_SELECTOR;\n    }\n    return true;\n  }\n  return false;\n};\n\n/**\n * Initialize nwsapi.\n * @param {object} window - The Window object.\n * @param {object} document - The Document object.\n * @returns {object} - The nwsapi instance.\n */\nexport const initNwsapi = (window, document) => {\n  if (!window?.DOMException) {\n    throw new TypeError(`Unexpected global object ${getType(window)}`);\n  }\n  if (document?.nodeType !== DOCUMENT_NODE) {\n    document = window.document;\n  }\n  const nw = nwsapi({\n    document,\n    DOMException: window.DOMException\n  });\n  nw.configure({\n    LOGERRORS: false\n  });\n  return nw;\n};\n\n/**\n * Filter a selector for use with nwsapi.\n * @param {string} selector - The selector string.\n * @param {string} target - The target type.\n * @returns {boolean} - True if the selector is valid for nwsapi.\n */\nexport const filterSelector = (selector, target) => {\n  if (\n    !selector ||\n    typeof selector !== 'string' ||\n    /null|undefined/.test(selector) ||\n    target === TARGET_FIRST\n  ) {\n    return false;\n  }\n  // Exclude simple class selector for TARGET_ALL.\n  if (target === TARGET_ALL && REG_SIMPLE_CLASS.test(selector)) {\n    return false;\n  }\n  // Exclude missing close square bracket.\n  if (selector.includes('[')) {\n    const index = selector.lastIndexOf('[');\n    const sel = selector.substring(index);\n    if (sel.indexOf(']') < 0) {\n      return false;\n    }\n  }\n  // Exclude various complex or unsupported selectors.\n  // - selectors containing '/'\n  // - namespaced selectors\n  // - escaped selectors\n  // - pseudo-element selectors\n  // - selectors containing non-ASCII\n  // - selectors containing control character other than whitespace\n  // - attribute selectors with case flag, e.g. [attr i]\n  // - attribute selectors with unclosed quotes\n  // - empty :is() or :where()\n  if (selector.includes('/') || REG_EXCLUDE_BASIC.test(selector)) {\n    return false;\n  }\n  // Include pseudo-classes that are known to work correctly.\n  if (selector.includes(':')) {\n    let complex = false;\n    if (target !== TARGET_ALL) {\n      complex = REG_COMPLEX.test(selector);\n    }\n    if (\n      target === TARGET_ALL &&\n      REG_DESCEND.test(selector) &&\n      !REG_SIBLING.test(selector)\n    ) {\n      return false;\n    } else if (target !== TARGET_ALL && /:has\\(/.test(selector)) {\n      if (!complex || REG_LOGIC_HAS_COMPOUND.test(selector)) {\n        return false;\n      }\n      return REG_END_WITH_HAS.test(selector);\n    } else if (/:(?:is|not)\\(/.test(selector)) {\n      if (complex) {\n        return !REG_LOGIC_COMPLEX.test(selector);\n      } else {\n        return !REG_LOGIC_COMPOUND.test(selector);\n      }\n    } else {\n      return !REG_WO_LOGICAL.test(selector);\n    }\n  }\n  return true;\n};\n", "/**\n * constant.js\n */\n\n/* string */\nexport const ATRULE = 'Atrule';\nexport const ATTR_SELECTOR = 'AttributeSelector';\nexport const CLASS_SELECTOR = 'ClassSelector';\nexport const COMBINATOR = 'Combinator';\nexport const IDENT = 'Identifier';\nexport const ID_SELECTOR = 'IdSelector';\nexport const NOT_SUPPORTED_ERR = 'NotSupportedError';\nexport const NTH = 'Nth';\nexport const OPERATOR = 'Operator';\nexport const PS_CLASS_SELECTOR = 'PseudoClassSelector';\nexport const PS_ELEMENT_SELECTOR = 'PseudoElementSelector';\nexport const RULE = 'Rule';\nexport const SCOPE = 'Scope';\nexport const SELECTOR = 'Selector';\nexport const SELECTOR_LIST = 'SelectorList';\nexport const STRING = 'String';\nexport const SYNTAX_ERR = 'SyntaxError';\nexport const TARGET_ALL = 'all';\nexport const TARGET_FIRST = 'first';\nexport const TARGET_LINEAL = 'lineal';\nexport const TARGET_SELF = 'self';\nexport const TYPE_SELECTOR = 'TypeSelector';\n\n/* numeric */\nexport const BIT_01 = 1;\nexport const BIT_02 = 2;\nexport const BIT_04 = 4;\nexport const BIT_08 = 8;\nexport const BIT_16 = 0x10;\nexport const BIT_32 = 0x20;\nexport const BIT_FFFF = 0xffff;\nexport const DUO = 2;\nexport const HEX = 16;\nexport const TYPE_FROM = 8;\nexport const TYPE_TO = -1;\n\n/* Node */\nexport const ELEMENT_NODE = 1;\nexport const TEXT_NODE = 3;\nexport const DOCUMENT_NODE = 9;\nexport const DOCUMENT_FRAGMENT_NODE = 11;\nexport const DOCUMENT_POSITION_PRECEDING = 2;\nexport const DOCUMENT_POSITION_CONTAINS = 8;\nexport const DOCUMENT_POSITION_CONTAINED_BY = 0x10;\n\n/* NodeFilter */\nexport const SHOW_ALL = 0xffffffff;\nexport const SHOW_CONTAINER = 0x501;\nexport const SHOW_DOCUMENT = 0x100;\nexport const SHOW_DOCUMENT_FRAGMENT = 0x400;\nexport const SHOW_ELEMENT = 1;\n\n/* selectors */\nexport const ALPHA_NUM = '[A-Z\\\\d]+';\nexport const CHILD_IDX = '(?:first|last|only)-(?:child|of-type)';\nexport const DIGIT = '(?:0|[1-9]\\\\d*)';\nexport const LANG_PART = `(?:-${ALPHA_NUM})*`;\nexport const PSEUDO_CLASS = `(?:any-)?link|${CHILD_IDX}|checked|empty|indeterminate|read-(?:only|write)|target`;\nexport const ANB = `[+-]?(?:${DIGIT}n?|n)|(?:[+-]?${DIGIT})?n\\\\s*[+-]\\\\s*${DIGIT}`;\n// N_TH: excludes An+B with selector list, e.g. :nth-child(2n+1 of .foo)\nexport const N_TH = `nth-(?:last-)?(?:child|of-type)\\\\(\\\\s*(?:even|odd|${ANB})\\\\s*\\\\)`;\n// SUB_TYPE: attr, id, class, pseudo-class, note that [foo|=bar] is excluded\nexport const SUB_TYPE = '\\\\[[^|\\\\]]+\\\\]|[#.:][\\\\w-]+';\nexport const SUB_TYPE_WO_PSEUDO = '\\\\[[^|\\\\]]+\\\\]|[#.][\\\\w-]+';\nexport const SUB_CLASS = '(?:\\\\.[\\\\w-]+)';\n// TAG_TYPE: *, tag\nexport const TAG_TYPE = '\\\\*|[A-Za-z][\\\\w-]*';\nexport const TAG_TYPE_I = '\\\\*|[A-Z][\\\\w-]*';\nexport const COMPOUND = `(?:${TAG_TYPE}|(?:${TAG_TYPE})?(?:${SUB_TYPE})+)`;\nexport const COMPOUND_WO_PSEUDO = `(?:${TAG_TYPE}|(?:${TAG_TYPE})?(?:${SUB_TYPE_WO_PSEUDO})+)`;\nexport const COMBO = '\\\\s?[\\\\s>~+]\\\\s?';\nexport const COMPLEX = `${COMPOUND}(?:${COMBO}${COMPOUND})*`;\nexport const DESCEND = '\\\\s?[\\\\s>]\\\\s?';\nexport const SIBLING = '\\\\s?[+~]\\\\s?';\nexport const NESTED_LOGIC_A = `:is\\\\(\\\\s*${COMPOUND}(?:\\\\s*,\\\\s*${COMPOUND})*\\\\s*\\\\)`;\nexport const NESTED_LOGIC_B = `:is\\\\(\\\\s*${COMPLEX}(?:\\\\s*,\\\\s*${COMPLEX})*\\\\s*\\\\)`;\nexport const COMPOUND_A = `(?:${TAG_TYPE}|(?:${TAG_TYPE})?(?:${SUB_TYPE}|${NESTED_LOGIC_A})+)`;\nexport const COMPOUND_B = `(?:${TAG_TYPE}|(?:${TAG_TYPE})?(?:${SUB_TYPE}|${NESTED_LOGIC_B})+)`;\nexport const COMPOUND_I = `(?:${TAG_TYPE_I}|(?:${TAG_TYPE_I})?(?:${SUB_TYPE})+)`;\nexport const COMPLEX_L = `${COMPOUND_B}(?:${COMBO}${COMPOUND_B})*`;\nexport const LOGIC_COMPLEX = `(?:is|not)\\\\(\\\\s*${COMPLEX_L}(?:\\\\s*,\\\\s*${COMPLEX_L})*\\\\s*\\\\)`;\nexport const LOGIC_COMPOUND = `(?:is|not)\\\\(\\\\s*${COMPOUND_A}(?:\\\\s*,\\\\s*${COMPOUND_A})*\\\\s*\\\\)`;\nexport const HAS_COMPOUND = `has\\\\([\\\\s>]?\\\\s*${COMPOUND_WO_PSEUDO}\\\\s*\\\\)`;\n\n/* forms and input types */\nexport const FORM_PARTS = Object.freeze([\n  'button',\n  'input',\n  'select',\n  'textarea'\n]);\nexport const INPUT_BUTTON = Object.freeze(['button', 'reset', 'submit']);\nexport const INPUT_CHECK = Object.freeze(['checkbox', 'radio']);\nexport const INPUT_DATE = Object.freeze([\n  'date',\n  'datetime-local',\n  'month',\n  'time',\n  'week'\n]);\nexport const INPUT_TEXT = Object.freeze([\n  'email',\n  'password',\n  'search',\n  'tel',\n  'text',\n  'url'\n]);\nexport const INPUT_EDIT = Object.freeze([\n  ...INPUT_DATE,\n  ...INPUT_TEXT,\n  'number'\n]);\nexport const INPUT_LTR = Object.freeze([\n  ...INPUT_CHECK,\n  'color',\n  'date',\n  'image',\n  'number',\n  'range',\n  'time'\n]);\n\n/* keywords */\nexport const KEY_INPUT_EDIT = new Set(INPUT_EDIT);\nexport const KEY_LOGICAL = new Set(['has', 'is', 'not', 'where']);\nexport const KEY_MODIFIER = new Set([\n  'Alt',\n  'AltGraph',\n  'CapsLock',\n  'Control',\n  'Fn',\n  'FnLock',\n  'Hyper',\n  'Meta',\n  'NumLock',\n  'ScrollLock',\n  'Shift',\n  'Super',\n  'Symbol',\n  'SymbolLock'\n]);\nexport const KEY_PS_ELEMENT = new Set([\n  'after',\n  'backdrop',\n  'before',\n  'cue',\n  'cue-region',\n  'first-letter',\n  'first-line',\n  'file-selector-button',\n  'marker',\n  'placeholder',\n  'selection',\n  'target-text'\n]);\nexport const KEY_PS_ELEMENT_FUNC = new Set(['part', 'slotted']);\nexport const KEY_PS_CLASS_STATE = new Set([\n  'enabled',\n  'disabled',\n  'valid',\n  'invalid',\n  'in-range',\n  'out-of-range',\n  'checked',\n  'indeterminate',\n  'read-only',\n  'read-write',\n  'open',\n  'closed',\n  'placeholder-shown'\n]);\nexport const KEY_SHADOW_HOST = new Set(['host', 'host-context']);\n", "/**\n * matcher.js\n */\n\n/* import */\nimport { generateCSS, parseAstName, unescapeSelector } from './parser.js';\nimport {\n  findAttributeValues,\n  findLangAttribute,\n  getCaseSensitivity,\n  getDirectionality,\n  getType,\n  isContentEditable,\n  isCustomElement\n} from './utility.js';\n\n/* constants */\nimport {\n  ALPHA_NUM,\n  ELEMENT_NODE,\n  FORM_PARTS,\n  IDENT,\n  KEY_INPUT_EDIT,\n  KEY_PS_ELEMENT,\n  KEY_PS_ELEMENT_FUNC,\n  LANG_PART,\n  NOT_SUPPORTED_ERR,\n  PS_ELEMENT_SELECTOR,\n  STRING,\n  SYNTAX_ERR\n} from './constant.js';\nconst KEY_FORM_PS_DISABLED = new Set([\n  ...FORM_PARTS,\n  'fieldset',\n  'optgroup',\n  'option'\n]);\nconst REG_VALID_LANG = new RegExp(`^(?:\\\\*-)?${ALPHA_NUM}${LANG_PART}$`, 'i');\n\n/**\n * Matches a pseudo-element selector against supported and known types.\n * @param {string} astName - The name of the pseudo-element from the AST.\n * @param {string} astType - The type of the node from the AST.\n * @param {object} [opt] - Options.\n * @param {boolean} [opt.forgive] - If true, ignores unknown pseudo-element.\n * @param {boolean} [opt.warn] - If true, throws for unsupported pseudo-element.\n * @throws {DOMException} If the pseudo-element is invalid or unsupported.\n * @returns {void}\n */\nexport const matchPseudoElementSelector = (astName, astType, opt = {}) => {\n  if (astType !== PS_ELEMENT_SELECTOR) {\n    throw new TypeError(`Unexpected ast type ${getType(astType)}`);\n  }\n  const { forgive, warn } = opt;\n  const isKnown =\n    KEY_PS_ELEMENT.has(astName) ||\n    KEY_PS_ELEMENT_FUNC.has(astName) ||\n    astName.startsWith('-webkit-');\n  if (!isKnown && !forgive && !warn) {\n    throw new DOMException(`Unknown pseudo-element ::${astName}`, SYNTAX_ERR);\n  } else if (warn) {\n    let msg = '';\n    if (KEY_PS_ELEMENT_FUNC.has(astName)) {\n      msg = `Unsupported pseudo-element ::${astName}()`;\n    } else if (astName.startsWith('-webkit-')) {\n      msg = `Unsupported pseudo-element ::${astName}`;\n    } else {\n      msg = `Unsupported pseudo-element ::${astName}`;\n    }\n    throw new DOMException(msg, NOT_SUPPORTED_ERR);\n  }\n};\n\n/**\n * Matches the :dir() pseudo-class against an element's directionality.\n * @param {object} ast - The :dir() pseudo-class AST node.\n * @param {object} node - The element node to check.\n * @returns {boolean} True if the element's directionality matches the selector.\n * @throws {TypeError} If the AST node does not contain a valid direction name.\n */\nexport const matchDirectionPseudoClass = (ast, node) => {\n  const { name } = ast;\n  // The AST must provide a non-empty string for the direction.\n  if (typeof name !== 'string' || name === '') {\n    const type = name === '' ? '(empty String)' : getType(name);\n    throw new TypeError(`Unexpected ast type ${type}`);\n  }\n  const dir = getDirectionality(node);\n  return name === dir;\n};\n\n/**\n * Matches the :lang() pseudo-class against an element's language.\n * @see https://datatracker.ietf.org/doc/html/rfc4647#section-3.3.1\n * @param {object} ast - The :lang() pseudo-class AST node.\n * @param {object} node - The element node to check.\n * @returns {boolean} True if the element's language matches the selector.\n */\nexport const matchLanguagePseudoClass = (ast, node) => {\n  const { name, type, value } = ast;\n  let astName;\n  if (type === STRING && value) {\n    astName = value;\n  } else if (type === IDENT && name) {\n    astName = unescapeSelector(name);\n  } else {\n    // No valid language identifier provided in the selector.\n    return false;\n  }\n  const effectiveLang = findLangAttribute(node);\n  // If no language is defined on the element or its ancestors, it cannot match.\n  if (typeof effectiveLang !== 'string') {\n    return false;\n  }\n  // Handle the wildcard selector :lang(*)\n  if (astName === '*') {\n    return effectiveLang !== '';\n  }\n  // Validate the provided language.\n  if (!REG_VALID_LANG.test(astName)) {\n    return false;\n  }\n  // Build the extended language range regex for matching.\n  let regExtendedLang;\n  if (astName.includes('-')) {\n    const [langMain, langSub, ...langRest] = astName.split('-');\n    let extendedMain = `${ALPHA_NUM}${LANG_PART}`;\n    if (langMain !== '*') {\n      extendedMain = `${langMain}${LANG_PART}`;\n    }\n    const extendedSub = `-${langSub}${LANG_PART}`;\n    const extendedRest = langRest.map(part => `-${part}${LANG_PART}`).join('');\n    regExtendedLang = new RegExp(\n      `^${extendedMain}${extendedSub}${extendedRest}$`,\n      'i'\n    );\n  } else {\n    regExtendedLang = new RegExp(`^${astName}${LANG_PART}$`, 'i');\n  }\n  return regExtendedLang.test(effectiveLang);\n};\n\n/**\n * Matches the :disabled and :enabled pseudo-classes.\n * @param {string} astName - pseudo-class name\n * @param {object} node - Element node\n * @returns {boolean} - True if matched\n */\nexport const matchDisabledEnabledPseudo = (astName = '', node = {}) => {\n  if (!/^(?:dis|en)abled$/.test(astName) || node?.nodeType !== ELEMENT_NODE) {\n    return false;\n  }\n  const { localName, parentNode } = node;\n  if (\n    !KEY_FORM_PS_DISABLED.has(localName) &&\n    !isCustomElement(node, { formAssociated: true })\n  ) {\n    return false;\n  }\n  let isDisabled = false;\n  if (node.disabled || node.hasAttribute('disabled')) {\n    isDisabled = true;\n  } else if (localName === 'option') {\n    if (\n      parentNode &&\n      parentNode.localName === 'optgroup' &&\n      (parentNode.disabled || parentNode.hasAttribute('disabled'))\n    ) {\n      isDisabled = true;\n    }\n  } else if (localName !== 'optgroup') {\n    let current = parentNode;\n    while (current) {\n      if (\n        current.localName === 'fieldset' &&\n        (current.disabled || current.hasAttribute('disabled'))\n      ) {\n        // The first <legend> in a disabled <fieldset> is not disabled.\n        let legend;\n        let element = current.firstElementChild;\n        while (element) {\n          if (element.localName === 'legend') {\n            legend = element;\n            break;\n          }\n          element = element.nextElementSibling;\n        }\n        if (!legend || !legend.contains(node)) {\n          isDisabled = true;\n        }\n        // Found the containing fieldset, stop searching up.\n        break;\n      }\n      current = current.parentNode;\n    }\n  }\n  if (astName === 'disabled') {\n    return isDisabled;\n  }\n  return !isDisabled;\n};\n\n/**\n * Match the :read-only and :read-write pseudo-classes\n * @param {string} astName - pseudo-class name\n * @param {object} node - Element node\n * @returns {boolean} - True if matched\n */\nexport const matchReadOnlyWritePseudo = (astName, node) => {\n  if (\n    !/^read-(?:only|write)$/.test(astName) ||\n    node?.nodeType !== ELEMENT_NODE\n  ) {\n    return false;\n  }\n  const { localName } = node;\n  let isReadOnly = false;\n  switch (localName) {\n    case 'textarea':\n    case 'input': {\n      const isEditableInput = !node.type || KEY_INPUT_EDIT.has(node.type);\n      if (localName === 'textarea' || isEditableInput) {\n        isReadOnly =\n          node.readOnly ||\n          node.hasAttribute('readonly') ||\n          node.disabled ||\n          node.hasAttribute('disabled');\n      } else {\n        // Non-editable input types are always read-only\n        isReadOnly = true;\n      }\n      break;\n    }\n    default: {\n      isReadOnly = !isContentEditable(node);\n    }\n  }\n  if (astName === 'read-only') {\n    return isReadOnly;\n  }\n  return !isReadOnly;\n};\n\n/**\n * Matches an attribute selector against an element.\n * @param {object} ast - The attribute selector AST node.\n * @param {object} node - The element node to check.\n * @param {object} [opt] - Options.\n * @param {boolean} [opt.check] - Internal flag for nwsapi compatibility.\n * @param {boolean} [opt.forgive] - If true, forgive invalid syntax.\n * @returns {boolean} True if the element matches the attribute selector.\n */\nexport const matchAttributeSelector = (ast, node, opt = {}) => {\n  const {\n    flags: astFlags,\n    matcher: astMatcher,\n    name: astName,\n    value: astValue\n  } = ast;\n  const { check, forgive } = opt;\n  if (typeof astFlags === 'string' && !/^[is]$/i.test(astFlags) && !forgive) {\n    const css = generateCSS(ast);\n    throw new DOMException(`Invalid selector ${css}`, SYNTAX_ERR);\n  }\n  if (!node.attributes?.length) {\n    return false;\n  }\n  const {\n    ownerDocument: { contentType }\n  } = node;\n  const caseSensitive = getCaseSensitivity(astFlags, contentType);\n  let astAttrName = unescapeSelector(astName.name);\n  if (!caseSensitive) {\n    astAttrName = astAttrName.toLowerCase();\n  }\n  const { prefix: astPrefix, localName: astLocalName } =\n    parseAstName(astAttrName);\n  if (astAttrName.includes('|')) {\n    const { prefix: astPrefix } = parseAstName(astAttrName);\n    if (astPrefix !== '' && astPrefix !== '*' && !check) {\n      if (forgive) {\n        return false;\n      }\n      const css = generateCSS(ast);\n      throw new DOMException(`Invalid selector ${css}`, SYNTAX_ERR);\n    }\n  }\n  const values = findAttributeValues(node, {\n    astAttrName,\n    astLocalName,\n    astPrefix,\n    caseSensitive\n  });\n  if (!values.length) {\n    return false;\n  }\n  // If there's no matcher in the selector (e.g., [disabled]), a match is found.\n  if (!astMatcher) {\n    return true;\n  }\n  let selectorValue = '';\n  if (astValue.type === IDENT) {\n    selectorValue = caseSensitive ? astValue.name : astValue.name.toLowerCase();\n  } else if (astValue.type === STRING) {\n    selectorValue = caseSensitive\n      ? astValue.value\n      : astValue.value.toLowerCase();\n  }\n  switch (astMatcher) {\n    case '~=': {\n      return (\n        !!selectorValue &&\n        values.some(v => v.split(/\\s+/).includes(selectorValue))\n      );\n    }\n    case '|=': {\n      return (\n        !!selectorValue &&\n        values.some(\n          v => v === selectorValue || v.startsWith(`${selectorValue}-`)\n        )\n      );\n    }\n    case '^=': {\n      return !!selectorValue && values.some(v => v.startsWith(selectorValue));\n    }\n    case '$=': {\n      return !!selectorValue && values.some(v => v.endsWith(selectorValue));\n    }\n    case '*=': {\n      return !!selectorValue && values.some(v => v.includes(selectorValue));\n    }\n    case '=':\n    default: {\n      return values.some(v => v === selectorValue);\n    }\n  }\n};\n\n/**\n * Matches a type selector (e.g., 'div', 'ns|E') against an element.\n * @param {object} ast - The type selector AST node.\n * @param {object} node - The element node to check.\n * @param {object} [opt] - Options.\n * @param {boolean} [opt.check] - Internal flag for nwsapi compatibility.\n * @param {boolean} [opt.forgive] - If true, forgive undeclared namespaces.\n * @returns {boolean} True if the element matches the type selector.\n */\nexport const matchTypeSelector = (ast, node, opt = {}) => {\n  const astName = unescapeSelector(ast.name);\n  const { localName, namespaceURI, prefix } = node;\n  const { check, forgive } = opt;\n  let { prefix: astPrefix, localName: astLocalName } = parseAstName(\n    astName,\n    node\n  );\n  if (\n    node.ownerDocument.contentType === 'text/html' &&\n    (!namespaceURI || namespaceURI === 'http://www.w3.org/1999/xhtml') &&\n    /[A-Z][\\w-]*/i.test(localName)\n  ) {\n    astPrefix = astPrefix.toLowerCase();\n    astLocalName = astLocalName.toLowerCase();\n  }\n  let nodePrefix = '';\n  let nodeLocalName = '';\n  // just in case that the namespaced content is parsed as text/html\n  if (localName.includes(':')) {\n    [nodePrefix, nodeLocalName] = localName.split(':');\n  } else {\n    nodePrefix = prefix || '';\n    nodeLocalName = localName;\n  }\n  switch (astPrefix) {\n    case '': {\n      if (\n        !nodePrefix &&\n        !namespaceURI &&\n        (astLocalName === '*' || astLocalName === nodeLocalName)\n      ) {\n        return true;\n      }\n      return false;\n    }\n    case '*': {\n      if (astLocalName === '*' || astLocalName === nodeLocalName) {\n        return true;\n      }\n      return false;\n    }\n    default: {\n      if (!check) {\n        if (forgive) {\n          return false;\n        }\n        const css = generateCSS(ast);\n        throw new DOMException(`Invalid selector ${css}`, SYNTAX_ERR);\n      }\n      const astNS = node.lookupNamespaceURI(astPrefix);\n      const nodeNS = node.lookupNamespaceURI(nodePrefix);\n      if (astNS === nodeNS && astPrefix === nodePrefix) {\n        if (astLocalName === '*' || astLocalName === nodeLocalName) {\n          return true;\n        }\n        return false;\n      } else if (!forgive && !astNS) {\n        throw new DOMException(`Undeclared namespace ${astPrefix}`, SYNTAX_ERR);\n      }\n      return false;\n    }\n  }\n};\n", "/**\n * finder.js\n */\n\n/* import */\nimport {\n  matchAttributeSelector,\n  matchDirectionPseudoClass,\n  matchDisabledEnabledPseudo,\n  matchLanguagePseudoClass,\n  matchPseudoElementSelector,\n  matchReadOnlyWritePseudo,\n  matchTypeSelector\n} from './matcher.js';\nimport {\n  findAST,\n  generateCSS,\n  parseSelector,\n  sortAST,\n  unescapeSelector,\n  walkAST\n} from './parser.js';\nimport {\n  filterNodesByAnB,\n  findLogicalWithNestedHas,\n  generateException,\n  isCustomElement,\n  isFocusVisible,\n  isFocusableArea,\n  isValidShadowHostSelector,\n  isVisible,\n  resolveContent,\n  sortNodes,\n  traverseNode\n} from './utility.js';\n\n/* constants */\nimport {\n  ATTR_SELECTOR,\n  BIT_01,\n  CLASS_SELECTOR,\n  COMBINATOR,\n  DOCUMENT_FRAGMENT_NODE,\n  ELEMENT_NODE,\n  FORM_PARTS,\n  ID_SELECTOR,\n  INPUT_CHECK,\n  INPUT_DATE,\n  INPUT_EDIT,\n  INPUT_TEXT,\n  KEY_LOGICAL,\n  KEY_MODIFIER,\n  NOT_SUPPORTED_ERR,\n  PS_CLASS_SELECTOR,\n  PS_ELEMENT_SELECTOR,\n  SHOW_ALL,\n  SHOW_CONTAINER,\n  SYNTAX_ERR,\n  TARGET_ALL,\n  TARGET_FIRST,\n  TARGET_LINEAL,\n  TARGET_SELF,\n  TEXT_NODE,\n  TYPE_SELECTOR\n} from './constant.js';\nconst DIR_NEXT = 'next';\nconst DIR_PREV = 'prev';\nconst KEY_FORM = new Set([...FORM_PARTS, 'fieldset', 'form']);\nconst KEY_FORM_PS_VALID = new Set([...FORM_PARTS, 'form']);\nconst KEY_INPUT_CHECK = new Set(INPUT_CHECK);\nconst KEY_INPUT_PLACEHOLDER = new Set([...INPUT_TEXT, 'number']);\nconst KEY_INPUT_RANGE = new Set([...INPUT_DATE, 'number', 'range']);\nconst KEY_INPUT_REQUIRED = new Set([...INPUT_CHECK, ...INPUT_EDIT, 'file']);\nconst KEY_INPUT_RESET = new Set(['button', 'reset']);\nconst KEY_INPUT_SUBMIT = new Set(['image', 'submit']);\nconst KEY_PS_UNCACHE = new Set(['any-link', 'defined', 'dir', 'link', 'scope']);\nconst KEY_PS_FORM = new Set([\n  'checked',\n  'default',\n  'disabled',\n  'enabled',\n  'in-range',\n  'indeterminate',\n  'invalid',\n  'optional',\n  'out-of-range',\n  'placeholder-shown',\n  'read-only',\n  'read-write',\n  'required',\n  'user-invalid',\n  'user-valid',\n  'valid'\n]);\nconst KEY_PS_UI = new Set(['closed', 'open', 'popover-open']);\n\n/**\n * Finder\n * NOTE: #ast[i] corresponds to #nodes[i]\n */\nexport class Finder {\n  /* private fields */\n  #ast;\n  #astCache;\n  #check;\n  #descendant;\n  #document;\n  #documentCache;\n  #documentURL;\n  #domSymbolTree;\n  #event;\n  #eventHandlers;\n  #focus;\n  #invalidate;\n  #invalidateResults;\n  #lastFocusVisible;\n  #node;\n  #nodeWalker;\n  #nodes;\n  #noexcept;\n  #pseudoElement;\n  #results;\n  #root;\n  #rootWalker;\n  #selector;\n  #shadow;\n  #verifyShadowHost;\n  #walkers;\n  #warn;\n  #window;\n\n  /**\n   * constructor\n   * @param {object} window - The window object.\n   */\n  constructor(window) {\n    this.#window = window;\n    this.#astCache = new WeakMap();\n    this.#documentCache = new WeakMap();\n    this.#invalidateResults = new WeakMap();\n    this.#results = new WeakMap();\n    this.#event = null;\n    this.#focus = null;\n    this.#lastFocusVisible = null;\n    this.#eventHandlers = new Set([\n      {\n        keys: ['focus', 'focusin'],\n        handler: this._handleFocusEvent\n      },\n      {\n        keys: ['keydown', 'keyup'],\n        handler: this._handleKeyboardEvent\n      },\n      {\n        keys: ['mouseover', 'mousedown', 'mouseup', 'mouseout'],\n        handler: this._handleMouseEvent\n      },\n      {\n        keys: ['click'],\n        handler: this._handleClickEvent\n      }\n    ]);\n    this._registerEventListeners();\n  }\n\n  /**\n   * Handles errors.\n   * @param {Error} e - The error object.\n   * @param {object} [opt] - Options.\n   * @param {boolean} [opt.noexcept] - If true, exceptions are not thrown.\n   * @throws {Error} Throws an error.\n   * @returns {void}\n   */\n  onError = (e, opt = {}) => {\n    const noexcept = opt.noexcept ?? this.#noexcept;\n    if (noexcept) {\n      return;\n    }\n    const isDOMException =\n      e instanceof DOMException || e instanceof this.#window.DOMException;\n    if (isDOMException) {\n      if (e.name === NOT_SUPPORTED_ERR) {\n        if (this.#warn) {\n          console.warn(e.message);\n        }\n        return;\n      }\n      throw new this.#window.DOMException(e.message, e.name);\n    }\n    if (e.name in this.#window) {\n      throw new this.#window[e.name](e.message, { cause: e });\n    }\n    throw e;\n  };\n\n  /**\n   * Sets up the finder.\n   * @param {string} selector - The CSS selector.\n   * @param {object} node - Document, DocumentFragment, or Element.\n   * @param {object} [opt] - Options.\n   * @param {boolean} [opt.check] - Indicates if running in internal check().\n   * @param {object} [opt.domSymbolTree] - The domSymbolTree object.\n   * @param {boolean} [opt.noexcept] - If true, exceptions are not thrown.\n   * @param {boolean} [opt.warn] - If true, console warnings are enabled.\n   * @returns {object} The finder instance.\n   */\n  setup = (selector, node, opt = {}) => {\n    const { check, domSymbolTree, noexcept, warn } = opt;\n    this.#check = !!check;\n    this.#domSymbolTree = domSymbolTree;\n    this.#noexcept = !!noexcept;\n    this.#warn = !!warn;\n    [this.#document, this.#root, this.#shadow] = resolveContent(node);\n    this.#documentURL = new URL(this.#document.URL);\n    this.#node = node;\n    this.#selector = selector;\n    [this.#ast, this.#nodes] = this._correspond(selector);\n    this.#invalidateResults = new WeakMap();\n    this.#pseudoElement = [];\n    this.#walkers = new WeakMap();\n    this.#nodeWalker = null;\n    this.#rootWalker = null;\n\n    this.#verifyShadowHost = null;\n    return this;\n  };\n\n  /**\n   * Handles focus events.\n   * @private\n   * @param {Event} evt - The event object.\n   * @returns {void}\n   */\n  _handleFocusEvent = evt => {\n    this.#focus = evt;\n  };\n\n  /**\n   * Handles keyboard events.\n   * @private\n   * @param {Event} evt - The event object.\n   * @returns {void}\n   */\n  _handleKeyboardEvent = evt => {\n    const { key } = evt;\n    if (!KEY_MODIFIER.has(key)) {\n      this.#event = evt;\n    }\n  };\n\n  /**\n   * Handles mouse events.\n   * @private\n   * @param {Event} evt - The event object.\n   * @returns {void}\n   */\n  _handleMouseEvent = evt => {\n    this.#event = evt;\n  };\n\n  /**\n   * Handles click events.\n   * @private\n   * @param {Event} evt - The event object.\n   * @returns {void}\n   */\n  _handleClickEvent = evt => {\n    this.#event = evt;\n    this.#invalidateResults = new WeakMap();\n    this.#results = new WeakMap();\n  };\n\n  /**\n   * Registers event listeners.\n   * @private\n   * @returns {Array.<void>} An array of return values from addEventListener.\n   */\n  _registerEventListeners = () => {\n    const opt = {\n      capture: true,\n      passive: true\n    };\n    const func = [];\n    for (const eventHandler of this.#eventHandlers) {\n      const { keys, handler } = eventHandler;\n      const l = keys.length;\n      for (let i = 0; i < l; i++) {\n        const key = keys[i];\n        func.push(this.#window.addEventListener(key, handler, opt));\n      }\n    }\n    return func;\n  };\n\n  /**\n   * Processes selector branches into the internal AST structure.\n   * @private\n   * @param {Array.<Array.<object>>} branches - The branches from walkAST.\n   * @param {string} selector - The original selector for error reporting.\n   * @returns {{ast: Array, descendant: boolean, invalidate: boolean}}\n   * An object with the AST, descendant flag, and invalidate flag.\n   */\n  _processSelectorBranches = (branches, selector) => {\n    let invalidate = false;\n    let descendant = false;\n    const ast = [];\n    const l = branches.length;\n    for (let i = 0; i < l; i++) {\n      const items = [...branches[i]];\n      const branch = [];\n      let item = items.shift();\n      if (item && item.type !== COMBINATOR) {\n        const leaves = new Set();\n        while (item) {\n          if (item.type === COMBINATOR) {\n            const [nextItem] = items;\n            if (!nextItem || nextItem.type === COMBINATOR) {\n              const msg = `Invalid selector ${selector}`;\n              this.onError(generateException(msg, SYNTAX_ERR, this.#window));\n              // Stop processing on invalid selector.\n              return { ast: [], descendant: false, invalidate: false };\n            }\n            if (item.name === '+' || item.name === '~') {\n              invalidate = true;\n            } else {\n              descendant = true;\n            }\n            branch.push({ combo: item, leaves: sortAST(leaves) });\n            leaves.clear();\n          } else {\n            if (item.name && typeof item.name === 'string') {\n              const unescapedName = unescapeSelector(item.name);\n              if (unescapedName !== item.name) {\n                item.name = unescapedName;\n              }\n              if (/[|:]/.test(unescapedName)) {\n                item.namespace = true;\n              }\n            }\n            leaves.add(item);\n          }\n          if (items.length) {\n            item = items.shift();\n          } else {\n            branch.push({ combo: null, leaves: sortAST(leaves) });\n            leaves.clear();\n            break;\n          }\n        }\n      }\n      ast.push({ branch, dir: null, filtered: false, find: false });\n    }\n    return { ast, descendant, invalidate };\n  };\n\n  /**\n   * Corresponds AST and nodes.\n   * @private\n   * @param {string} selector - The CSS selector.\n   * @returns {Array.<Array.<object>>} An array with the AST and nodes.\n   */\n  _correspond = selector => {\n    const nodes = [];\n    this.#descendant = false;\n    this.#invalidate = false;\n    let ast;\n    if (this.#documentCache.has(this.#document)) {\n      const cachedItem = this.#documentCache.get(this.#document);\n      if (cachedItem && cachedItem.has(`${selector}`)) {\n        const item = cachedItem.get(`${selector}`);\n        ast = item.ast;\n        this.#descendant = item.descendant;\n        this.#invalidate = item.invalidate;\n      }\n    }\n    if (ast) {\n      const l = ast.length;\n      for (let i = 0; i < l; i++) {\n        ast[i].dir = null;\n        ast[i].filtered = false;\n        ast[i].find = false;\n        nodes[i] = [];\n      }\n    } else {\n      let cssAst;\n      try {\n        cssAst = parseSelector(selector);\n      } catch (e) {\n        return this.onError(e);\n      }\n      const { branches, info } = walkAST(cssAst);\n      const {\n        hasHasPseudoFunc,\n        hasLogicalPseudoFunc,\n        hasNthChildOfSelector,\n        hasStatePseudoClass\n      } = info;\n      const baseInvalidate =\n        hasHasPseudoFunc ||\n        hasStatePseudoClass ||\n        !!(hasLogicalPseudoFunc && hasNthChildOfSelector);\n      const processed = this._processSelectorBranches(branches, selector);\n      ast = processed.ast;\n      this.#descendant = processed.descendant;\n      this.#invalidate = baseInvalidate || processed.invalidate;\n      let cachedItem;\n      if (this.#documentCache.has(this.#document)) {\n        cachedItem = this.#documentCache.get(this.#document);\n      } else {\n        cachedItem = new Map();\n      }\n      cachedItem.set(`${selector}`, {\n        ast,\n        descendant: this.#descendant,\n        invalidate: this.#invalidate\n      });\n      this.#documentCache.set(this.#document, cachedItem);\n      // Initialize nodes array for each branch.\n      for (let i = 0; i < ast.length; i++) {\n        nodes[i] = [];\n      }\n    }\n    return [ast, nodes];\n  };\n\n  /**\n   * Creates a TreeWalker.\n   * @private\n   * @param {object} node - The Document, DocumentFragment, or Element node.\n   * @param {object} [opt] - Options.\n   * @param {boolean} [opt.force] - Force creation of a new TreeWalker.\n   * @param {number} [opt.whatToShow] - The NodeFilter whatToShow value.\n   * @returns {object} The TreeWalker object.\n   */\n  _createTreeWalker = (node, opt = {}) => {\n    const { force = false, whatToShow = SHOW_CONTAINER } = opt;\n    if (force) {\n      return this.#document.createTreeWalker(node, whatToShow);\n    }\n    if (this.#walkers.has(node)) {\n      return this.#walkers.get(node);\n    }\n    const walker = this.#document.createTreeWalker(node, whatToShow);\n    this.#walkers.set(node, walker);\n    return walker;\n  };\n\n  /**\n   * Gets selector branches from cache or parses them.\n   * @private\n   * @param {object} selector - The AST.\n   * @returns {Array.<Array.<object>>} The selector branches.\n   */\n  _getSelectorBranches = selector => {\n    if (this.#astCache.has(selector)) {\n      return this.#astCache.get(selector);\n    }\n    const { branches } = walkAST(selector);\n    this.#astCache.set(selector, branches);\n    return branches;\n  };\n\n  /**\n   * Gets the children of a node, optionally filtered by a selector.\n   * @private\n   * @param {object} parentNode - The parent element.\n   * @param {Array.<Array.<object>>} selectorBranches - The selector branches.\n   * @param {object} [opt] - Options.\n   * @returns {Array.<object>} An array of child nodes.\n   */\n  _getFilteredChildren = (parentNode, selectorBranches, opt = {}) => {\n    const children = [];\n    const walker = this._createTreeWalker(parentNode, { force: true });\n    let childNode = walker.firstChild();\n    while (childNode) {\n      if (selectorBranches) {\n        if (isVisible(childNode)) {\n          let isMatch = false;\n          const l = selectorBranches.length;\n          for (let i = 0; i < l; i++) {\n            const leaves = selectorBranches[i];\n            if (this._matchLeaves(leaves, childNode, opt)) {\n              isMatch = true;\n              break;\n            }\n          }\n          if (isMatch) {\n            children.push(childNode);\n          }\n        }\n      } else {\n        children.push(childNode);\n      }\n      childNode = walker.nextSibling();\n    }\n    return children;\n  };\n\n  /**\n   * Collects nth-child nodes.\n   * @private\n   * @param {object} anb - An+B options.\n   * @param {number} anb.a - The 'a' value.\n   * @param {number} anb.b - The 'b' value.\n   * @param {boolean} [anb.reverse] - If true, reverses the order.\n   * @param {object} [anb.selector] - The AST.\n   * @param {object} node - The Element node.\n   * @param {object} [opt] - Options.\n   * @returns {Set.<object>} A collection of matched nodes.\n   */\n  _collectNthChild = (anb, node, opt = {}) => {\n    const { a, b, selector } = anb;\n    const { parentNode } = node;\n    if (!parentNode) {\n      const matchedNode = new Set();\n      if (node === this.#root && a * 1 + b * 1 === 1) {\n        if (selector) {\n          const selectorBranches = this._getSelectorBranches(selector);\n          const l = selectorBranches.length;\n          for (let i = 0; i < l; i++) {\n            const leaves = selectorBranches[i];\n            if (this._matchLeaves(leaves, node, opt)) {\n              matchedNode.add(node);\n              break;\n            }\n          }\n        } else {\n          matchedNode.add(node);\n        }\n      }\n      return matchedNode;\n    }\n    const selectorBranches = selector\n      ? this._getSelectorBranches(selector)\n      : null;\n    const children = this._getFilteredChildren(\n      parentNode,\n      selectorBranches,\n      opt\n    );\n    const matchedNodes = filterNodesByAnB(children, anb);\n    return new Set(matchedNodes);\n  };\n\n  /**\n   * Collects nth-of-type nodes.\n   * @private\n   * @param {object} anb - An+B options.\n   * @param {number} anb.a - The 'a' value.\n   * @param {number} anb.b - The 'b' value.\n   * @param {boolean} [anb.reverse] - If true, reverses the order.\n   * @param {object} node - The Element node.\n   * @returns {Set.<object>} A collection of matched nodes.\n   */\n  _collectNthOfType = (anb, node) => {\n    const { parentNode } = node;\n    if (!parentNode) {\n      if (node === this.#root && anb.a * 1 + anb.b * 1 === 1) {\n        return new Set([node]);\n      }\n      return new Set();\n    }\n    const typedSiblings = [];\n    const walker = this._createTreeWalker(parentNode, { force: true });\n    let sibling = walker.firstChild();\n    while (sibling) {\n      if (\n        sibling.localName === node.localName &&\n        sibling.namespaceURI === node.namespaceURI &&\n        sibling.prefix === node.prefix\n      ) {\n        typedSiblings.push(sibling);\n      }\n      sibling = walker.nextSibling();\n    }\n    const matchedNodes = filterNodesByAnB(typedSiblings, anb);\n    return new Set(matchedNodes);\n  };\n\n  /**\n   * Matches An+B.\n   * @private\n   * @param {object} ast - The AST.\n   * @param {object} node - The Element node.\n   * @param {string} nthName - The name of the nth pseudo-class.\n   * @param {object} [opt] - Options.\n   * @returns {Set.<object>} A collection of matched nodes.\n   */\n  _matchAnPlusB = (ast, node, nthName, opt = {}) => {\n    const {\n      nth: { a, b, name: nthIdentName },\n      selector\n    } = ast;\n    const anbMap = new Map();\n    if (nthIdentName) {\n      if (nthIdentName === 'even') {\n        anbMap.set('a', 2);\n        anbMap.set('b', 0);\n      } else if (nthIdentName === 'odd') {\n        anbMap.set('a', 2);\n        anbMap.set('b', 1);\n      }\n      if (nthName.indexOf('last') > -1) {\n        anbMap.set('reverse', true);\n      }\n    } else {\n      if (typeof a === 'string' && /-?\\d+/.test(a)) {\n        anbMap.set('a', a * 1);\n      } else {\n        anbMap.set('a', 0);\n      }\n      if (typeof b === 'string' && /-?\\d+/.test(b)) {\n        anbMap.set('b', b * 1);\n      } else {\n        anbMap.set('b', 0);\n      }\n      if (nthName.indexOf('last') > -1) {\n        anbMap.set('reverse', true);\n      }\n    }\n    if (nthName === 'nth-child' || nthName === 'nth-last-child') {\n      if (selector) {\n        anbMap.set('selector', selector);\n      }\n      const anb = Object.fromEntries(anbMap);\n      const nodes = this._collectNthChild(anb, node, opt);\n      return nodes;\n    } else if (nthName === 'nth-of-type' || nthName === 'nth-last-of-type') {\n      const anb = Object.fromEntries(anbMap);\n      const nodes = this._collectNthOfType(anb, node);\n      return nodes;\n    }\n    return new Set();\n  };\n\n  /**\n   * Matches the :has() pseudo-class function.\n   * @private\n   * @param {Array.<object>} astLeaves - The AST leaves.\n   * @param {object} node - The Element node.\n   * @param {object} [opt] - Options.\n   * @returns {boolean} The result.\n   */\n  _matchHasPseudoFunc = (astLeaves, node, opt = {}) => {\n    if (Array.isArray(astLeaves) && astLeaves.length) {\n      // Prepare a copy to avoid astLeaves being consumed.\n      const leaves = [...astLeaves];\n      const [leaf] = leaves;\n      const { type: leafType } = leaf;\n      let combo;\n      if (leafType === COMBINATOR) {\n        combo = leaves.shift();\n      } else {\n        combo = {\n          name: ' ',\n          type: COMBINATOR\n        };\n      }\n      const twigLeaves = [];\n      while (leaves.length) {\n        const [item] = leaves;\n        const { type: itemType } = item;\n        if (itemType === COMBINATOR) {\n          break;\n        } else {\n          twigLeaves.push(leaves.shift());\n        }\n      }\n      const twig = {\n        combo,\n        leaves: twigLeaves\n      };\n      opt.dir = DIR_NEXT;\n      const nodes = this._matchCombinator(twig, node, opt);\n      if (nodes.size) {\n        if (leaves.length) {\n          let bool = false;\n          for (const nextNode of nodes) {\n            bool = this._matchHasPseudoFunc(leaves, nextNode, opt);\n            if (bool) {\n              break;\n            }\n          }\n          return bool;\n        }\n        return true;\n      }\n    }\n    return false;\n  };\n\n  /**\n   * Gets nth-of-type related pseudo-classes (:first-of-type, etc.).\n   * @private\n   * @param {string} astName - The name of the pseudo-class.\n   * @param {object} node - The Element node.\n   * @returns {?object} The matched node or null.\n   */\n  _getNthOfTypePseudo = (astName, node) => {\n    switch (astName) {\n      case 'first-of-type': {\n        const [first] = this._collectNthOfType({ a: 0, b: 1 }, node);\n        return first;\n      }\n      case 'last-of-type': {\n        const [last] = this._collectNthOfType(\n          { a: 0, b: 1, reverse: true },\n          node\n        );\n        return last;\n      }\n      // 'only-of-type' is handled by default.\n      default: {\n        const [first] = this._collectNthOfType({ a: 0, b: 1 }, node);\n        if (first === node) {\n          const [last] = this._collectNthOfType(\n            { a: 0, b: 1, reverse: true },\n            node\n          );\n          if (last === node) {\n            return node;\n          }\n        }\n        return null;\n      }\n    }\n  };\n\n  /**\n   * Matches child-position pseudo-classes (:first-child, :last-child,\n   * :only-child).\n   * @private\n   * @param {string} astName - The name of the pseudo-class.\n   * @param {object} node - The Element node.\n   * @returns {boolean} True if matched.\n   */\n  _matchChildPositionPseudo = (astName, node) => {\n    if (node === this.#root) {\n      return true;\n    }\n    const { parentNode } = node;\n    switch (astName) {\n      case 'first-child': {\n        return node === parentNode.firstElementChild;\n      }\n      case 'last-child': {\n        return node === parentNode.lastElementChild;\n      }\n      // 'only-child' is handled by default.\n      default: {\n        return (\n          node === parentNode.firstElementChild &&\n          node === parentNode.lastElementChild\n        );\n      }\n    }\n  };\n\n  /**\n   * Matches link-related pseudo-classes.\n   * @private\n   * @param {string} astName - The name of the pseudo-class.\n   * @param {object} node - The Element node.\n   * @returns {boolean} True if matched.\n   */\n  _matchLinkPseudo = (astName, node) => {\n    const { localName } = node;\n    const isLinkElement =\n      (localName === 'a' || localName === 'area') && node.hasAttribute('href');\n    if (!isLinkElement) {\n      return false;\n    }\n    switch (astName) {\n      case 'local-link': {\n        const { href, origin, pathname } = this.#documentURL;\n        const attrURL = new URL(node.getAttribute('href'), href);\n        return attrURL.origin === origin && attrURL.pathname === pathname;\n      }\n      case 'visited': {\n        // Prevent fingerprinting; never match.\n        return false;\n      }\n      // 'any-link' and 'link' are handled by default.\n      default: {\n        return true;\n      }\n    }\n  };\n\n  /**\n   * Matches focus-related pseudo-classes.\n   * @private\n   * @param {string} astName - The name of the pseudo-class.\n   * @param {object} node - The Element node.\n   * @returns {boolean} True if matched.\n   */\n  _matchFocusPseudo = (astName, node) => {\n    switch (astName) {\n      case 'focus-visible': {\n        if (node === this.#document.activeElement && isFocusableArea(node)) {\n          let bool = false;\n          if (isFocusVisible(node)) {\n            bool = true;\n          } else if (this.#focus) {\n            const { relatedTarget, target: focusTarget } = this.#focus;\n            if (focusTarget === node) {\n              if (isFocusVisible(relatedTarget)) {\n                bool = true;\n              } else if (this.#event) {\n                const {\n                  key: eventKey,\n                  target: eventTarget,\n                  type: eventType\n                } = this.#event;\n                // Event is irrelevant if its target is the relatedTarget.\n                if (eventTarget === relatedTarget) {\n                  if (this.#lastFocusVisible === null) {\n                    bool = true;\n                  } else if (focusTarget === this.#lastFocusVisible) {\n                    bool = true;\n                  }\n                } else if (eventKey === 'Tab') {\n                  if (\n                    (eventType === 'keydown' && eventTarget !== node) ||\n                    (eventType === 'keyup' && eventTarget === node)\n                  ) {\n                    if (eventTarget === focusTarget) {\n                      if (this.#lastFocusVisible === null) {\n                        bool = true;\n                      } else if (\n                        eventTarget === this.#lastFocusVisible &&\n                        relatedTarget === null\n                      ) {\n                        bool = true;\n                      }\n                    } else {\n                      bool = true;\n                    }\n                  }\n                } else if (eventKey) {\n                  if (\n                    (eventType === 'keydown' || eventType === 'keyup') &&\n                    eventTarget === node\n                  ) {\n                    bool = true;\n                  }\n                }\n              } else if (\n                relatedTarget === null ||\n                relatedTarget === this.#lastFocusVisible\n              ) {\n                bool = true;\n              }\n            }\n          }\n          if (bool) {\n            this.#lastFocusVisible = node;\n            return true;\n          } else if (this.#lastFocusVisible === node) {\n            this.#lastFocusVisible = null;\n          }\n        }\n        return false;\n      }\n      case 'focus-within': {\n        let current = this.#document.activeElement;\n        if (isFocusableArea(current)) {\n          while (current) {\n            if (current === node) {\n              return true;\n            }\n            current = current.parentNode;\n          }\n        }\n        return false;\n      }\n      // 'focus' is handled by default.\n      default: {\n        return node === this.#document.activeElement && isFocusableArea(node);\n      }\n    }\n  };\n\n  /**\n   * Matches :hover and :active pseudo-classes.\n   * @private\n   * @param {string} astName - The name of the pseudo-class.\n   * @param {object} node - The Element node.\n   * @returns {boolean} True if matched.\n   */\n  _matchHoverActivePseudo = (astName, node) => {\n    const { target, type, buttons } = this.#event ?? {};\n    if (!target) {\n      return false;\n    }\n    if (astName === 'hover') {\n      return (\n        /^(?:click|mouse(?:down|over|up))$/.test(type) && node.contains(target)\n      );\n    }\n    return type === 'mousedown' && buttons & BIT_01 && node.contains(target);\n  };\n\n  /**\n   * Matches a selector branch backwards from a given node.\n   * @private\n   * @param {Array.<object>} branch - The AST branch.\n   * @param {object} node - The node to start matching from.\n   * @param {object} opt - Options.\n   * @returns {boolean} True if the entire branch matches.\n   */\n  _matchBranchBackwards = (branch, node, opt) => {\n    let currentNodes = new Set([node]);\n    // Iterate from the second to last twig to the first.\n    for (let i = branch.length - 2; i >= 0; i--) {\n      const twig = branch[i];\n      const matchedNodes = new Set();\n      opt.dir = DIR_PREV;\n      for (const currentNode of currentNodes) {\n        const result = this._matchCombinator(twig, currentNode, opt);\n        if (result.size) {\n          const resultArr = [...result];\n          matchedNodes.add(...resultArr);\n        }\n      }\n      if (matchedNodes.size === 0) {\n        return false;\n      }\n      currentNodes = matchedNodes;\n    }\n    // The entire selector chain matched.\n    return true;\n  };\n\n  /**\n   * Evaluates the :has() pseudo-class.\n   * @private\n   * @param {object} astData - The AST data.\n   * @param {object} node - The Element node.\n   * @param {object} [opt] - Options.\n   * @returns {?object} The matched node.\n   */\n  _evaluateHasPseudo = (astData, node, opt) => {\n    const { branches } = astData;\n    let bool = false;\n    const l = branches.length;\n    for (let i = 0; i < l; i++) {\n      const leaves = branches[i];\n      bool = this._matchHasPseudoFunc(leaves, node, opt);\n      if (bool) {\n        break;\n      }\n    }\n    if (!bool) {\n      return null;\n    }\n    if (\n      (opt.isShadowRoot || this.#shadow) &&\n      node.nodeType === DOCUMENT_FRAGMENT_NODE\n    ) {\n      return this.#verifyShadowHost ? node : null;\n    }\n    return node;\n  };\n\n  /**\n   * Evaluates :is(), :not(), and :where() pseudo-classes.\n   * @private\n   * @param {object} astData - The AST data.\n   * @param {object} node - The Element node.\n   * @param {object} [opt] - Options.\n   * @returns {?object} The matched node.\n   */\n  _evaluateIsWhereNotPseudo = (astData, node, opt) => {\n    const { astName, branches, twigBranches } = astData;\n    const isShadowRoot =\n      (opt.isShadowRoot || this.#shadow) &&\n      node.nodeType === DOCUMENT_FRAGMENT_NODE;\n    if (isShadowRoot && !isValidShadowHostSelector(astName, branches)) {\n      return null;\n    }\n    opt.forgive = astName === 'is' || astName === 'where';\n    let branchMatched = false;\n    const l = twigBranches.length;\n    for (let i = 0; i < l; i++) {\n      const branch = twigBranches[i];\n      const { leaves: lastTwigLeaves } = branch[branch.length - 1];\n      const initialMatch = this._matchLeaves(lastTwigLeaves, node, opt);\n      if (!initialMatch) {\n        branchMatched = false;\n      } else if (branch.length === 1) {\n        branchMatched = true;\n        break;\n      } else {\n        // If there are combinators, match backwards.\n        branchMatched = this._matchBranchBackwards(branch, node, opt);\n        if (branchMatched) {\n          break;\n        }\n      }\n    }\n    if (astName === 'not') {\n      return branchMatched ? null : node;\n    }\n    return branchMatched ? node : null;\n  };\n\n  /**\n   * Matches logical pseudo-class functions.\n   * @private\n   * @param {object} astData - The AST data.\n   * @param {object} node - The Element node.\n   * @param {object} [opt] - Options.\n   * @returns {?object} The matched node.\n   */\n  _matchLogicalPseudoFunc = (astData, node, opt = {}) => {\n    const { astName } = astData;\n    // Handle :has().\n    if (astName === 'has') {\n      return this._evaluateHasPseudo(astData, node, opt);\n    }\n    // Handle :is(), :not(), :where().\n    return this._evaluateIsWhereNotPseudo(astData, node, opt);\n  };\n\n  /**\n   * Matches form-related pseudo-classes.\n   * @private\n   * @param {string} astName - The name of the pseudo-class.\n   * @param {object} node - The Element node.\n   * @returns {boolean} True if matched.\n   */\n  _matchFormPseudo = (astName, node) => {\n    const { localName } = node;\n    switch (astName) {\n      case 'checked': {\n        const attrType = node.getAttribute('type');\n        if (\n          node.checked &&\n          localName === 'input' &&\n          (attrType === 'checkbox' || attrType === 'radio')\n        ) {\n          return true;\n        } else if (node.selected && localName === 'option') {\n          return true;\n        }\n        return false;\n      }\n      case 'default': {\n        // Default submit buttons.\n        const attrType = node.getAttribute('type');\n        if (\n          (localName === 'button' &&\n            !(node.hasAttribute('type') && KEY_INPUT_RESET.has(attrType))) ||\n          (localName === 'input' &&\n            node.hasAttribute('type') &&\n            KEY_INPUT_SUBMIT.has(attrType))\n        ) {\n          let form = node.parentNode;\n          while (form) {\n            if (form.localName === 'form') {\n              break;\n            }\n            form = form.parentNode;\n          }\n          if (form) {\n            const walker = this._createTreeWalker(form);\n            let refNode = traverseNode(form, walker);\n            refNode = walker.firstChild();\n            while (refNode) {\n              const nodeName = refNode.localName;\n              const nodeAttrType = refNode.getAttribute('type');\n              let m;\n              if (nodeName === 'button') {\n                m = !(\n                  refNode.hasAttribute('type') &&\n                  KEY_INPUT_RESET.has(nodeAttrType)\n                );\n              } else if (nodeName === 'input') {\n                m =\n                  refNode.hasAttribute('type') &&\n                  KEY_INPUT_SUBMIT.has(nodeAttrType);\n              }\n              if (m) {\n                return refNode === node;\n              }\n              refNode = walker.nextNode();\n            }\n          }\n          // Default checked elements.\n        } else if (\n          localName === 'input' &&\n          node.hasAttribute('type') &&\n          KEY_INPUT_CHECK.has(attrType) &&\n          node.hasAttribute('checked')\n        ) {\n          return true;\n          // Default selected option.\n        } else if (localName === 'option' && node.hasAttribute('selected')) {\n          return true;\n        }\n        return false;\n      }\n      case 'disabled':\n      case 'enabled': {\n        return matchDisabledEnabledPseudo(astName, node);\n      }\n      case 'in-range':\n      case 'out-of-range': {\n        const attrType = node.getAttribute('type');\n        if (\n          localName === 'input' &&\n          !(node.readonly || node.hasAttribute('readonly')) &&\n          !(node.disabled || node.hasAttribute('disabled')) &&\n          KEY_INPUT_RANGE.has(attrType)\n        ) {\n          const flowed =\n            node.validity.rangeUnderflow || node.validity.rangeOverflow;\n          if (astName === 'out-of-range' && flowed) {\n            return true;\n          } else if (\n            astName === 'in-range' &&\n            !flowed &&\n            (node.hasAttribute('min') ||\n              node.hasAttribute('max') ||\n              attrType === 'range')\n          ) {\n            return true;\n          }\n        }\n        return false;\n      }\n      case 'indeterminate': {\n        if (\n          (node.indeterminate &&\n            localName === 'input' &&\n            node.type === 'checkbox') ||\n          (localName === 'progress' && !node.hasAttribute('value'))\n        ) {\n          return true;\n        } else if (\n          localName === 'input' &&\n          node.type === 'radio' &&\n          !node.hasAttribute('checked')\n        ) {\n          const nodeName = node.name;\n          let parent = node.parentNode;\n          while (parent) {\n            if (parent.localName === 'form') {\n              break;\n            }\n            parent = parent.parentNode;\n          }\n          if (!parent) {\n            parent = this.#document.documentElement;\n          }\n          const walker = this._createTreeWalker(parent);\n          let refNode = traverseNode(parent, walker);\n          refNode = walker.firstChild();\n          let checked;\n          while (refNode) {\n            if (\n              refNode.localName === 'input' &&\n              refNode.getAttribute('type') === 'radio'\n            ) {\n              if (refNode.hasAttribute('name')) {\n                if (refNode.getAttribute('name') === nodeName) {\n                  checked = !!refNode.checked;\n                }\n              } else {\n                checked = !!refNode.checked;\n              }\n              if (checked) {\n                break;\n              }\n            }\n            refNode = walker.nextNode();\n          }\n          return !checked;\n        }\n        return false;\n      }\n      case 'invalid':\n      case 'valid': {\n        if (KEY_FORM_PS_VALID.has(localName)) {\n          let valid;\n          if (node.checkValidity()) {\n            if (node.maxLength >= 0) {\n              if (node.maxLength >= node.value.length) {\n                valid = true;\n              }\n            } else {\n              valid = true;\n            }\n          }\n          if (valid) {\n            return astName === 'valid';\n          }\n          return astName === 'invalid';\n        } else if (localName === 'fieldset') {\n          const walker = this._createTreeWalker(node);\n          let refNode = traverseNode(node, walker);\n          refNode = walker.firstChild();\n          let valid;\n          if (!refNode) {\n            valid = true;\n          } else {\n            while (refNode) {\n              if (KEY_FORM_PS_VALID.has(refNode.localName)) {\n                if (refNode.checkValidity()) {\n                  if (refNode.maxLength >= 0) {\n                    valid = refNode.maxLength >= refNode.value.length;\n                  } else {\n                    valid = true;\n                  }\n                } else {\n                  valid = false;\n                }\n                if (!valid) {\n                  break;\n                }\n              }\n              refNode = walker.nextNode();\n            }\n          }\n          if (valid) {\n            return astName === 'valid';\n          }\n          return astName === 'invalid';\n        }\n        return false;\n      }\n      case 'optional':\n      case 'required': {\n        let required;\n        let optional;\n        if (localName === 'select' || localName === 'textarea') {\n          if (node.required || node.hasAttribute('required')) {\n            required = true;\n          } else {\n            optional = true;\n          }\n        } else if (localName === 'input') {\n          if (node.hasAttribute('type')) {\n            const attrType = node.getAttribute('type');\n            if (KEY_INPUT_REQUIRED.has(attrType)) {\n              if (node.required || node.hasAttribute('required')) {\n                required = true;\n              } else {\n                optional = true;\n              }\n            } else {\n              optional = true;\n            }\n          } else if (node.required || node.hasAttribute('required')) {\n            required = true;\n          } else {\n            optional = true;\n          }\n        }\n        if (astName === 'required' && required) {\n          return true;\n        } else if (astName === 'optional' && optional) {\n          return true;\n        }\n        return false;\n      }\n      case 'placeholder-shown': {\n        let placeholder;\n        if (node.placeholder) {\n          placeholder = node.placeholder;\n        } else if (node.hasAttribute('placeholder')) {\n          placeholder = node.getAttribute('placeholder');\n        }\n        if (typeof placeholder === 'string' && !/[\\r\\n]/.test(placeholder)) {\n          let targetNode;\n          if (localName === 'textarea') {\n            targetNode = node;\n          } else if (localName === 'input') {\n            if (node.hasAttribute('type')) {\n              if (KEY_INPUT_PLACEHOLDER.has(node.getAttribute('type'))) {\n                targetNode = node;\n              }\n            } else {\n              targetNode = node;\n            }\n          }\n          if (targetNode && node.value === '') {\n            return true;\n          }\n        }\n        return false;\n      }\n      case 'read-only':\n      case 'read-write': {\n        return matchReadOnlyWritePseudo(astName, node);\n      }\n      default: {\n        return false;\n      }\n    }\n  };\n\n  /**\n   * Matches UI state pseudo-classes.\n   * @private\n   * @param {string} astName - The name of the pseudo-class.\n   * @param {object} node - The Element node.\n   * @returns {boolean} True if matched.\n   */\n  _matchUIStatePseudo = (astName, node) => {\n    const { localName } = node;\n    switch (astName) {\n      case 'popover-open': {\n        return node.popover && isVisible(node);\n      }\n      case 'closed':\n      case 'open':\n      default: {\n        if (localName === 'details' || localName === 'dialog') {\n          if (node.hasAttribute('open')) {\n            return astName === 'open';\n          }\n          return astName === 'closed';\n        }\n        return false;\n      }\n    }\n  };\n\n  /**\n   * Matches a pseudo-class selector.\n   * @private\n   * @see https://html.spec.whatwg.org/#pseudo-classes\n   * @param {object} ast - The AST.\n   * @param {object} node - The Element node.\n   * @param {object} [opt] - Options.\n   * @returns {Set.<object>} A collection of matched nodes.\n   */\n  _matchPseudoClassSelector = (ast, node, opt = {}) => {\n    const { children: astChildren, name: astName } = ast;\n    const { localName } = node;\n    const { forgive, warn = this.#warn } = opt;\n    const matched = new Set();\n    /* :has(), :is(), :not(), :where() */\n    if (Array.isArray(astChildren) && KEY_LOGICAL.has(astName)) {\n      if (!astChildren.length && astName !== 'is' && astName !== 'where') {\n        const css = generateCSS(ast);\n        const msg = `Invalid selector ${css}`;\n        return this.onError(generateException(msg, SYNTAX_ERR, this.#window));\n      }\n      let astData;\n      if (this.#astCache.has(ast)) {\n        astData = this.#astCache.get(ast);\n      } else {\n        const { branches } = walkAST(ast);\n        if (astName === 'has') {\n          // Check for nested :has().\n          let forgiven = false;\n          const l = astChildren.length;\n          for (let i = 0; i < l; i++) {\n            const child = astChildren[i];\n            const item = findAST(child, findLogicalWithNestedHas);\n            if (item) {\n              const itemName = item.name;\n              if (itemName === 'is' || itemName === 'where') {\n                forgiven = true;\n                break;\n              } else {\n                const css = generateCSS(ast);\n                const msg = `Invalid selector ${css}`;\n                return this.onError(\n                  generateException(msg, SYNTAX_ERR, this.#window)\n                );\n              }\n            }\n          }\n          if (forgiven) {\n            return matched;\n          }\n          astData = {\n            astName,\n            branches\n          };\n        } else {\n          const twigBranches = [];\n          const l = branches.length;\n          for (let i = 0; i < l; i++) {\n            const [...leaves] = branches[i];\n            const branch = [];\n            const leavesSet = new Set();\n            let item = leaves.shift();\n            while (item) {\n              if (item.type === COMBINATOR) {\n                branch.push({\n                  combo: item,\n                  leaves: [...leavesSet]\n                });\n                leavesSet.clear();\n              } else if (item) {\n                leavesSet.add(item);\n              }\n              if (leaves.length) {\n                item = leaves.shift();\n              } else {\n                branch.push({\n                  combo: null,\n                  leaves: [...leavesSet]\n                });\n                leavesSet.clear();\n                break;\n              }\n            }\n            twigBranches.push(branch);\n          }\n          astData = {\n            astName,\n            branches,\n            twigBranches\n          };\n          this.#astCache.set(ast, astData);\n        }\n      }\n      const res = this._matchLogicalPseudoFunc(astData, node, opt);\n      if (res) {\n        matched.add(res);\n      }\n    } else if (Array.isArray(astChildren)) {\n      // Functional pseudo-classes like :nth-child().\n      if (/^nth-(?:last-)?(?:child|of-type)$/.test(astName)) {\n        if (astChildren.length !== 1) {\n          const css = generateCSS(ast);\n          const msg = `Invalid selector ${css}`;\n          return this.onError(generateException(msg, SYNTAX_ERR, this.#window));\n        }\n        const [branch] = astChildren;\n        const nodes = this._matchAnPlusB(branch, node, astName, opt);\n        return nodes;\n      } else {\n        switch (astName) {\n          case 'dir': {\n            if (astChildren.length !== 1) {\n              const css = generateCSS(ast);\n              const msg = `Invalid selector ${css}`;\n              return this.onError(\n                generateException(msg, SYNTAX_ERR, this.#window)\n              );\n            }\n            const [astChild] = astChildren;\n            const res = matchDirectionPseudoClass(astChild, node);\n            if (res) {\n              matched.add(node);\n            }\n            break;\n          }\n          case 'lang': {\n            if (!astChildren.length) {\n              const css = generateCSS(ast);\n              const msg = `Invalid selector ${css}`;\n              return this.onError(\n                generateException(msg, SYNTAX_ERR, this.#window)\n              );\n            }\n            let bool;\n            const l = astChildren.length;\n            for (let i = 0; i < l; i++) {\n              const astChild = astChildren[i];\n              bool = matchLanguagePseudoClass(astChild, node);\n              if (bool) {\n                break;\n              }\n            }\n            if (bool) {\n              matched.add(node);\n            }\n            break;\n          }\n          case 'state': {\n            if (isCustomElement(node)) {\n              const [{ value: stateValue }] = astChildren;\n              if (stateValue) {\n                if (node[stateValue]) {\n                  matched.add(node);\n                } else {\n                  for (const i in node) {\n                    const prop = node[i];\n                    if (prop instanceof this.#window.ElementInternals) {\n                      if (prop?.states?.has(stateValue)) {\n                        matched.add(node);\n                      }\n                      break;\n                    }\n                  }\n                }\n              }\n            }\n            break;\n          }\n          case 'current':\n          case 'nth-col':\n          case 'nth-last-col': {\n            if (warn) {\n              const msg = `Unsupported pseudo-class :${astName}()`;\n              this.onError(\n                generateException(msg, NOT_SUPPORTED_ERR, this.#window)\n              );\n            }\n            break;\n          }\n          // Ignore :host and :host-context in this context.\n          case 'host':\n          case 'host-context': {\n            break;\n          }\n          // Deprecated in CSS Selectors 3.\n          case 'contains': {\n            if (warn) {\n              const msg = `Unknown pseudo-class :${astName}()`;\n              this.onError(\n                generateException(msg, NOT_SUPPORTED_ERR, this.#window)\n              );\n            }\n            break;\n          }\n          default: {\n            if (!forgive) {\n              const msg = `Unknown pseudo-class :${astName}()`;\n              this.onError(generateException(msg, SYNTAX_ERR, this.#window));\n            }\n          }\n        }\n      }\n    } else {\n      let isMatch = false;\n      if (KEY_PS_FORM.has(astName)) {\n        isMatch = this._matchFormPseudo(astName, node);\n      } else if (KEY_PS_UI.has(astName)) {\n        isMatch = this._matchUIStatePseudo(astName, node);\n      } else {\n        switch (astName) {\n          case 'any-link':\n          case 'link':\n          case 'local-link':\n          case 'visited': {\n            if (this._matchLinkPseudo(astName, node)) {\n              isMatch = true;\n            }\n            break;\n          }\n          case 'hover':\n          case 'active': {\n            if (this._matchHoverActivePseudo(astName, node)) {\n              isMatch = true;\n            }\n            break;\n          }\n          case 'target': {\n            const { hash } = this.#documentURL;\n            if (\n              node.id &&\n              hash === `#${node.id}` &&\n              this.#document.contains(node)\n            ) {\n              isMatch = true;\n            }\n            break;\n          }\n          case 'target-within': {\n            const { hash } = this.#documentURL;\n            if (hash) {\n              const id = hash.replace(/^#/, '');\n              let current = this.#document.getElementById(id);\n              while (current) {\n                if (current === node) {\n                  isMatch = true;\n                  break;\n                }\n                current = current.parentNode;\n              }\n            }\n            break;\n          }\n          case 'scope': {\n            if (this.#node.nodeType === ELEMENT_NODE) {\n              if (!this.#shadow && node === this.#node) {\n                isMatch = true;\n              }\n            } else if (node === this.#document.documentElement) {\n              isMatch = true;\n            }\n            break;\n          }\n          case 'focus':\n          case 'focus-visible':\n          case 'focus-within': {\n            if (this._matchFocusPseudo(astName, node)) {\n              isMatch = true;\n            }\n            break;\n          }\n          case 'root': {\n            if (node === this.#document.documentElement) {\n              isMatch = true;\n            }\n            break;\n          }\n          case 'empty': {\n            if (node.hasChildNodes()) {\n              const walker = this._createTreeWalker(node, {\n                force: true,\n                whatToShow: SHOW_ALL\n              });\n              let refNode = walker.firstChild();\n              let bool;\n              while (refNode) {\n                bool =\n                  refNode.nodeType !== ELEMENT_NODE &&\n                  refNode.nodeType !== TEXT_NODE;\n                if (!bool) {\n                  break;\n                }\n                refNode = walker.nextSibling();\n              }\n              if (bool) {\n                isMatch = true;\n              }\n            } else {\n              isMatch = true;\n            }\n            break;\n          }\n          case 'first-child':\n          case 'last-child':\n          case 'only-child': {\n            if (this._matchChildPositionPseudo(astName, node)) {\n              isMatch = true;\n            }\n            break;\n          }\n          case 'first-of-type':\n          case 'last-of-type':\n          case 'only-of-type': {\n            if (node === this.#root) {\n              matched.add(node);\n            } else {\n              const matchedNode = this._getNthOfTypePseudo(astName, node);\n              if (matchedNode) {\n                matched.add(matchedNode);\n              }\n            }\n            break;\n          }\n          case 'defined': {\n            if (node.hasAttribute('is') || localName.includes('-')) {\n              if (isCustomElement(node)) {\n                isMatch = true;\n              }\n              // NOTE: MathMLElement is not implemented in jsdom.\n            } else if (\n              node instanceof this.#window.HTMLElement ||\n              node instanceof this.#window.SVGElement\n            ) {\n              isMatch = true;\n            }\n            break;\n          }\n          case 'host':\n          case 'host-context': {\n            // This is handled in _matchSelectorForShadowRoot.\n            break;\n          }\n          // Legacy pseudo-elements.\n          case 'after':\n          case 'before':\n          case 'first-letter':\n          case 'first-line': {\n            if (warn) {\n              const msg = `Unsupported pseudo-element ::${astName}`;\n              this.onError(\n                generateException(msg, NOT_SUPPORTED_ERR, this.#window)\n              );\n            }\n            break;\n          }\n          // Unsupported pseudo-classes.\n          case 'autofill':\n          case 'blank':\n          case 'buffering':\n          case 'current':\n          case 'fullscreen':\n          case 'future':\n          case 'has-slotted':\n          case 'modal':\n          case 'muted':\n          case 'past':\n          case 'paused':\n          case 'picture-in-picture':\n          case 'playing':\n          case 'seeking':\n          case 'stalled':\n          case 'user-invalid':\n          case 'user-valid':\n          case 'volume-locked':\n          case '-webkit-autofill': {\n            if (warn) {\n              const msg = `Unsupported pseudo-class :${astName}`;\n              this.onError(\n                generateException(msg, NOT_SUPPORTED_ERR, this.#window)\n              );\n            }\n            break;\n          }\n          default: {\n            if (astName.startsWith('-webkit-')) {\n              if (warn) {\n                const msg = `Unsupported pseudo-class :${astName}`;\n                this.onError(\n                  generateException(msg, NOT_SUPPORTED_ERR, this.#window)\n                );\n              }\n            } else if (!forgive) {\n              const msg = `Unknown pseudo-class :${astName}`;\n              this.onError(generateException(msg, SYNTAX_ERR, this.#window));\n            }\n          }\n        }\n      }\n      if (isMatch) {\n        matched.add(node);\n      }\n    }\n    return matched;\n  };\n\n  /**\n   * Evaluates the :host() pseudo-class.\n   * @private\n   * @param {Array.<object>} leaves - The AST leaves.\n   * @param {object} host - The host element.\n   * @param {object} ast - The original AST for error reporting.\n   * @returns {boolean} True if matched.\n   */\n  _evaluateHostPseudo = (leaves, host, ast) => {\n    const l = leaves.length;\n    for (let i = 0; i < l; i++) {\n      const leaf = leaves[i];\n      if (leaf.type === COMBINATOR) {\n        const css = generateCSS(ast);\n        const msg = `Invalid selector ${css}`;\n        this.onError(generateException(msg, SYNTAX_ERR, this.#window));\n        return false;\n      }\n      if (!this._matchSelector(leaf, host).has(host)) {\n        return false;\n      }\n    }\n    return true;\n  };\n\n  /**\n   * Evaluates the :host-context() pseudo-class.\n   * @private\n   * @param {Array.<object>} leaves - The AST leaves.\n   * @param {object} host - The host element.\n   * @param {object} ast - The original AST for error reporting.\n   * @returns {boolean} True if matched.\n   */\n  _evaluateHostContextPseudo = (leaves, host, ast) => {\n    let parent = host;\n    while (parent) {\n      let bool;\n      const l = leaves.length;\n      for (let i = 0; i < l; i++) {\n        const leaf = leaves[i];\n        if (leaf.type === COMBINATOR) {\n          const css = generateCSS(ast);\n          const msg = `Invalid selector ${css}`;\n          this.onError(generateException(msg, SYNTAX_ERR, this.#window));\n          return false;\n        }\n        bool = this._matchSelector(leaf, parent).has(parent);\n        if (!bool) {\n          break;\n        }\n      }\n      if (bool) {\n        return true;\n      }\n      parent = parent.parentNode;\n    }\n    return false;\n  };\n\n  /**\n   * Matches shadow host pseudo-classes.\n   * @private\n   * @param {object} ast - The AST.\n   * @param {object} node - The DocumentFragment node.\n   * @returns {?object} The matched node.\n   */\n  _matchShadowHostPseudoClass = (ast, node) => {\n    const { children: astChildren, name: astName } = ast;\n    // Handle simple pseudo-class (no arguments).\n    if (!Array.isArray(astChildren)) {\n      if (astName === 'host') {\n        return node;\n      }\n      const msg = `Invalid selector :${astName}`;\n      return this.onError(generateException(msg, SYNTAX_ERR, this.#window));\n    }\n    // Handle functional pseudo-class like :host(...).\n    if (astName !== 'host' && astName !== 'host-context') {\n      const msg = `Invalid selector :${astName}()`;\n      return this.onError(generateException(msg, SYNTAX_ERR, this.#window));\n    }\n    if (astChildren.length !== 1) {\n      const css = generateCSS(ast);\n      const msg = `Invalid selector ${css}`;\n      return this.onError(generateException(msg, SYNTAX_ERR, this.#window));\n    }\n    const { host } = node;\n    const { branches } = walkAST(astChildren[0]);\n    const [branch] = branches;\n    const [...leaves] = branch;\n    if (astName === 'host') {\n      const isMatch = this._evaluateHostPseudo(leaves, host, ast);\n      return isMatch ? node : null;\n    }\n    // astName === 'host-context'.\n    const isMatch = this._evaluateHostContextPseudo(leaves, host, ast);\n    return isMatch ? node : null;\n  };\n\n  /**\n   * Matches a selector for element nodes.\n   * @private\n   * @param {object} ast - The AST.\n   * @param {object} node - The Element node.\n   * @param {object} [opt] - Options.\n   * @returns {Set.<object>} A collection of matched nodes.\n   */\n  _matchSelectorForElement = (ast, node, opt = {}) => {\n    const { type: astType } = ast;\n    const astName = unescapeSelector(ast.name);\n    const matched = new Set();\n    switch (astType) {\n      case ATTR_SELECTOR: {\n        if (matchAttributeSelector(ast, node, opt)) {\n          matched.add(node);\n        }\n        break;\n      }\n      case ID_SELECTOR: {\n        if (node.id === astName) {\n          matched.add(node);\n        }\n        break;\n      }\n      case CLASS_SELECTOR: {\n        if (node.classList.contains(astName)) {\n          matched.add(node);\n        }\n        break;\n      }\n      case PS_CLASS_SELECTOR: {\n        return this._matchPseudoClassSelector(ast, node, opt);\n      }\n      case TYPE_SELECTOR: {\n        if (matchTypeSelector(ast, node, opt)) {\n          matched.add(node);\n        }\n        break;\n      }\n      case PS_ELEMENT_SELECTOR:\n      default: {\n        try {\n          if (opt.check) {\n            const css = generateCSS(ast);\n            this.#pseudoElement.push(css);\n            matched.add(node);\n          } else {\n            matchPseudoElementSelector(astName, astType, opt);\n          }\n        } catch (e) {\n          this.onError(e);\n        }\n      }\n    }\n    return matched;\n  };\n\n  /**\n   * Matches a selector for a shadow root.\n   * @private\n   * @param {object} ast - The AST.\n   * @param {object} node - The DocumentFragment node.\n   * @param {object} [opt] - Options.\n   * @returns {Set.<object>} A collection of matched nodes.\n   */\n  _matchSelectorForShadowRoot = (ast, node, opt = {}) => {\n    const { name: astName } = ast;\n    const matched = new Set();\n    if (KEY_LOGICAL.has(astName)) {\n      opt.isShadowRoot = true;\n      return this._matchPseudoClassSelector(ast, node, opt);\n    } else if (astName === 'host' || astName === 'host-context') {\n      const res = this._matchShadowHostPseudoClass(ast, node, opt);\n      if (res) {\n        this.#verifyShadowHost = true;\n        matched.add(res);\n      }\n    }\n    return matched;\n  };\n\n  /**\n   * Matches a selector.\n   * @private\n   * @param {object} ast - The AST.\n   * @param {object} node - The Document, DocumentFragment, or Element node.\n   * @param {object} [opt] - Options.\n   * @returns {Set.<object>} A collection of matched nodes.\n   */\n  _matchSelector = (ast, node, opt = {}) => {\n    if (node.nodeType === ELEMENT_NODE) {\n      return this._matchSelectorForElement(ast, node, opt);\n    }\n    if (\n      this.#shadow &&\n      node.nodeType === DOCUMENT_FRAGMENT_NODE &&\n      ast.type === PS_CLASS_SELECTOR\n    ) {\n      return this._matchSelectorForShadowRoot(ast, node, opt);\n    }\n    return new Set();\n  };\n\n  /**\n   * Matches leaves.\n   * @private\n   * @param {Array.<object>} leaves - The AST leaves.\n   * @param {object} node - The node.\n   * @param {object} [opt] - Options.\n   * @returns {boolean} The result.\n   */\n  _matchLeaves = (leaves, node, opt = {}) => {\n    const results = this.#invalidate ? this.#invalidateResults : this.#results;\n    let result = results.get(leaves);\n    if (result && result.has(node)) {\n      const { matched } = result.get(node);\n      return matched;\n    }\n    let cacheable = true;\n    if (node.nodeType === ELEMENT_NODE && KEY_FORM.has(node.localName)) {\n      cacheable = false;\n    }\n    let bool;\n    const l = leaves.length;\n    for (let i = 0; i < l; i++) {\n      const leaf = leaves[i];\n      switch (leaf.type) {\n        case ATTR_SELECTOR:\n        case ID_SELECTOR: {\n          cacheable = false;\n          break;\n        }\n        case PS_CLASS_SELECTOR: {\n          if (KEY_PS_UNCACHE.has(leaf.name)) {\n            cacheable = false;\n          }\n          break;\n        }\n        default: {\n          // No action needed for other types.\n        }\n      }\n      bool = this._matchSelector(leaf, node, opt).has(node);\n      if (!bool) {\n        break;\n      }\n    }\n    if (cacheable) {\n      if (!result) {\n        result = new WeakMap();\n      }\n      result.set(node, {\n        matched: bool\n      });\n      results.set(leaves, result);\n    }\n    return bool;\n  };\n\n  /**\n   * Traverses all descendant nodes and collects matches.\n   * @private\n   * @param {object} baseNode - The base Element node or Element.shadowRoot.\n   * @param {Array.<object>} leaves - The AST leaves.\n   * @param {object} [opt] - Options.\n   * @returns {Set.<object>} A collection of matched nodes.\n   */\n  _traverseAllDescendants = (baseNode, leaves, opt = {}) => {\n    const walker = this._createTreeWalker(baseNode);\n    traverseNode(baseNode, walker);\n    let currentNode = walker.firstChild();\n    const nodes = new Set();\n    while (currentNode) {\n      if (this._matchLeaves(leaves, currentNode, opt)) {\n        nodes.add(currentNode);\n      }\n      currentNode = walker.nextNode();\n    }\n    return nodes;\n  };\n\n  /**\n   * Finds descendant nodes.\n   * @private\n   * @param {Array.<object>} leaves - The AST leaves.\n   * @param {object} baseNode - The base Element node or Element.shadowRoot.\n   * @param {object} [opt] - Options.\n   * @returns {Set.<object>} A collection of matched nodes.\n   */\n  _findDescendantNodes = (leaves, baseNode, opt = {}) => {\n    const [leaf, ...filterLeaves] = leaves;\n    const { type: leafType } = leaf;\n    switch (leafType) {\n      case ID_SELECTOR: {\n        const canUseGetElementById =\n          !this.#shadow &&\n          baseNode.nodeType === ELEMENT_NODE &&\n          this.#root.nodeType !== ELEMENT_NODE;\n        if (canUseGetElementById) {\n          const leafName = unescapeSelector(leaf.name);\n          const nodes = new Set();\n          const foundNode = this.#root.getElementById(leafName);\n          if (\n            foundNode &&\n            foundNode !== baseNode &&\n            baseNode.contains(foundNode)\n          ) {\n            const isCompoundSelector = filterLeaves.length > 0;\n            if (\n              !isCompoundSelector ||\n              this._matchLeaves(filterLeaves, foundNode, opt)\n            ) {\n              nodes.add(foundNode);\n            }\n          }\n          return nodes;\n        }\n        // Fallback to default traversal if fast path is not applicable.\n        return this._traverseAllDescendants(baseNode, leaves, opt);\n      }\n      case PS_ELEMENT_SELECTOR: {\n        const leafName = unescapeSelector(leaf.name);\n        matchPseudoElementSelector(leafName, leafType, opt);\n        return new Set();\n      }\n      default: {\n        return this._traverseAllDescendants(baseNode, leaves, opt);\n      }\n    }\n  };\n\n  /**\n   * Matches the descendant combinator ' '.\n   * @private\n   * @param {object} twig - The twig object.\n   * @param {object} node - The Element node.\n   * @param {object} [opt] - Options.\n   * @returns {Set.<object>} A collection of matched nodes.\n   */\n  _matchDescendantCombinator = (twig, node, opt = {}) => {\n    const { leaves } = twig;\n    const { parentNode } = node;\n    const { dir } = opt;\n    if (dir === DIR_NEXT) {\n      return this._findDescendantNodes(leaves, node, opt);\n    }\n    // DIR_PREV\n    const ancestors = [];\n    let refNode = parentNode;\n    while (refNode) {\n      if (this._matchLeaves(leaves, refNode, opt)) {\n        ancestors.push(refNode);\n      }\n      refNode = refNode.parentNode;\n    }\n    if (ancestors.length) {\n      // Reverse to maintain document order.\n      return new Set(ancestors.reverse());\n    }\n    return new Set();\n  };\n\n  /**\n   * Matches the child combinator '>'.\n   * @private\n   * @param {object} twig - The twig object.\n   * @param {object} node - The Element node.\n   * @param {object} [opt] - Options.\n   * @returns {Set.<object>} A collection of matched nodes.\n   */\n  _matchChildCombinator = (twig, node, opt = {}) => {\n    const { leaves } = twig;\n    const { dir } = opt;\n    const { parentNode } = node;\n    const matched = new Set();\n    if (dir === DIR_NEXT) {\n      let refNode = node.firstElementChild;\n      while (refNode) {\n        if (this._matchLeaves(leaves, refNode, opt)) {\n          matched.add(refNode);\n        }\n        refNode = refNode.nextElementSibling;\n      }\n    } else {\n      // DIR_PREV\n      if (parentNode && this._matchLeaves(leaves, parentNode, opt)) {\n        matched.add(parentNode);\n      }\n    }\n    return matched;\n  };\n\n  /**\n   * Matches the adjacent sibling combinator '+'.\n   * @private\n   * @param {object} twig - The twig object.\n   * @param {object} node - The Element node.\n   * @param {object} [opt] - Options.\n   * @returns {Set.<object>} A collection of matched nodes.\n   */\n  _matchAdjacentSiblingCombinator = (twig, node, opt = {}) => {\n    const { leaves } = twig;\n    const { dir } = opt;\n    const matched = new Set();\n    const refNode =\n      dir === DIR_NEXT ? node.nextElementSibling : node.previousElementSibling;\n    if (refNode && this._matchLeaves(leaves, refNode, opt)) {\n      matched.add(refNode);\n    }\n    return matched;\n  };\n\n  /**\n   * Matches the general sibling combinator '~'.\n   * @private\n   * @param {object} twig - The twig object.\n   * @param {object} node - The Element node.\n   * @param {object} [opt] - Options.\n   * @returns {Set.<object>} A collection of matched nodes.\n   */\n  _matchGeneralSiblingCombinator = (twig, node, opt = {}) => {\n    const { leaves } = twig;\n    const { dir } = opt;\n    const matched = new Set();\n    let refNode =\n      dir === DIR_NEXT ? node.nextElementSibling : node.previousElementSibling;\n    while (refNode) {\n      if (this._matchLeaves(leaves, refNode, opt)) {\n        matched.add(refNode);\n      }\n      refNode =\n        dir === DIR_NEXT\n          ? refNode.nextElementSibling\n          : refNode.previousElementSibling;\n    }\n    return matched;\n  };\n\n  /**\n   * Matches a combinator.\n   * @private\n   * @param {object} twig - The twig object.\n   * @param {object} node - The Element node.\n   * @param {object} [opt] - Options.\n   * @returns {Set.<object>} A collection of matched nodes.\n   */\n  _matchCombinator = (twig, node, opt = {}) => {\n    const {\n      combo: { name: comboName }\n    } = twig;\n    switch (comboName) {\n      case '+': {\n        return this._matchAdjacentSiblingCombinator(twig, node, opt);\n      }\n      case '~': {\n        return this._matchGeneralSiblingCombinator(twig, node, opt);\n      }\n      case '>': {\n        return this._matchChildCombinator(twig, node, opt);\n      }\n      case ' ':\n      default: {\n        return this._matchDescendantCombinator(twig, node, opt);\n      }\n    }\n  };\n\n  /**\n   * Traverses with a TreeWalker and collects nodes matching the leaves.\n   * @private\n   * @param {TreeWalker} walker - The TreeWalker instance to use.\n   * @param {Array} leaves - The AST leaves to match against.\n   * @param {object} options - Traversal options.\n   * @param {Node} options.startNode - The node to start traversal from.\n   * @param {string} options.targetType - The type of target ('all' or 'first').\n   * @param {Node} [options.boundaryNode] - The node to stop traversal at.\n   * @param {boolean} [options.force] - Force traversal to the next node.\n   * @returns {Array.<Node>} An array of matched nodes.\n   */\n  _traverseAndCollectNodes = (walker, leaves, options) => {\n    const { boundaryNode, force, startNode, targetType } = options;\n    const collectedNodes = [];\n    let currentNode = traverseNode(startNode, walker, !!force);\n    if (!currentNode) {\n      return [];\n    }\n    // Adjust starting node.\n    if (currentNode.nodeType !== ELEMENT_NODE) {\n      currentNode = walker.nextNode();\n    } else if (currentNode === startNode && currentNode !== this.#root) {\n      currentNode = walker.nextNode();\n    }\n    while (currentNode) {\n      // Stop when we reach the boundary node.\n      if (boundaryNode && currentNode === boundaryNode) {\n        break;\n      }\n      if (this._matchLeaves(leaves, currentNode, { warn: this.#warn })) {\n        collectedNodes.push(currentNode);\n        // Stop after the first match if not collecting all.\n        if (targetType !== TARGET_ALL) {\n          break;\n        }\n      }\n      currentNode = walker.nextNode();\n    }\n    return collectedNodes;\n  };\n\n  /**\n   * Finds matched node(s) preceding this.#node.\n   * @private\n   * @param {Array.<object>} leaves - The AST leaves.\n   * @param {object} node - The node to start from.\n   * @param {object} opt - Options.\n   * @param {boolean} [opt.force] - If true, traverses only to the next node.\n   * @param {string} [opt.targetType] - The target type.\n   * @returns {Array.<object>} A collection of matched nodes.\n   */\n  _findPrecede = (leaves, node, opt = {}) => {\n    const { force, targetType } = opt;\n    if (!this.#rootWalker) {\n      this.#rootWalker = this._createTreeWalker(this.#root);\n    }\n    return this._traverseAndCollectNodes(this.#rootWalker, leaves, {\n      force,\n      targetType,\n      boundaryNode: this.#node,\n      startNode: node\n    });\n  };\n\n  /**\n   * Finds matched node(s) in #nodeWalker.\n   * @private\n   * @param {Array.<object>} leaves - The AST leaves.\n   * @param {object} node - The node to start from.\n   * @param {object} opt - Options.\n   * @param {boolean} [opt.precede] - If true, finds preceding nodes.\n   * @returns {Array.<object>} A collection of matched nodes.\n   */\n  _findNodeWalker = (leaves, node, opt = {}) => {\n    const { precede, ...traversalOpts } = opt;\n    if (precede) {\n      const precedeNodes = this._findPrecede(leaves, this.#root, opt);\n      if (precedeNodes.length) {\n        return precedeNodes;\n      }\n    }\n    if (!this.#nodeWalker) {\n      this.#nodeWalker = this._createTreeWalker(this.#node);\n    }\n    return this._traverseAndCollectNodes(this.#nodeWalker, leaves, {\n      startNode: node,\n      ...traversalOpts\n    });\n  };\n\n  /**\n   * Matches the node itself.\n   * @private\n   * @param {Array} leaves - The AST leaves.\n   * @param {boolean} check - Indicates if running in internal check().\n   * @returns {Array} An array containing [nodes, filtered, pseudoElement].\n   */\n  _matchSelf = (leaves, check = false) => {\n    const options = { check, warn: this.#warn };\n    const matched = this._matchLeaves(leaves, this.#node, options);\n    const nodes = matched ? [this.#node] : [];\n    return [nodes, matched, this.#pseudoElement];\n  };\n\n  /**\n   * Finds lineal nodes (self and ancestors).\n   * @private\n   * @param {Array} leaves - The AST leaves.\n   * @param {object} opt - Options.\n   * @returns {Array} An array containing [nodes, filtered].\n   */\n  _findLineal = (leaves, opt) => {\n    const { complex } = opt;\n    const nodes = [];\n    const options = { warn: this.#warn };\n    const selfMatched = this._matchLeaves(leaves, this.#node, options);\n    if (selfMatched) {\n      nodes.push(this.#node);\n    }\n    if (!selfMatched || complex) {\n      let currentNode = this.#node.parentNode;\n      while (currentNode) {\n        if (this._matchLeaves(leaves, currentNode, options)) {\n          nodes.push(currentNode);\n        }\n        currentNode = currentNode.parentNode;\n      }\n    }\n    const filtered = nodes.length > 0;\n    return [nodes, filtered];\n  };\n\n  /**\n   * Finds entry nodes for pseudo-element selectors.\n   * @private\n   * @param {object} leaf - The pseudo-element leaf from the AST.\n   * @param {Array.<object>} filterLeaves - Leaves for compound selectors.\n   * @param {string} targetType - The type of target to find.\n   * @returns {object} The result { nodes, filtered, pending }.\n   */\n  _findEntryNodesForPseudoElement = (leaf, filterLeaves, targetType) => {\n    let nodes = [];\n    let filtered = false;\n    if (targetType === TARGET_SELF && this.#check) {\n      const css = generateCSS(leaf);\n      this.#pseudoElement.push(css);\n      if (filterLeaves.length) {\n        [nodes, filtered] = this._matchSelf(filterLeaves, this.#check);\n      } else {\n        nodes.push(this.#node);\n        filtered = true;\n      }\n    } else {\n      matchPseudoElementSelector(leaf.name, leaf.type, { warn: this.#warn });\n    }\n    return { nodes, filtered, pending: false };\n  };\n\n  /**\n   * Finds entry nodes for ID selectors.\n   * @private\n   * @param {object} twig - The current twig from the AST branch.\n   * @param {string} targetType - The type of target to find.\n   * @param {object} opt - Additional options for finding nodes.\n   * @returns {object} The result { nodes, filtered, pending }.\n   */\n  _findEntryNodesForId = (twig, targetType, opt) => {\n    const { leaves } = twig;\n    const [leaf, ...filterLeaves] = leaves;\n    const { complex, precede } = opt;\n    let nodes = [];\n    let filtered = false;\n    if (targetType === TARGET_SELF) {\n      [nodes, filtered] = this._matchSelf(leaves);\n    } else if (targetType === TARGET_LINEAL) {\n      [nodes, filtered] = this._findLineal(leaves, { complex });\n    } else if (\n      targetType === TARGET_FIRST &&\n      this.#root.nodeType !== ELEMENT_NODE\n    ) {\n      const node = this.#root.getElementById(leaf.name);\n      if (node) {\n        if (filterLeaves.length) {\n          if (this._matchLeaves(filterLeaves, node, { warn: this.#warn })) {\n            nodes.push(node);\n            filtered = true;\n          }\n        } else {\n          nodes.push(node);\n          filtered = true;\n        }\n      }\n    } else {\n      nodes = this._findNodeWalker(leaves, this.#node, { precede, targetType });\n      filtered = nodes.length > 0;\n    }\n    return { nodes, filtered, pending: false };\n  };\n\n  /**\n   * Finds entry nodes for class selectors.\n   * @private\n   * @param {Array.<object>} leaves - The AST leaves for the selector.\n   * @param {string} targetType - The type of target to find.\n   * @param {object} opt - Additional options for finding nodes.\n   * @returns {object} The result { nodes, filtered, pending }.\n   */\n  _findEntryNodesForClass = (leaves, targetType, opt) => {\n    const { complex, precede } = opt;\n    let nodes = [];\n    let filtered = false;\n    if (targetType === TARGET_SELF) {\n      [nodes, filtered] = this._matchSelf(leaves);\n    } else if (targetType === TARGET_LINEAL) {\n      [nodes, filtered] = this._findLineal(leaves, { complex });\n    } else {\n      nodes = this._findNodeWalker(leaves, this.#node, { precede, targetType });\n      filtered = nodes.length > 0;\n    }\n    return { nodes, filtered, pending: false };\n  };\n\n  /**\n   * Finds entry nodes for type selectors.\n   * @private\n   * @param {Array.<object>} leaves - The AST leaves for the selector.\n   * @param {string} targetType - The type of target to find.\n   * @param {object} opt - Additional options for finding nodes.\n   * @returns {object} The result { nodes, filtered, pending }.\n   */\n  _findEntryNodesForType = (leaves, targetType, opt) => {\n    const { complex, precede } = opt;\n    let nodes = [];\n    let filtered = false;\n    if (targetType === TARGET_SELF) {\n      [nodes, filtered] = this._matchSelf(leaves);\n    } else if (targetType === TARGET_LINEAL) {\n      [nodes, filtered] = this._findLineal(leaves, { complex });\n    } else {\n      nodes = this._findNodeWalker(leaves, this.#node, { precede, targetType });\n      filtered = nodes.length > 0;\n    }\n    return { nodes, filtered, pending: false };\n  };\n\n  /**\n   * Finds entry nodes for other selector types (default case).\n   * @private\n   * @param {object} twig - The current twig from the AST branch.\n   * @param {string} targetType - The type of target to find.\n   * @param {object} opt - Additional options for finding nodes.\n   * @returns {object} The result { nodes, filtered, pending }.\n   */\n  _findEntryNodesForOther = (twig, targetType, opt) => {\n    const { leaves } = twig;\n    const [leaf, ...filterLeaves] = leaves;\n    const { complex, precede } = opt;\n    let nodes = [];\n    let filtered = false;\n    let pending = false;\n    if (targetType !== TARGET_LINEAL && /host(?:-context)?/.test(leaf.name)) {\n      let shadowRoot = null;\n      if (this.#shadow && this.#node.nodeType === DOCUMENT_FRAGMENT_NODE) {\n        shadowRoot = this._matchShadowHostPseudoClass(leaf, this.#node);\n      } else if (filterLeaves.length && this.#node.nodeType === ELEMENT_NODE) {\n        shadowRoot = this._matchShadowHostPseudoClass(\n          leaf,\n          this.#node.shadowRoot\n        );\n      }\n      if (shadowRoot) {\n        let bool = true;\n        const l = filterLeaves.length;\n        for (let i = 0; i < l; i++) {\n          const filterLeaf = filterLeaves[i];\n          switch (filterLeaf.name) {\n            case 'host':\n            case 'host-context': {\n              const matchedNode = this._matchShadowHostPseudoClass(\n                filterLeaf,\n                shadowRoot\n              );\n              bool = matchedNode === shadowRoot;\n              break;\n            }\n            case 'has': {\n              bool = this._matchPseudoClassSelector(\n                filterLeaf,\n                shadowRoot,\n                {}\n              ).has(shadowRoot);\n              break;\n            }\n            default: {\n              bool = false;\n            }\n          }\n          if (!bool) {\n            break;\n          }\n        }\n        if (bool) {\n          nodes.push(shadowRoot);\n          filtered = true;\n        }\n      }\n    } else if (targetType === TARGET_SELF) {\n      [nodes, filtered] = this._matchSelf(leaves);\n    } else if (targetType === TARGET_LINEAL) {\n      [nodes, filtered] = this._findLineal(leaves, { complex });\n    } else if (targetType === TARGET_FIRST) {\n      nodes = this._findNodeWalker(leaves, this.#node, { precede, targetType });\n      filtered = nodes.length > 0;\n    } else {\n      pending = true;\n    }\n    return { nodes, filtered, pending };\n  };\n\n  /**\n   * Finds entry nodes.\n   * @private\n   * @param {object} twig - The twig object.\n   * @param {string} targetType - The target type.\n   * @param {object} [opt] - Options.\n   * @param {boolean} [opt.complex] - If true, the selector is complex.\n   * @param {string} [opt.dir] - The find direction.\n   * @returns {object} An object with nodes and their state.\n   */\n  _findEntryNodes = (twig, targetType, opt = {}) => {\n    const { leaves } = twig;\n    const [leaf, ...filterLeaves] = leaves;\n    const { complex = false, dir = DIR_PREV } = opt;\n    const precede =\n      dir === DIR_NEXT &&\n      this.#node.nodeType === ELEMENT_NODE &&\n      this.#node !== this.#root;\n    let result;\n    switch (leaf.type) {\n      case PS_ELEMENT_SELECTOR: {\n        result = this._findEntryNodesForPseudoElement(\n          leaf,\n          filterLeaves,\n          targetType\n        );\n        break;\n      }\n      case ID_SELECTOR: {\n        result = this._findEntryNodesForId(twig, targetType, {\n          complex,\n          precede\n        });\n        break;\n      }\n      case CLASS_SELECTOR: {\n        result = this._findEntryNodesForClass(leaves, targetType, {\n          complex,\n          precede\n        });\n        break;\n      }\n      case TYPE_SELECTOR: {\n        result = this._findEntryNodesForType(leaves, targetType, {\n          complex,\n          precede\n        });\n        break;\n      }\n      default: {\n        result = this._findEntryNodesForOther(twig, targetType, {\n          complex,\n          precede\n        });\n      }\n    }\n    return {\n      compound: filterLeaves.length > 0,\n      filtered: result.filtered,\n      nodes: result.nodes,\n      pending: result.pending\n    };\n  };\n\n  /**\n   * Determines the direction and starting twig for a selector branch.\n   * @private\n   * @param {Array.<object>} branch - The AST branch.\n   * @param {string} targetType - The type of target to find.\n   * @returns {object} An object with the direction and starting twig.\n   */\n  _determineTraversalStrategy = (branch, targetType) => {\n    const branchLen = branch.length;\n    const firstTwig = branch[0];\n    const lastTwig = branch[branchLen - 1];\n    if (branchLen === 1) {\n      return { dir: DIR_PREV, twig: firstTwig };\n    }\n    // Complex selector (branchLen > 1).\n    const {\n      leaves: [{ name: firstName, type: firstType }]\n    } = firstTwig;\n    const {\n      leaves: [{ name: lastName, type: lastType }]\n    } = lastTwig;\n    const { combo: firstCombo } = firstTwig;\n    if (\n      this.#selector.includes(':scope') ||\n      lastType === PS_ELEMENT_SELECTOR ||\n      lastType === ID_SELECTOR\n    ) {\n      return { dir: DIR_PREV, twig: lastTwig };\n    }\n    if (firstType === ID_SELECTOR) {\n      return { dir: DIR_NEXT, twig: firstTwig };\n    }\n    if (firstName === '*' && firstType === TYPE_SELECTOR) {\n      return { dir: DIR_PREV, twig: lastTwig };\n    }\n    if (lastName === '*' && lastType === TYPE_SELECTOR) {\n      return { dir: DIR_NEXT, twig: firstTwig };\n    }\n    if (branchLen === 2) {\n      if (targetType === TARGET_FIRST) {\n        return { dir: DIR_PREV, twig: lastTwig };\n      }\n      const { name: comboName } = firstCombo;\n      if (comboName === '+' || comboName === '~') {\n        return { dir: DIR_PREV, twig: lastTwig };\n      }\n    }\n    // Default strategy for complex selectors.\n    return { dir: DIR_NEXT, twig: firstTwig };\n  };\n\n  /**\n   * Processes pending items not resolved with a direct strategy.\n   * @private\n   * @param {Set.<Map>} pendingItems - The set of pending items.\n   */\n  _processPendingItems = pendingItems => {\n    if (!pendingItems.size) {\n      return;\n    }\n    if (!this.#rootWalker) {\n      this.#rootWalker = this._createTreeWalker(this.#root);\n    }\n    const isScopedContext =\n      this.#node !== this.#root && this.#node.nodeType === ELEMENT_NODE;\n    const walker = this.#rootWalker;\n    let node = this.#root;\n    if (isScopedContext) {\n      node = this.#node;\n    }\n    let nextNode = traverseNode(node, walker);\n    while (nextNode) {\n      const isWithinScope =\n        this.#node.nodeType !== ELEMENT_NODE ||\n        nextNode === this.#node ||\n        this.#node.contains(nextNode);\n      if (isWithinScope) {\n        for (const pendingItem of pendingItems) {\n          const { leaves } = pendingItem.get('twig');\n          if (this._matchLeaves(leaves, nextNode, { warn: this.#warn })) {\n            const index = pendingItem.get('index');\n            this.#ast[index].filtered = true;\n            this.#ast[index].find = true;\n            this.#nodes[index].push(nextNode);\n          }\n        }\n      } else if (isScopedContext) {\n        break;\n      }\n      nextNode = walker.nextNode();\n    }\n  };\n\n  /**\n   * Collects nodes.\n   * @private\n   * @param {string} targetType - The target type.\n   * @returns {Array.<Array.<object>>} An array containing the AST and nodes.\n   */\n  _collectNodes = targetType => {\n    const ast = this.#ast.values();\n    if (targetType === TARGET_ALL || targetType === TARGET_FIRST) {\n      const pendingItems = new Set();\n      let i = 0;\n      for (const { branch } of ast) {\n        const complex = branch.length > 1;\n        const { dir, twig } = this._determineTraversalStrategy(\n          branch,\n          targetType\n        );\n        const { compound, filtered, nodes, pending } = this._findEntryNodes(\n          twig,\n          targetType,\n          { complex, dir }\n        );\n        if (nodes.length) {\n          this.#ast[i].find = true;\n          this.#nodes[i] = nodes;\n        } else if (pending) {\n          pendingItems.add(\n            new Map([\n              ['index', i],\n              ['twig', twig]\n            ])\n          );\n        }\n        this.#ast[i].dir = dir;\n        this.#ast[i].filtered = filtered || !compound;\n        i++;\n      }\n      this._processPendingItems(pendingItems);\n    } else {\n      let i = 0;\n      for (const { branch } of ast) {\n        const twig = branch[branch.length - 1];\n        const complex = branch.length > 1;\n        const dir = DIR_PREV;\n        const { compound, filtered, nodes } = this._findEntryNodes(\n          twig,\n          targetType,\n          { complex, dir }\n        );\n        if (nodes.length) {\n          this.#ast[i].find = true;\n          this.#nodes[i] = nodes;\n        }\n        this.#ast[i].dir = dir;\n        this.#ast[i].filtered = filtered || !compound;\n        i++;\n      }\n    }\n    return [this.#ast, this.#nodes];\n  };\n\n  /**\n   * Gets combined nodes.\n   * @private\n   * @param {object} twig - The twig object.\n   * @param {object} nodes - A collection of nodes.\n   * @param {string} dir - The direction.\n   * @returns {Array.<object>} A collection of matched nodes.\n   */\n  _getCombinedNodes = (twig, nodes, dir) => {\n    const arr = [];\n    const options = {\n      dir,\n      warn: this.#warn\n    };\n    for (const node of nodes) {\n      const matched = this._matchCombinator(twig, node, options);\n      if (matched.size) {\n        arr.push(...matched);\n      }\n    }\n    return arr;\n  };\n\n  /**\n   * Matches a node in the 'next' direction.\n   * @private\n   * @param {Array} branch - The branch.\n   * @param {Set.<object>} nodes - A collection of Element nodes.\n   * @param {object} opt - Options.\n   * @param {object} opt.combo - The combo object.\n   * @param {number} opt.index - The index.\n   * @returns {?object} The matched node.\n   */\n  _matchNodeNext = (branch, nodes, opt) => {\n    const { combo, index } = opt;\n    const { combo: nextCombo, leaves } = branch[index];\n    const twig = {\n      combo,\n      leaves\n    };\n    const nextNodes = new Set(this._getCombinedNodes(twig, nodes, DIR_NEXT));\n    if (nextNodes.size) {\n      if (index === branch.length - 1) {\n        const [nextNode] = sortNodes(nextNodes);\n        return nextNode;\n      }\n      return this._matchNodeNext(branch, nextNodes, {\n        combo: nextCombo,\n        index: index + 1\n      });\n    }\n    return null;\n  };\n\n  /**\n   * Matches a node in the 'previous' direction.\n   * @private\n   * @param {Array} branch - The branch.\n   * @param {object} node - The Element node.\n   * @param {object} opt - Options.\n   * @param {number} opt.index - The index.\n   * @returns {?object} The node.\n   */\n  _matchNodePrev = (branch, node, opt) => {\n    const { index } = opt;\n    const twig = branch[index];\n    const nodes = new Set([node]);\n    const nextNodes = new Set(this._getCombinedNodes(twig, nodes, DIR_PREV));\n    if (nextNodes.size) {\n      if (index === 0) {\n        return node;\n      }\n      let matched;\n      for (const nextNode of nextNodes) {\n        matched = this._matchNodePrev(branch, nextNode, {\n          index: index - 1\n        });\n        if (matched) {\n          break;\n        }\n      }\n      if (matched) {\n        return node;\n      }\n    }\n    return null;\n  };\n\n  /**\n   * Processes a complex selector branch to find all matching nodes.\n   * @private\n   * @param {Array} branch - The selector branch from the AST.\n   * @param {Array} entryNodes - The initial set of nodes to start from.\n   * @param {string} dir - The direction of traversal ('next' or 'prev').\n   * @returns {Set.<object>} A set of all matched nodes.\n   */\n  _processComplexBranchAll = (branch, entryNodes, dir) => {\n    const matchedNodes = new Set();\n    const branchLen = branch.length;\n    const lastIndex = branchLen - 1;\n\n    if (dir === DIR_NEXT) {\n      const { combo: firstCombo } = branch[0];\n      for (const node of entryNodes) {\n        let combo = firstCombo;\n        let nextNodes = new Set([node]);\n        for (let j = 1; j < branchLen; j++) {\n          const { combo: nextCombo, leaves } = branch[j];\n          const twig = { combo, leaves };\n          const nodesArr = this._getCombinedNodes(twig, nextNodes, dir);\n          if (nodesArr.length) {\n            if (j === lastIndex) {\n              for (const nextNode of nodesArr) {\n                matchedNodes.add(nextNode);\n              }\n            }\n            combo = nextCombo;\n            nextNodes = new Set(nodesArr);\n          } else {\n            // No further matches down this path.\n            nextNodes.clear();\n            break;\n          }\n        }\n      }\n      // DIR_PREV\n    } else {\n      for (const node of entryNodes) {\n        let nextNodes = new Set([node]);\n        for (let j = lastIndex - 1; j >= 0; j--) {\n          const twig = branch[j];\n          const nodesArr = this._getCombinedNodes(twig, nextNodes, dir);\n          if (nodesArr.length) {\n            // The entry node is the final match\n            if (j === 0) {\n              matchedNodes.add(node);\n            }\n            nextNodes = new Set(nodesArr);\n          } else {\n            // No further matches down this path.\n            nextNodes.clear();\n            break;\n          }\n        }\n      }\n    }\n    return matchedNodes;\n  };\n\n  /**\n   * Find a node contained by this.#node.\n   * @private\n   * @param {Array} nodesArr - The set of nodes to find from.\n   * @returns {?object} The matched node, or null.\n   */\n  _findChildNodeContainedByNode = nodesArr => {\n    let matchedNode = null;\n    if (Array.isArray(nodesArr)) {\n      const l = nodesArr.length;\n      for (let i = 0; i < l; i++) {\n        const node = nodesArr[i];\n        if (this.#node.contains(node)) {\n          matchedNode = node;\n          break;\n        }\n      }\n    }\n    return matchedNode;\n  };\n\n  /**\n   * Processes a complex selector branch to find the first matching node.\n   * @private\n   * @param {Array} branch - The selector branch from the AST.\n   * @param {Array} entryNodes - The initial set of nodes to start from.\n   * @param {string} dir - The direction of traversal ('next' or 'prev').\n   * @param {string} targetType - The type of search (e.g., 'first').\n   * @returns {?object} The first matched node, or null.\n   */\n  _processComplexBranchFirst = (branch, entryNodes, dir, targetType) => {\n    const branchLen = branch.length;\n    const lastIndex = branchLen - 1;\n    // DIR_NEXT logic for finding the first match.\n    if (dir === DIR_NEXT) {\n      const { combo: entryCombo } = branch[0];\n      for (const node of entryNodes) {\n        const matchedNode = this._matchNodeNext(branch, new Set([node]), {\n          combo: entryCombo,\n          index: 1\n        });\n        if (matchedNode) {\n          if (this.#node.nodeType === ELEMENT_NODE) {\n            if (\n              matchedNode !== this.#node &&\n              this.#node.contains(matchedNode)\n            ) {\n              return matchedNode;\n            }\n          } else {\n            return matchedNode;\n          }\n        }\n      }\n      // Fallback logic if no direct match found.\n      const { leaves: entryLeaves } = branch[0];\n      const [entryNode] = entryNodes;\n      if (this.#node.contains(entryNode)) {\n        let [refNode] = this._findNodeWalker(entryLeaves, entryNode, {\n          targetType\n        });\n        while (refNode) {\n          const matchedNode = this._matchNodeNext(branch, new Set([refNode]), {\n            combo: entryCombo,\n            index: 1\n          });\n          if (matchedNode) {\n            if (this.#node.nodeType === ELEMENT_NODE) {\n              if (\n                matchedNode !== this.#node &&\n                this.#node.contains(matchedNode)\n              ) {\n                return matchedNode;\n              }\n            } else {\n              return matchedNode;\n            }\n          }\n          [refNode] = this._findNodeWalker(entryLeaves, refNode, {\n            targetType,\n            force: true\n          });\n        }\n      } else {\n        const { combo: firstCombo } = branch[0];\n        let combo = firstCombo;\n        let nextNodes = new Set([entryNode]);\n        for (let j = 1; j < branchLen; j++) {\n          const { combo: nextCombo, leaves } = branch[j];\n          const twig = { combo, leaves };\n          const nodesArr = this._getCombinedNodes(twig, nextNodes, dir);\n          if (nodesArr.length) {\n            if (j === lastIndex) {\n              return this._findChildNodeContainedByNode(nodesArr);\n            }\n            combo = nextCombo;\n            nextNodes = new Set(nodesArr);\n          } else {\n            break;\n          }\n        }\n      }\n      // DIR_PREV logic for finding the first match.\n    } else {\n      for (const node of entryNodes) {\n        const matchedNode = this._matchNodePrev(branch, node, {\n          index: lastIndex - 1\n        });\n        if (matchedNode) {\n          return matchedNode;\n        }\n      }\n      // Fallback for TARGET_FIRST.\n      if (targetType === TARGET_FIRST) {\n        const { leaves: entryLeaves } = branch[lastIndex];\n        const [entryNode] = entryNodes;\n        let [refNode] = this._findNodeWalker(entryLeaves, entryNode, {\n          targetType\n        });\n        while (refNode) {\n          const matchedNode = this._matchNodePrev(branch, refNode, {\n            index: lastIndex - 1\n          });\n          if (matchedNode) {\n            return refNode;\n          }\n          [refNode] = this._findNodeWalker(entryLeaves, refNode, {\n            targetType,\n            force: true\n          });\n        }\n      }\n    }\n    return null;\n  };\n\n  /**\n   * Finds matched nodes.\n   * @param {string} targetType - The target type.\n   * @returns {Set.<object>} A collection of matched nodes.\n   */\n  find = targetType => {\n    const [[...branches], collectedNodes] = this._collectNodes(targetType);\n    const l = branches.length;\n    let sort = false;\n    let nodes = new Set();\n    for (let i = 0; i < l; i++) {\n      const { branch, dir, find } = branches[i];\n      if (!branch.length || !find) {\n        continue;\n      }\n      const entryNodes = collectedNodes[i];\n      const lastIndex = branch.length - 1;\n      // Handle simple selectors (no combinators).\n      if (lastIndex === 0) {\n        if (\n          (targetType === TARGET_ALL || targetType === TARGET_FIRST) &&\n          this.#node.nodeType === ELEMENT_NODE\n        ) {\n          for (const node of entryNodes) {\n            if (node !== this.#node && this.#node.contains(node)) {\n              nodes.add(node);\n              if (targetType === TARGET_FIRST) {\n                break;\n              }\n            }\n          }\n        } else if (targetType === TARGET_ALL) {\n          if (nodes.size) {\n            for (const node of entryNodes) {\n              nodes.add(node);\n            }\n            sort = true;\n          } else {\n            nodes = new Set(entryNodes);\n          }\n        } else {\n          if (entryNodes.length) {\n            nodes.add(entryNodes[0]);\n          }\n        }\n        // Handle complex selectors.\n      } else {\n        if (targetType === TARGET_ALL) {\n          const newNodes = this._processComplexBranchAll(\n            branch,\n            entryNodes,\n            dir\n          );\n          if (nodes.size) {\n            for (const newNode of newNodes) {\n              nodes.add(newNode);\n            }\n            sort = true;\n          } else {\n            nodes = newNodes;\n          }\n        } else {\n          const matchedNode = this._processComplexBranchFirst(\n            branch,\n            entryNodes,\n            dir,\n            targetType\n          );\n          if (matchedNode) {\n            nodes.add(matchedNode);\n          }\n        }\n      }\n    }\n    if (this.#check) {\n      const match = !!nodes.size;\n      let pseudoElement;\n      if (this.#pseudoElement.length) {\n        pseudoElement = this.#pseudoElement.join('');\n      } else {\n        pseudoElement = null;\n      }\n      return { match, pseudoElement };\n    }\n    if (targetType === TARGET_FIRST || targetType === TARGET_ALL) {\n      nodes.delete(this.#node);\n    }\n    if ((sort || targetType === TARGET_FIRST) && nodes.size > 1) {\n      return new Set(sortNodes(nodes));\n    }\n    return nodes;\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,uBAAyB;;;ACHzB,IAAAA,WAAyB;;;ACAzB,oBAAmB;AACnB,qBAAwB;AACxB,cAAyB;AACzB,8CAAgC;;;ACFzB,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,aAAa;AACnB,IAAM,QAAQ;AACd,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,MAAM;AAEZ,IAAM,oBAAoB;AAC1B,IAAM,sBAAsB;AAG5B,IAAM,WAAW;AAEjB,IAAM,SAAS;AACf,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAM,cAAc;AACpB,IAAM,gBAAgB;AAGtB,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,WAAW;AACjB,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,YAAY;AAClB,IAAM,UAAU;AAGhB,IAAM,eAAe;AACrB,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,yBAAyB;AAC/B,IAAM,8BAA8B;AACpC,IAAM,6BAA6B;AAInC,IAAM,WAAW;AACjB,IAAM,iBAAiB;AAMvB,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,QAAQ;AACd,IAAM,YAAY,OAAO,SAAS;AAClC,IAAM,eAAe,iBAAiB,SAAS;AAC/C,IAAM,MAAM,WAAW,KAAK,iBAAiB,KAAK,kBAAkB,KAAK;AAEzE,IAAM,OAAO,qDAAqD,GAAG;AAErE,IAAM,WAAW;AACjB,IAAM,qBAAqB;AAC3B,IAAM,YAAY;AAElB,IAAM,WAAW;AACjB,IAAM,aAAa;AACnB,IAAM,WAAW,MAAM,QAAQ,OAAO,QAAQ,QAAQ,QAAQ;AAC9D,IAAM,qBAAqB,MAAM,QAAQ,OAAO,QAAQ,QAAQ,kBAAkB;AAClF,IAAM,QAAQ;AACd,IAAM,UAAU,GAAG,QAAQ,MAAM,KAAK,GAAG,QAAQ;AACjD,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,iBAAiB,aAAa,QAAQ,eAAe,QAAQ;AACnE,IAAM,iBAAiB,aAAa,OAAO,eAAe,OAAO;AACjE,IAAM,aAAa,MAAM,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,IAAI,cAAc;AAClF,IAAM,aAAa,MAAM,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,IAAI,cAAc;AAClF,IAAM,aAAa,MAAM,UAAU,OAAO,UAAU,QAAQ,QAAQ;AACpE,IAAM,YAAY,GAAG,UAAU,MAAM,KAAK,GAAG,UAAU;AACvD,IAAM,gBAAgB,oBAAoB,SAAS,eAAe,SAAS;AAC3E,IAAM,iBAAiB,oBAAoB,UAAU,eAAe,UAAU;AAC9E,IAAM,eAAe,oBAAoB,kBAAkB;AAG3D,IAAM,aAAa,OAAO,OAAO;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACM,IAAM,eAAe,OAAO,OAAO,CAAC,UAAU,SAAS,QAAQ,CAAC;AAChE,IAAM,cAAc,OAAO,OAAO,CAAC,YAAY,OAAO,CAAC;AACvD,IAAM,aAAa,OAAO,OAAO;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACM,IAAM,aAAa,OAAO,OAAO;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACM,IAAM,aAAa,OAAO,OAAO;AAAA,EACtC,GAAG;AAAA,EACH,GAAG;AAAA,EACH;AACF,CAAC;AACM,IAAM,YAAY,OAAO,OAAO;AAAA,EACrC,GAAG;AAAA,EACH;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAGM,IAAM,iBAAiB,IAAI,IAAI,UAAU;AACzC,IAAM,cAAc,oBAAI,IAAI,CAAC,OAAO,MAAM,OAAO,OAAO,CAAC;AACzD,IAAM,eAAe,oBAAI,IAAI;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACM,IAAM,iBAAiB,oBAAI,IAAI;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACM,IAAM,sBAAsB,oBAAI,IAAI,CAAC,QAAQ,SAAS,CAAC;AACvD,IAAM,qBAAqB,oBAAI,IAAI;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACM,IAAM,kBAAkB,oBAAI,IAAI,CAAC,QAAQ,cAAc,CAAC;;;ADtI/D,IAAM,eAAe,oBAAI,IAAI,CAAC,GAAG,cAAc,GAAG,YAAY,QAAQ,CAAC;AACvE,IAAM,cAAc,IAAI,IAAI,SAAS;AACrC,IAAMC,kBAAiB,IAAI,IAAI,UAAU;AACzC,IAAM,uBAAuB,oBAAI,IAAI,CAAC,OAAO,UAAU,SAAS,UAAU,CAAC;AAC3E,IAAM,qBAAqB,oBAAI,IAAI,CAAC,UAAU,UAAU,UAAU,CAAC;AACnE,IAAM,yBAAyB,oBAAI,IAAI;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAM,oBACJ;AACF,IAAM,mBAAmB,IAAI,OAAO,IAAI,SAAS,GAAG;AACpD,IAAM,cAAc,IAAI,OAAO,GAAG,UAAU,GAAG,KAAK,GAAG,UAAU,IAAI,GAAG;AACxE,IAAM,cAAc,IAAI,OAAO,GAAG,UAAU,GAAG,OAAO,GAAG,UAAU,IAAI,GAAG;AAC1E,IAAM,cAAc,IAAI,OAAO,GAAG,UAAU,GAAG,OAAO,GAAG,UAAU,IAAI,GAAG;AAC1E,IAAM,oBAAoB,IAAI;AAAA,EAC5B,OAAO,YAAY,IAAI,IAAI,IAAI,aAAa;AAC9C;AACA,IAAM,qBAAqB,IAAI;AAAA,EAC7B,OAAO,YAAY,IAAI,IAAI,IAAI,cAAc;AAC/C;AACA,IAAM,yBAAyB,IAAI;AAAA,EACjC,OAAO,YAAY,IAAI,IAAI,IAAI,cAAc,IAAI,YAAY;AAC/D;AACA,IAAM,mBAAmB,IAAI,OAAO,IAAI,YAAY,GAAG;AACvD,IAAM,iBAAiB,IAAI,OAAO,OAAO,YAAY,IAAI,IAAI,GAAG;AAkFzD,IAAM,UAAU,OACrB,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,WAAW,OAAO;AA+BrD,IAAM,oBAAoB,CAAC,KAAK,MAAM,eAAe,eAAe;AACzE,SAAO,IAAI,aAAa,aAAa,KAAK,IAAI;AAChD;AAOO,IAAM,gBAAgB,UAAQ;AACnC,SAAO,KAAK,SAAS;AACvB;AAOO,IAAM,2BAA2B,UAAQ;AAC9C,MAAI,YAAY,IAAI,KAAK,IAAI,KAAa,aAAK,MAAM,aAAa,GAAG;AACnE,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAWO,IAAM,mBAAmB,CAAC,OAAO,QAAQ;AAC9C,QAAM,EAAE,GAAG,GAAG,QAAQ,IAAI;AAC1B,QAAM,iBAAiB,UAAU,CAAC,GAAG,KAAK,EAAE,QAAQ,IAAI;AACxD,QAAM,IAAI,MAAM;AAChB,QAAM,UAAU,CAAC;AACjB,MAAI,MAAM,GAAG;AACX,QAAI,IAAI,KAAK,KAAK,GAAG;AACnB,cAAQ,KAAK,eAAe,IAAI,CAAC,CAAC;AAAA,IACpC;AACA,WAAO;AAAA,EACT;AACA,MAAI,aAAa,IAAI;AACrB,MAAI,IAAI,GAAG;AACT,WAAO,aAAa,GAAG;AACrB,oBAAc;AAAA,IAChB;AACA,aAAS,IAAI,YAAY,IAAI,GAAG,KAAK,GAAG;AACtC,cAAQ,KAAK,eAAe,CAAC,CAAC;AAAA,IAChC;AAAA,EACF,WAAW,cAAc,GAAG;AAC1B,aAAS,IAAI,YAAY,KAAK,GAAG,KAAK,GAAG;AACvC,cAAQ,KAAK,eAAe,CAAC,CAAC;AAAA,IAChC;AACA,WAAO,QAAQ,QAAQ;AAAA,EACzB;AACA,SAAO;AACT;AAOO,IAAM,iBAAiB,UAAQ;AACpC,MAAI,CAAC,MAAM,UAAU;AACnB,UAAM,IAAI,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AAAA,EACxD;AACA,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,UAAQ,KAAK,UAAU;AAAA,IACrB,KAAK,eAAe;AAClB,iBAAW;AACX,aAAO;AACP;AAAA,IACF;AAAA,IACA,KAAK,wBAAwB;AAC3B,YAAM,EAAE,MAAM,MAAM,cAAc,IAAI;AACtC,iBAAW;AACX,aAAO;AACP,eAAS,SAAS,SAAS,WAAW,SAAS;AAC/C;AAAA,IACF;AAAA,IACA,KAAK,cAAc;AACjB,iBAAW,KAAK;AAChB,UAAI,UAAU;AACd,aAAO,SAAS;AACd,cAAM,EAAE,MAAM,MAAM,UAAU,WAAW,IAAI;AAC7C,YAAI,aAAa,wBAAwB;AACvC,mBAAS,SAAS,SAAS,WAAW,SAAS;AAC/C;AAAA,QACF,WAAW,YAAY;AACrB,oBAAU;AAAA,QACZ,OAAO;AACL;AAAA,QACF;AAAA,MACF;AACA,aAAO;AACP;AAAA,IACF;AAAA,IACA,SAAS;AACP,YAAM,IAAI,UAAU,mBAAmB,KAAK,QAAQ,EAAE;AAAA,IACxD;AAAA,EACF;AACA,SAAO,CAAC,UAAU,MAAM,CAAC,CAAC,MAAM;AAClC;AASO,IAAM,eAAe,CAAC,MAAM,QAAQ,QAAQ,UAAU;AAC3D,MAAI,CAAC,MAAM,UAAU;AACnB,UAAM,IAAI,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AAAA,EACxD;AACA,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,MAAI,UAAU,OAAO;AACrB,MAAI,YAAY,MAAM;AACpB,WAAO;AAAA,EACT,WAAW,SAAS,QAAQ,SAAS,IAAI,GAAG;AAC1C,cAAU,OAAO,SAAS;AAC1B,WAAO,SAAS;AACd,UAAI,YAAY,MAAM;AACpB;AAAA,MACF;AACA,gBAAU,OAAO,SAAS;AAAA,IAC5B;AACA,WAAO;AAAA,EACT,OAAO;AACL,QAAI,YAAY,OAAO,MAAM;AAC3B,UAAI;AACJ,aAAO,SAAS;AACd,YAAI,YAAY,MAAM;AACpB,iBAAO;AACP;AAAA,QACF,WAAW,YAAY,OAAO,QAAQ,QAAQ,SAAS,IAAI,GAAG;AAC5D;AAAA,QACF;AACA,kBAAU,OAAO,WAAW;AAAA,MAC9B;AACA,UAAI,MAAM;AACR,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,KAAK,aAAa,cAAc;AAClC,UAAI;AACJ,aAAO,SAAS;AACd,YAAI,YAAY,MAAM;AACpB,iBAAO;AACP;AAAA,QACF;AACA,kBAAU,OAAO,SAAS;AAAA,MAC5B;AACA,UAAI,MAAM;AACR,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAQO,IAAM,kBAAkB,CAAC,MAAM,MAAM,CAAC,MAAM;AACjD,MAAI,CAAC,MAAM,UAAU;AACnB,UAAM,IAAI,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AAAA,EACxD;AACA,MAAI,KAAK,aAAa,cAAc;AAClC,WAAO;AAAA,EACT;AACA,QAAM,EAAE,WAAW,cAAc,IAAI;AACrC,QAAM,EAAE,eAAe,IAAI;AAC3B,QAAM,SAAS,cAAc;AAC7B,MAAI;AACJ,QAAM,OAAO,KAAK,aAAa,IAAI;AACnC,MAAI,MAAM;AACR,yBACE,wCAAAC,SAAoB,IAAI,KAAK,OAAO,eAAe,IAAI,IAAI;AAAA,EAC/D,OAAO;AACL,yBACE,wCAAAA,SAAoB,SAAS,KAAK,OAAO,eAAe,IAAI,SAAS;AAAA,EACzE;AACA,MAAI,gBAAgB;AAClB,QAAI,gBAAgB;AAClB,aAAO,CAAC,CAAC,eAAe;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAOO,IAAM,wBAAwB,UAAQ;AAC3C,MAAI,CAAC,MAAM,UAAU;AACnB,UAAM,IAAI,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AAAA,EACxD;AACA,MAAI,OAAO,KAAK,kBAAkB,YAAY;AAC5C,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,KAAK,cAAc;AACjC,MAAI,MAAM,QAAQ;AAChB,QAAI,OAAO;AACX,UAAM,IAAI,MAAM;AAChB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,OAAO,MAAM,CAAC;AACpB,aAAO,KAAK,YAAY,KAAK;AAC7B,UAAI,MAAM;AACR;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,KAAK,YAAY,KAAK;AAC/B;AAQO,IAAM,oBAAoB,UAAQ;AACvC,MAAI,CAAC,MAAM,UAAU;AACnB,UAAM,IAAI,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AAAA,EACxD;AACA,MAAI,KAAK,aAAa,cAAc;AAClC,WAAO;AAAA,EACT;AACA,QAAM,EAAE,KAAK,SAAS,WAAW,WAAW,IAAI;AAChD,QAAM,EAAE,mBAAmB,QAAI,eAAAC,SAAY;AAC3C,MAAI,YAAY,SAAS,YAAY,OAAO;AAC1C,WAAO;AAAA,EACT,WAAW,YAAY,QAAQ;AAC7B,QAAI,OAAO;AACX,YAAQ,WAAW;AAAA,MACjB,KAAK,SAAS;AACZ,YAAI,CAAC,KAAK,QAAQ,aAAa,IAAI,KAAK,IAAI,GAAG;AAC7C,iBAAO,KAAK;AAAA,QACd,WAAW,YAAY,IAAI,KAAK,IAAI,GAAG;AACrC,iBAAO;AAAA,QACT;AACA;AAAA,MACF;AAAA,MACA,KAAK,QAAQ;AACX,eAAO,sBAAsB,IAAI;AACjC;AAAA,MACF;AAAA,MACA,KAAK,YAAY;AACf,eAAO,KAAK;AACZ;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM,QAAQ,CAAC,EAAE,MAAM,KAAK,KAAK,UAAU;AAC3C,mBAAW,QAAQ,OAAO;AACxB,gBAAM;AAAA,YACJ,KAAK;AAAA,YACL,WAAW;AAAA,YACX,UAAU;AAAA,YACV,aAAa;AAAA,UACf,IAAI;AACJ,cAAI,iBAAiB,WAAW;AAC9B,mBAAO,gBAAgB,KAAK;AAAA,UAC9B,WACE,iBAAiB,gBACjB,CAAC,qBAAqB,IAAI,aAAa,MACtC,CAAC,WAAY,YAAY,SAAS,YAAY,QAC/C;AACA,gBAAI,kBAAkB,QAAQ;AAC5B,qBAAO,sBAAsB,IAAI;AAAA,YACnC,OAAO;AACL,qBAAO,gBAAgB,KAAK;AAAA,YAC9B;AAAA,UACF;AACA,cAAI,MAAM;AACR;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM;AACR,YAAM;AAAA,QACJ,YAAY,CAAC,EAAE,MAAM,CAAC;AAAA,MACxB,IAAI,mBAAmB,IAAI;AAC3B,UAAI,QAAQ,MAAM,GAAG;AACnB,eAAO;AAAA,MACT;AAAA,IACF,WAAW,YAAY;AACrB,YAAM,EAAE,UAAU,eAAe,IAAI;AACrC,UAAI,mBAAmB,cAAc;AACnC,eAAO,kBAAkB,UAAU;AAAA,MACrC;AAAA,IACF;AAAA,EACF,WAAW,cAAc,WAAW,KAAK,SAAS,OAAO;AACvD,WAAO;AAAA,EACT,WAAW,cAAc,OAAO;AAC9B,UAAM,OAAO,KAAK,YAAY,KAAK;AACnC,QAAI,MAAM;AACR,YAAM;AAAA,QACJ,YAAY,CAAC,EAAE,MAAM,CAAC;AAAA,MACxB,IAAI,mBAAmB,IAAI;AAC3B,UAAI,QAAQ,MAAM,GAAG;AACnB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,WAAW,YAAY;AACrB,QAAI,cAAc,QAAQ;AACxB,YAAM,OAAO,sBAAsB,IAAI;AACvC,UAAI,MAAM;AACR,cAAM;AAAA,UACJ,YAAY,CAAC,EAAE,MAAM,CAAC;AAAA,QACxB,IAAI,mBAAmB,IAAI;AAC3B,YAAI,QAAQ,MAAM,GAAG;AACnB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,UAAM,EAAE,UAAU,eAAe,IAAI;AACrC,QAAI,mBAAmB,cAAc;AACnC,aAAO,kBAAkB,UAAU;AAAA,IACrC;AAAA,EACF;AACA,SAAO;AACT;AAOO,IAAM,oBAAoB,UAAQ;AACvC,MAAI,CAAC,MAAM,UAAU;AACnB,UAAM,IAAI,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AAAA,EACxD;AACA,MAAI,KAAK,aAAa,cAAc;AAClC,WAAO;AAAA,EACT;AACA,QAAM,EAAE,YAAY,IAAI,KAAK;AAC7B,QAAM,SAAS,yCAAyC,KAAK,WAAW;AACxE,QAAM,QACJ,iEAAiE;AAAA,IAC/D;AAAA,EACF;AACF,MAAI,UAAU;AACd,SAAO,SAAS;AACd,QAAI,OAAO;AAEX,QAAI,UAAU,QAAQ,aAAa,MAAM,GAAG;AAC1C,aAAO,QAAQ,aAAa,MAAM;AAAA,IACpC,WAAW,SAAS,QAAQ,aAAa,UAAU,GAAG;AACpD,aAAO,QAAQ,aAAa,UAAU;AAAA,IACxC;AAGA,QAAI,SAAS,MAAM;AACjB,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,eAAe;AACzB,gBAAU,QAAQ;AAAA,IACpB,OAAO;AACL;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAQO,IAAM,qBAAqB,CAAC,UAAU,gBAAgB;AAC3D,MAAI,gBAAgB,aAAa;AAC/B,WAAO,OAAO,aAAa,YAAY,OAAO,KAAK,QAAQ;AAAA,EAC7D;AACA,SAAO,EAAE,OAAO,aAAa,YAAY,OAAO,KAAK,QAAQ;AAC/D;AAQO,IAAM,oBAAoB,UAAQ;AACvC,MAAI,CAAC,MAAM,UAAU;AACnB,UAAM,IAAI,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AAAA,EACxD;AACA,MAAI,KAAK,aAAa,cAAc;AAClC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,KAAK,sBAAsB,WAAW;AAC/C,WAAO,KAAK;AAAA,EACd,WAAW,KAAK,cAAc,eAAe,MAAM;AACjD,WAAO;AAAA,EACT,OAAO;AACL,QAAI;AACJ,QAAI,KAAK,aAAa,iBAAiB,GAAG;AACxC,aAAO,KAAK,aAAa,iBAAiB;AAAA,IAC5C,OAAO;AACL,aAAO;AAAA,IACT;AACA,YAAQ,MAAM;AAAA,MACZ,KAAK;AAAA,MACL,KAAK,QAAQ;AACX,eAAO;AAAA,MACT;AAAA,MACA,KAAK,kBAAkB;AAIrB,eAAO;AAAA,MACT;AAAA,MACA,KAAK,SAAS;AACZ,eAAO;AAAA,MACT;AAAA,MACA,SAAS;AACP,YAAI,MAAM,YAAY,aAAa,cAAc;AAC/C,iBAAO,kBAAkB,KAAK,UAAU;AAAA,QAC1C;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AAOO,IAAM,YAAY,UAAQ;AAC/B,MAAI,MAAM,aAAa,cAAc;AACnC,WAAO;AAAA,EACT;AACA,QAAM,SAAS,KAAK,cAAc;AAClC,QAAM,EAAE,SAAS,WAAW,IAAI,OAAO,iBAAiB,IAAI;AAC5D,MAAI,YAAY,UAAU,eAAe,WAAW;AAClD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAOO,IAAM,iBAAiB,UAAQ;AACpC,MAAI,MAAM,aAAa,cAAc;AACnC,WAAO;AAAA,EACT;AACA,QAAM,EAAE,WAAW,KAAK,IAAI;AAC5B,UAAQ,WAAW;AAAA,IACjB,KAAK,SAAS;AACZ,UAAI,CAAC,QAAQC,gBAAe,IAAI,IAAI,GAAG;AACrC,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA,IACA,KAAK,YAAY;AACf,aAAO;AAAA,IACT;AAAA,IACA,SAAS;AACP,aAAO,kBAAkB,IAAI;AAAA,IAC/B;AAAA,EACF;AACF;AAOO,IAAM,kBAAkB,UAAQ;AACrC,MAAI,MAAM,aAAa,cAAc;AACnC,WAAO;AAAA,EACT;AACA,MAAI,CAAC,KAAK,aAAa;AACrB,WAAO;AAAA,EACT;AACA,QAAM,SAAS,KAAK,cAAc;AAClC,MAAI,gBAAgB,OAAO,aAAa;AACtC,QAAI,OAAO,UAAU,SAAS,KAAK,aAAa,UAAU,CAAC,CAAC,GAAG;AAC7D,aAAO;AAAA,IACT;AACA,QAAI,kBAAkB,IAAI,GAAG;AAC3B,aAAO;AAAA,IACT;AACA,UAAM,EAAE,WAAW,WAAW,IAAI;AAClC,YAAQ,WAAW;AAAA,MACjB,KAAK,KAAK;AACR,YAAI,KAAK,QAAQ,KAAK,aAAa,MAAM,GAAG;AAC1C,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MACA,KAAK,UAAU;AACb,eAAO;AAAA,MACT;AAAA,MACA,KAAK,SAAS;AACZ,YACE,KAAK,YACL,KAAK,aAAa,UAAU,KAC5B,KAAK,UACL,KAAK,aAAa,QAAQ,GAC1B;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MACA,KAAK,WAAW;AACd,YAAI,WAAW,cAAc,WAAW;AACtC,cAAI,QAAQ,WAAW;AACvB,cAAI,OAAO;AACX,iBAAO,OAAO;AACZ,gBAAI,MAAM,cAAc,WAAW;AACjC,qBAAO,UAAU;AACjB;AAAA,YACF;AACA,oBAAQ,MAAM;AAAA,UAChB;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MACA,SAAS;AACP,YACE,mBAAmB,IAAI,SAAS,KAChC,EAAE,KAAK,YAAY,KAAK,aAAa,UAAU,IAC/C;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF,WAAW,gBAAgB,OAAO,YAAY;AAC5C,QAAI,OAAO,UAAU,SAAS,KAAK,eAAe,MAAM,UAAU,CAAC,CAAC,GAAG;AACrE,YAAM,KAAK;AACX,UAAI;AACJ,UAAI,UAAU;AACd,aAAO,QAAQ,iBAAiB,IAAI;AAClC,eAAO,uBAAuB,IAAI,QAAQ,SAAS;AACnD,YAAI,MAAM;AACR;AAAA,QACF;AACA,YAAI,SAAS,YAAY,iBAAiB,IAAI;AAC5C,oBAAU,QAAQ;AAAA,QACpB,OAAO;AACL;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAM;AACR,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,QACE,KAAK,cAAc,QAClB,KAAK,QAAQ,KAAK,eAAe,MAAM,MAAM,IAC9C;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAqDO,IAAM,kBAAkB,CAAC,IAAI,SAAS;AAC3C,MAAI,OAAO,OAAO,UAAU;AAC1B,UAAM,IAAI,UAAU,mBAAmB,QAAQ,EAAE,CAAC,EAAE;AAAA,EACtD,WAAW,CAAC,MAAM,UAAU;AAC1B,UAAM,IAAI,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AAAA,EACxD;AACA,MAAI,CAAC,MAAM,KAAK,aAAa,cAAc;AACzC,WAAO;AAAA,EACT;AACA,QAAM,EAAE,WAAW,IAAI;AACvB,MAAI;AACJ,aAAW,QAAQ,YAAY;AAC7B,UAAM,EAAE,MAAM,cAAc,QAAQ,MAAM,IAAI;AAC9C,QAAI,SAAS,SAAS,EAAE,IAAI;AAC1B,YAAM;AAAA,IACR,WAAW,WAAW,IAAI;AACxB,YAAM;AAAA,IACR;AACA,QAAI,KAAK;AACP;AAAA,IACF;AAAA,EACF;AACA,SAAO,OAAO;AAChB;AAQO,IAAM,sBAAsB,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM;AACzD,MAAI,CAAC,MAAM,OAAO,OAAO,YAAY,MAAM,aAAa,cAAc;AACpE,WAAO;AAAA,EACT;AACA,MAAI,KAAK,mBAAmB,EAAE,GAAG;AAC/B,WAAO;AAAA,EACT;AACA,QAAM,OAAO,KAAK,cAAc;AAChC,MAAI,SAAS;AACb,MAAI;AACJ,SAAO,QAAQ;AACb,UAAM,gBAAgB,IAAI,MAAM;AAChC,QAAI,OAAO,WAAW,MAAM;AAC1B;AAAA,IACF;AACA,aAAS,OAAO;AAAA,EAClB;AACA,SAAO,CAAC,CAAC;AACX;AAQO,IAAM,cAAc,CAAC,OAAO,UAAU;AAC3C,MAAI,CAAC,OAAO,UAAU;AACpB,UAAM,IAAI,UAAU,mBAAmB,QAAQ,KAAK,CAAC,EAAE;AAAA,EACzD,WAAW,CAAC,OAAO,UAAU;AAC3B,UAAM,IAAI,UAAU,mBAAmB,QAAQ,KAAK,CAAC,EAAE;AAAA,EACzD;AACA,MAAI,MAAM,aAAa,gBAAgB,MAAM,aAAa,cAAc;AACtE,WAAO;AAAA,EACT;AACA,QAAM,SAAS,MAAM,wBAAwB,KAAK;AAClD,QAAM,MACJ,SAAS,+BAA+B,SAAS;AACnD,SAAO,CAAC,CAAC;AACX;AAQO,IAAM,eAAe,CAAC,GAAG,MAAM;AACpC,MAAI,YAAY,GAAG,CAAC,GAAG;AACrB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAOO,IAAM,YAAY,CAAC,QAAQ,CAAC,MAAM;AACvC,QAAM,MAAM,CAAC,GAAG,KAAK;AACrB,MAAI,IAAI,SAAS,GAAG;AAClB,QAAI,KAAK,YAAY;AAAA,EACvB;AACA,SAAO;AACT;AAmGO,IAAM,sBAAsB,CAAC,MAAM,MAAM,CAAC,MAAM;AACrD,MAAI,CAAC,MAAM,UAAU;AACnB,UAAM,IAAI,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AAAA,EACxD;AACA,QAAM,EAAE,WAAW,IAAI;AACvB,QAAM;AAAA,IACJ,cAAc;AAAA,IACd,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,gBAAgB;AAAA,EAClB,IAAI;AACJ,MAAI,CAAC,WAAW,UAAU,CAAC,aAAa;AACtC,WAAO,CAAC;AAAA,EACV;AACA,QAAM,aAAa,oBAAI,IAAI;AAC3B,QAAM,eAAe,YAAY,SAAS,GAAG;AAC7C,aAAW,QAAQ,YAAY;AAC7B,QAAI,EAAE,MAAM,UAAU,OAAO,UAAU,IAAI;AAC3C,QAAI,CAAC,eAAe;AAClB,iBAAW,SAAS,YAAY;AAChC,kBAAY,UAAU,YAAY;AAAA,IACpC;AACA,QAAI,aAAa,YAAY;AAC3B;AAAA,IACF;AACA,UAAM,CAAC,YAAY,aAAa,IAAI,SAAS,SAAS,GAAG,IACrD,SAAS,MAAM,GAAG,IAClB,CAAC,IAAI,QAAQ;AACjB,QAAI,cAAc;AAChB,UAAI,iBAAiB,eAAe;AAClC,YAAI,cAAc,OAAQ,cAAc,MAAM,eAAe,IAAK;AAChE,qBAAW,IAAI,SAAS;AAAA,QAC1B,WACE,cAAc,cACd,oBAAoB,WAAW,IAAI,GACnC;AACA,qBAAW,IAAI,SAAS;AAAA,QAC1B;AAAA,MACF;AAAA,IACF,WAAW,gBAAgB,eAAe;AACxC,iBAAW,IAAI,SAAS;AAAA,IAC1B;AAAA,EACF;AACA,SAAO,CAAC,GAAG,UAAU;AACvB;AAQO,IAAM,4BAA4B,CAAC,UAAU,IAAI,WAAW,CAAC,MAAM;AACxE,MAAI,CAAC,qBAAqB,KAAK,OAAO,GAAG;AACvC,WAAO;AAAA,EACT;AACA,QAAM,CAAC,MAAM,IAAI;AACjB,MAAI,MAAM,QAAQ,MAAM,KAAK,OAAO,WAAW,GAAG;AAChD,UAAM,CAAC,GAAG,IAAI;AACd,QAAI,CAAC,OAAO,CAAC,OAAO,OAAO,KAAK,MAAM,GAAG;AACvC,aAAO;AAAA,IACT;AACA,QAAI,YAAY,OAAO;AACrB,YAAM,EAAE,KAAK,IAAI;AACjB,aAAO,SAAS;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAQO,IAAM,aAAa,CAAC,QAAQ,aAAa;AAC9C,MAAI,CAAC,QAAQ,cAAc;AACzB,UAAM,IAAI,UAAU,4BAA4B,QAAQ,MAAM,CAAC,EAAE;AAAA,EACnE;AACA,MAAI,UAAU,aAAa,eAAe;AACxC,eAAW,OAAO;AAAA,EACpB;AACA,QAAM,SAAK,cAAAC,SAAO;AAAA,IAChB;AAAA,IACA,cAAc,OAAO;AAAA,EACvB,CAAC;AACD,KAAG,UAAU;AAAA,IACX,WAAW;AAAA,EACb,CAAC;AACD,SAAO;AACT;AAQO,IAAM,iBAAiB,CAAC,UAAU,WAAW;AAClD,MACE,CAAC,YACD,OAAO,aAAa,YACpB,iBAAiB,KAAK,QAAQ,KAC9B,WAAW,cACX;AACA,WAAO;AAAA,EACT;AAEA,MAAI,WAAW,cAAc,iBAAiB,KAAK,QAAQ,GAAG;AAC5D,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,SAAS,GAAG,GAAG;AAC1B,UAAM,QAAQ,SAAS,YAAY,GAAG;AACtC,UAAM,MAAM,SAAS,UAAU,KAAK;AACpC,QAAI,IAAI,QAAQ,GAAG,IAAI,GAAG;AACxB,aAAO;AAAA,IACT;AAAA,EACF;AAWA,MAAI,SAAS,SAAS,GAAG,KAAK,kBAAkB,KAAK,QAAQ,GAAG;AAC9D,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,SAAS,GAAG,GAAG;AAC1B,QAAI,UAAU;AACd,QAAI,WAAW,YAAY;AACzB,gBAAU,YAAY,KAAK,QAAQ;AAAA,IACrC;AACA,QACE,WAAW,cACX,YAAY,KAAK,QAAQ,KACzB,CAAC,YAAY,KAAK,QAAQ,GAC1B;AACA,aAAO;AAAA,IACT,WAAW,WAAW,cAAc,SAAS,KAAK,QAAQ,GAAG;AAC3D,UAAI,CAAC,WAAW,uBAAuB,KAAK,QAAQ,GAAG;AACrD,eAAO;AAAA,MACT;AACA,aAAO,iBAAiB,KAAK,QAAQ;AAAA,IACvC,WAAW,gBAAgB,KAAK,QAAQ,GAAG;AACzC,UAAI,SAAS;AACX,eAAO,CAAC,kBAAkB,KAAK,QAAQ;AAAA,MACzC,OAAO;AACL,eAAO,CAAC,mBAAmB,KAAK,QAAQ;AAAA,MAC1C;AAAA,IACF,OAAO;AACL,aAAO,CAAC,eAAe,KAAK,QAAQ;AAAA,IACtC;AAAA,EACF;AACA,SAAO;AACT;;;ADxwBA,sBAAyD;AAzXzD,IAAM,iBAAiB,oBAAI,IAAI;AAAA,EAC7B,CAAC,qBAAqB,MAAM;AAAA,EAC5B,CAAC,aAAa,MAAM;AAAA,EACpB,CAAC,gBAAgB,MAAM;AAAA,EACvB,CAAC,eAAe,MAAM;AAAA,EACtB,CAAC,eAAe,MAAM;AAAA,EACtB,CAAC,mBAAmB,MAAM;AAC5B,CAAC;AACD,IAAM,oBACJ;AACF,IAAM,wBAAwB;AAC9B,IAAM,SAAS;AAOR,IAAM,mBAAmB,CAAC,WAAW,OAAO;AACjD,MAAI,OAAO,aAAa,YAAY,SAAS,QAAQ,MAAM,CAAC,KAAK,GAAG;AAClE,UAAM,MAAM,SAAS,MAAM,IAAI;AAC/B,UAAM,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAC7B,UAAM,IAAI,IAAI;AACd,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,OAAO,IAAI,CAAC;AAClB,UAAI,SAAS,MAAM,MAAM,IAAI,GAAG;AAC9B,sBAAc,KAAK,MAAM;AAAA,MAC3B,OAAO;AACL,cAAM,YAAY,sBAAsB,KAAK,IAAI;AACjD,YAAI,WAAW;AACb,gBAAM,CAAC,EAAE,GAAG,IAAI;AAChB,cAAI;AACJ,cAAI;AACF,kBAAM,MAAM,SAAS,QAAQ,GAAG;AAChC,kBAAM,OAAO,SAAS,QAAQ,GAAG;AACjC,kBAAM,OAAO,SAAS,KAAK,GAAG;AAC9B,gBAAI,SAAS,KAAM,QAAQ,OAAO,QAAQ,MAAO;AAC/C,oBAAM;AAAA,YACR,OAAO;AACL,oBAAM,OAAO,cAAc,IAAI;AAAA,YACjC;AAAA,UACF,SAAS,GAAG;AACV,kBAAM;AAAA,UACR;AACA,cAAI,UAAU;AACd,cAAI,KAAK,SAAS,IAAI,QAAQ;AAC5B,sBAAU,KAAK,UAAU,IAAI,MAAM;AAAA,UACrC;AACA,wBAAc,KAAK,GAAG,GAAG,GAAG,OAAO,EAAE;AAAA,QAEvC,WAAW,YAAY,KAAK,IAAI,GAAG;AACjC,wBAAc,KAAK,KAAK,IAAI,EAAE;AAAA,QAChC,OAAO;AACL,wBAAc,KAAK,IAAI;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,WAAO,cAAc,KAAK,EAAE;AAAA,EAC9B;AACA,SAAO;AACT;AAQO,IAAM,aAAa,WAAS;AAEjC,MAAI,OAAO,UAAU,UAAU;AAC7B,QAAI,UAAU,UAAa,UAAU,MAAM;AACzC,aAAO,QAAQ,KAAK,EAAE,YAAY;AAAA,IACpC,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,aAAO,MAAM,KAAK,GAAG;AAAA,IACvB,WAAW,OAAO,OAAO,OAAO,UAAU,GAAG;AAC3C,aAAO,MAAM,SAAS;AAAA,IACxB,OAAO;AACL,YAAM,IAAI,aAAa,oBAAoB,KAAK,IAAI,UAAU;AAAA,IAChE;AAAA,EACF;AACA,MAAI,WAAW;AACf,MAAI,QAAQ;AACZ,SAAO,SAAS,GAAG;AAEjB,YAAQ,SAAS,QAAQ,KAAK,KAAK;AACnC,QAAI,QAAQ,GAAG;AACb;AAAA,IACF;AACA,UAAM,UAAU,SAAS,UAAU,GAAG,QAAQ,CAAC;AAC/C,QAAI,WAAW,SAAS,UAAU,QAAQ,CAAC;AAC3C,UAAM,YAAY,SAAS,YAAY,CAAC;AACxC,QAAI,YAAY,UAAU;AACxB,YAAM,MAAM,KAAK,UAAU,SAAS,GAAG,CAAC;AACxC,UAAI,SAAS,WAAW,KAAK;AAC3B,mBAAW;AAAA,MACb,OAAO;AACL,mBAAW,GAAG,GAAG,GAAG,SAAS,UAAU,GAAG,CAAC;AAAA,MAC7C;AAAA,IACF;AACA,eAAW,GAAG,OAAO,GAAG,QAAQ;AAChC;AAAA,EACF;AACA,SAAO,SACJ,QAAQ,aAAa,IAAI,EACzB,QAAQ,0BAA0B,MAAM,EACxC,QAAQ,SAAS,QAAQ;AAC9B;AAOO,IAAM,gBAAgB,SAAO;AAClC,QAAM,WAAW,WAAW,GAAG;AAE/B,MAAI,iBAAiB,KAAK,QAAQ,GAAG;AACnC,UAAM,IAAI,aAAa,oBAAoB,QAAQ,IAAI,UAAU;AAAA,EACnE;AACA,MAAI;AACF,UAAM,MAAc,eAAM,UAAU;AAAA,MAClC,SAAS;AAAA,MACT,qBAAqB;AAAA,IACvB,CAAC;AACD,WAAe,uBAAc,GAAG;AAAA,EAClC,SAAS,GAAG;AACV,UAAM,EAAE,QAAQ,IAAI;AACpB,QACE,2DAA2D;AAAA,MACzD;AAAA,IACF,KACA,CAAC,SAAS,SAAS,GAAG,GACtB;AACA,YAAM,QAAQ,SAAS,YAAY,GAAG;AACtC,YAAM,UAAU,SAAS,UAAU,KAAK;AACxC,UAAI,QAAQ,SAAS,GAAG,GAAG;AACzB,cAAM,SAAS,QAAQ,MAAM,IAAI,EAAE;AACnC,YAAI,SAAS,GAAG;AACd,iBAAO,cAAc,GAAG,QAAQ,IAAI;AAAA,QACtC;AACA,eAAO,cAAc,GAAG,QAAQ,GAAG;AAAA,MACrC;AACA,aAAO,cAAc,GAAG,QAAQ,GAAG;AAAA,IACrC,WAAW,YAAY,mBAAmB;AAExC,UAAI,kBAAkB,KAAK,QAAQ,GAAG;AACpC,eAAO,cAAc,GAAG,SAAS,WAAW,mBAAmB,IAAI,CAAC,EAAE;AAAA,MACxE,WAAW,CAAC,SAAS,SAAS,GAAG,GAAG;AAClC,eAAO,cAAc,GAAG,QAAQ,GAAG;AAAA,MACrC,OAAO;AACL,cAAM,IAAI,aAAa,oBAAoB,QAAQ,IAAI,UAAU;AAAA,MACnE;AAAA,IACF,OAAO;AACL,YAAM,IAAI,aAAa,oBAAoB,QAAQ,IAAI,UAAU;AAAA,IACnE;AAAA,EACF;AACF;AAQO,IAAM,UAAU,CAAC,MAAM,CAAC,MAAM;AACnC,QAAM,WAAW,oBAAI,IAAI;AACzB,QAAM,OAAO;AAAA,IACX,kBAAkB;AAAA,IAClB,sBAAsB;AAAA,IACtB,uBAAuB;AAAA,IACvB,mBAAmB;AAAA,IACnB,qBAAqB;AAAA,EACvB;AACA,QAAM,MAAM;AAAA,IACV,MAAM,MAAM;AACV,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK,gBAAgB;AACnB,cAAI,QAAQ,KAAK,KAAK,IAAI,GAAG;AAC3B,kBAAM,IAAI;AAAA,cACR,qBAAqB,KAAK,IAAI;AAAA,cAC9B;AAAA,YACF;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK,aAAa;AAChB,cAAI,QAAQ,KAAK,KAAK,IAAI,GAAG;AAC3B,kBAAM,IAAI;AAAA,cACR,qBAAqB,KAAK,IAAI;AAAA,cAC9B;AAAA,YACF;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK,mBAAmB;AACtB,cAAI,YAAY,IAAI,KAAK,IAAI,GAAG;AAC9B,iBAAK,oBAAoB;AACzB,iBAAK,uBAAuB;AAC5B,gBAAI,KAAK,SAAS,OAAO;AACvB,mBAAK,mBAAmB;AAAA,YAC1B;AAAA,UACF,WAAW,mBAAmB,IAAI,KAAK,IAAI,GAAG;AAC5C,iBAAK,sBAAsB;AAAA,UAC7B,WACE,gBAAgB,IAAI,KAAK,IAAI,KAC7B,MAAM,QAAQ,KAAK,QAAQ,KAC3B,KAAK,SAAS,QACd;AACA,iBAAK,oBAAoB;AAAA,UAC3B;AACA;AAAA,QACF;AAAA,QACA,KAAK,qBAAqB;AACxB,cAAI,sBAAsB,KAAK,KAAK,IAAI,GAAG;AACzC,iBAAK,oBAAoB;AAAA,UAC3B;AACA;AAAA,QACF;AAAA,QACA,KAAK,KAAK;AACR,cAAI,KAAK,UAAU;AACjB,iBAAK,oBAAoB;AACzB,iBAAK,wBAAwB;AAAA,UAC/B;AACA;AAAA,QACF;AAAA,QACA,KAAK,UAAU;AACb,mBAAS,IAAI,KAAK,QAAQ;AAC1B;AAAA,QACF;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,EAAQ,cAAK,KAAK,GAAG;AACrB,MAAI,KAAK,sBAAsB,MAAM;AACnC,IAAQ,iBAAQ,KAAK,CAAC,MAAM,MAAM,SAAS;AACzC,UAAI,MAAM;AACR,YAAI,KAAK,SAAS,qBAAqB,YAAY,IAAI,KAAK,IAAI,GAAG;AACjE,gBAAM,WAAW,KAAK,OAAO,OAAK;AAChC,kBAAM,EAAE,MAAM,KAAK,IAAI;AACvB,mBAAO,SAAS,qBAAqB,YAAY,IAAI,IAAI;AAAA,UAC3D,CAAC;AACD,qBAAW,EAAE,SAAS,KAAK,UAAU;AAEnC,uBAAW,EAAE,UAAU,cAAc,KAAK,UAAU;AAElD,yBAAW,EAAE,UAAU,mBAAmB,KAAK,eAAe;AAC5D,oBAAI,SAAS,IAAI,kBAAkB,GAAG;AACpC,2BAAS,OAAO,kBAAkB;AAAA,gBACpC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF,WACE,KAAK,SAAS,qBACd,gBAAgB,IAAI,KAAK,IAAI,KAC7B,MAAM,QAAQ,KAAK,QAAQ,KAC3B,KAAK,SAAS,QACd;AACA,gBAAM,WAAW,KAAK,OAAO,OAAK;AAChC,kBAAM,EAAE,UAAU,MAAM,KAAK,IAAI;AACjC,kBAAM,MACJ,SAAS,qBACT,gBAAgB,IAAI,IAAI,KACxB,MAAM,QAAQ,QAAQ,KACtB,SAAS;AACX,mBAAO;AAAA,UACT,CAAC;AACD,qBAAW,EAAE,SAAS,KAAK,UAAU;AAEnC,uBAAW,EAAE,UAAU,cAAc,KAAK,UAAU;AAClD,kBAAI,SAAS,IAAI,aAAa,GAAG;AAC/B,yBAAS,OAAO,aAAa;AAAA,cAC/B;AAAA,YACF;AAAA,UACF;AAAA,QACF,WACE,KAAK,SAAS,uBACd,sBAAsB,KAAK,KAAK,IAAI,GACpC;AACA,gBAAM,WAAW,KAAK,OAAO,OAAK;AAChC,kBAAM,EAAE,MAAM,KAAK,IAAI;AACvB,kBAAM,MACJ,SAAS,uBAAuB,sBAAsB,KAAK,IAAI;AACjE,mBAAO;AAAA,UACT,CAAC;AACD,qBAAW,EAAE,SAAS,KAAK,UAAU;AAEnC,uBAAW,EAAE,UAAU,cAAc,KAAK,UAAU;AAClD,kBAAI,SAAS,IAAI,aAAa,GAAG;AAC/B,yBAAS,OAAO,aAAa;AAAA,cAC/B;AAAA,YACF;AAAA,UACF;AAAA,QACF,WAAW,KAAK,SAAS,OAAO,KAAK,UAAU;AAC7C,gBAAM,WAAW,KAAK,OAAO,OAAK;AAChC,kBAAM,EAAE,UAAU,KAAK,IAAI;AAC3B,kBAAM,MAAM,SAAS,OAAO;AAC5B,mBAAO;AAAA,UACT,CAAC;AACD,qBAAW,EAAE,SAAS,KAAK,UAAU;AACnC,kBAAM,EAAE,SAAS,IAAI;AAErB,uBAAW,EAAE,UAAU,cAAc,KAAK,UAAU;AAClD,kBAAI,SAAS,IAAI,aAAa,GAAG;AAC/B,yBAAS,OAAO,aAAa;AAAA,cAC/B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL;AAAA,IACA,UAAU,CAAC,GAAG,QAAQ;AAAA,EACxB;AACF;AAQO,IAAM,kBAAkB,CAAC,GAAG,MAAM;AACvC,QAAM,OAAO,eAAe,IAAI,EAAE,IAAI;AACtC,QAAM,OAAO,eAAe,IAAI,EAAE,IAAI;AACtC,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT,WAAW,OAAO,MAAM;AACtB,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAOO,IAAM,UAAU,UAAQ;AAC7B,QAAM,MAAM,CAAC,GAAG,IAAI;AACpB,MAAI,IAAI,SAAS,GAAG;AAClB,QAAI,KAAK,eAAe;AAAA,EAC1B;AACA,SAAO;AACT;AAQO,IAAM,eAAe,cAAY;AACtC,MAAI;AACJ,MAAI;AACJ,MAAI,YAAY,OAAO,aAAa,UAAU;AAC5C,QAAI,SAAS,QAAQ,GAAG,IAAI,IAAI;AAC9B,OAAC,QAAQ,SAAS,IAAI,SAAS,MAAM,GAAG;AAAA,IAC1C,OAAO;AACL,eAAS;AACT,kBAAY;AAAA,IACd;AAAA,EACF,OAAO;AACL,UAAM,IAAI,aAAa,oBAAoB,QAAQ,IAAI,UAAU;AAAA,EACnE;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;;;AGvXA,IAAM,uBAAuB,oBAAI,IAAI;AAAA,EACnC,GAAG;AAAA,EACH;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAM,iBAAiB,IAAI,OAAO,aAAa,SAAS,GAAG,SAAS,KAAK,GAAG;AAYrE,IAAM,6BAA6B,CAAC,SAAS,SAAS,MAAM,CAAC,MAAM;AACxE,MAAI,YAAY,qBAAqB;AACnC,UAAM,IAAI,UAAU,uBAAuB,QAAQ,OAAO,CAAC,EAAE;AAAA,EAC/D;AACA,QAAM,EAAE,SAAS,KAAK,IAAI;AAC1B,QAAM,UACJ,eAAe,IAAI,OAAO,KAC1B,oBAAoB,IAAI,OAAO,KAC/B,QAAQ,WAAW,UAAU;AAC/B,MAAI,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM;AACjC,UAAM,IAAI,aAAa,4BAA4B,OAAO,IAAI,UAAU;AAAA,EAC1E,WAAW,MAAM;AACf,QAAI,MAAM;AACV,QAAI,oBAAoB,IAAI,OAAO,GAAG;AACpC,YAAM,gCAAgC,OAAO;AAAA,IAC/C,WAAW,QAAQ,WAAW,UAAU,GAAG;AACzC,YAAM,gCAAgC,OAAO;AAAA,IAC/C,OAAO;AACL,YAAM,gCAAgC,OAAO;AAAA,IAC/C;AACA,UAAM,IAAI,aAAa,KAAK,iBAAiB;AAAA,EAC/C;AACF;AASO,IAAM,4BAA4B,CAAC,KAAK,SAAS;AACtD,QAAM,EAAE,KAAK,IAAI;AAEjB,MAAI,OAAO,SAAS,YAAY,SAAS,IAAI;AAC3C,UAAM,OAAO,SAAS,KAAK,mBAAmB,QAAQ,IAAI;AAC1D,UAAM,IAAI,UAAU,uBAAuB,IAAI,EAAE;AAAA,EACnD;AACA,QAAM,MAAM,kBAAkB,IAAI;AAClC,SAAO,SAAS;AAClB;AASO,IAAM,2BAA2B,CAAC,KAAK,SAAS;AACrD,QAAM,EAAE,MAAM,MAAM,MAAM,IAAI;AAC9B,MAAI;AACJ,MAAI,SAAS,UAAU,OAAO;AAC5B,cAAU;AAAA,EACZ,WAAW,SAAS,SAAS,MAAM;AACjC,cAAU,iBAAiB,IAAI;AAAA,EACjC,OAAO;AAEL,WAAO;AAAA,EACT;AACA,QAAM,gBAAgB,kBAAkB,IAAI;AAE5C,MAAI,OAAO,kBAAkB,UAAU;AACrC,WAAO;AAAA,EACT;AAEA,MAAI,YAAY,KAAK;AACnB,WAAO,kBAAkB;AAAA,EAC3B;AAEA,MAAI,CAAC,eAAe,KAAK,OAAO,GAAG;AACjC,WAAO;AAAA,EACT;AAEA,MAAI;AACJ,MAAI,QAAQ,SAAS,GAAG,GAAG;AACzB,UAAM,CAAC,UAAU,SAAS,GAAG,QAAQ,IAAI,QAAQ,MAAM,GAAG;AAC1D,QAAI,eAAe,GAAG,SAAS,GAAG,SAAS;AAC3C,QAAI,aAAa,KAAK;AACpB,qBAAe,GAAG,QAAQ,GAAG,SAAS;AAAA,IACxC;AACA,UAAM,cAAc,IAAI,OAAO,GAAG,SAAS;AAC3C,UAAM,eAAe,SAAS,IAAI,UAAQ,IAAI,IAAI,GAAG,SAAS,EAAE,EAAE,KAAK,EAAE;AACzE,sBAAkB,IAAI;AAAA,MACpB,IAAI,YAAY,GAAG,WAAW,GAAG,YAAY;AAAA,MAC7C;AAAA,IACF;AAAA,EACF,OAAO;AACL,sBAAkB,IAAI,OAAO,IAAI,OAAO,GAAG,SAAS,KAAK,GAAG;AAAA,EAC9D;AACA,SAAO,gBAAgB,KAAK,aAAa;AAC3C;AAQO,IAAM,6BAA6B,CAAC,UAAU,IAAI,OAAO,CAAC,MAAM;AACrE,MAAI,CAAC,oBAAoB,KAAK,OAAO,KAAK,MAAM,aAAa,cAAc;AACzE,WAAO;AAAA,EACT;AACA,QAAM,EAAE,WAAW,WAAW,IAAI;AAClC,MACE,CAAC,qBAAqB,IAAI,SAAS,KACnC,CAAC,gBAAgB,MAAM,EAAE,gBAAgB,KAAK,CAAC,GAC/C;AACA,WAAO;AAAA,EACT;AACA,MAAI,aAAa;AACjB,MAAI,KAAK,YAAY,KAAK,aAAa,UAAU,GAAG;AAClD,iBAAa;AAAA,EACf,WAAW,cAAc,UAAU;AACjC,QACE,cACA,WAAW,cAAc,eACxB,WAAW,YAAY,WAAW,aAAa,UAAU,IAC1D;AACA,mBAAa;AAAA,IACf;AAAA,EACF,WAAW,cAAc,YAAY;AACnC,QAAI,UAAU;AACd,WAAO,SAAS;AACd,UACE,QAAQ,cAAc,eACrB,QAAQ,YAAY,QAAQ,aAAa,UAAU,IACpD;AAEA,YAAI;AACJ,YAAI,UAAU,QAAQ;AACtB,eAAO,SAAS;AACd,cAAI,QAAQ,cAAc,UAAU;AAClC,qBAAS;AACT;AAAA,UACF;AACA,oBAAU,QAAQ;AAAA,QACpB;AACA,YAAI,CAAC,UAAU,CAAC,OAAO,SAAS,IAAI,GAAG;AACrC,uBAAa;AAAA,QACf;AAEA;AAAA,MACF;AACA,gBAAU,QAAQ;AAAA,IACpB;AAAA,EACF;AACA,MAAI,YAAY,YAAY;AAC1B,WAAO;AAAA,EACT;AACA,SAAO,CAAC;AACV;AAQO,IAAM,2BAA2B,CAAC,SAAS,SAAS;AACzD,MACE,CAAC,wBAAwB,KAAK,OAAO,KACrC,MAAM,aAAa,cACnB;AACA,WAAO;AAAA,EACT;AACA,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,aAAa;AACjB,UAAQ,WAAW;AAAA,IACjB,KAAK;AAAA,IACL,KAAK,SAAS;AACZ,YAAM,kBAAkB,CAAC,KAAK,QAAQ,eAAe,IAAI,KAAK,IAAI;AAClE,UAAI,cAAc,cAAc,iBAAiB;AAC/C,qBACE,KAAK,YACL,KAAK,aAAa,UAAU,KAC5B,KAAK,YACL,KAAK,aAAa,UAAU;AAAA,MAChC,OAAO;AAEL,qBAAa;AAAA,MACf;AACA;AAAA,IACF;AAAA,IACA,SAAS;AACP,mBAAa,CAAC,kBAAkB,IAAI;AAAA,IACtC;AAAA,EACF;AACA,MAAI,YAAY,aAAa;AAC3B,WAAO;AAAA,EACT;AACA,SAAO,CAAC;AACV;AAWO,IAAM,yBAAyB,CAAC,KAAK,MAAM,MAAM,CAAC,MAAM;AAC7D,QAAM;AAAA,IACJ,OAAO;AAAA,IACP,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,EACT,IAAI;AACJ,QAAM,EAAE,OAAO,QAAQ,IAAI;AAC3B,MAAI,OAAO,aAAa,YAAY,CAAC,UAAU,KAAK,QAAQ,KAAK,CAAC,SAAS;AACzE,UAAM,UAAM,0BAAY,GAAG;AAC3B,UAAM,IAAI,aAAa,oBAAoB,GAAG,IAAI,UAAU;AAAA,EAC9D;AACA,MAAI,CAAC,KAAK,YAAY,QAAQ;AAC5B,WAAO;AAAA,EACT;AACA,QAAM;AAAA,IACJ,eAAe,EAAE,YAAY;AAAA,EAC/B,IAAI;AACJ,QAAM,gBAAgB,mBAAmB,UAAU,WAAW;AAC9D,MAAI,cAAc,iBAAiB,QAAQ,IAAI;AAC/C,MAAI,CAAC,eAAe;AAClB,kBAAc,YAAY,YAAY;AAAA,EACxC;AACA,QAAM,EAAE,QAAQ,WAAW,WAAW,aAAa,IACjD,aAAa,WAAW;AAC1B,MAAI,YAAY,SAAS,GAAG,GAAG;AAC7B,UAAM,EAAE,QAAQC,WAAU,IAAI,aAAa,WAAW;AACtD,QAAIA,eAAc,MAAMA,eAAc,OAAO,CAAC,OAAO;AACnD,UAAI,SAAS;AACX,eAAO;AAAA,MACT;AACA,YAAM,UAAM,0BAAY,GAAG;AAC3B,YAAM,IAAI,aAAa,oBAAoB,GAAG,IAAI,UAAU;AAAA,IAC9D;AAAA,EACF;AACA,QAAM,SAAS,oBAAoB,MAAM;AAAA,IACvC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,CAAC,OAAO,QAAQ;AAClB,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB;AACpB,MAAI,SAAS,SAAS,OAAO;AAC3B,oBAAgB,gBAAgB,SAAS,OAAO,SAAS,KAAK,YAAY;AAAA,EAC5E,WAAW,SAAS,SAAS,QAAQ;AACnC,oBAAgB,gBACZ,SAAS,QACT,SAAS,MAAM,YAAY;AAAA,EACjC;AACA,UAAQ,YAAY;AAAA,IAClB,KAAK,MAAM;AACT,aACE,CAAC,CAAC,iBACF,OAAO,KAAK,OAAK,EAAE,MAAM,KAAK,EAAE,SAAS,aAAa,CAAC;AAAA,IAE3D;AAAA,IACA,KAAK,MAAM;AACT,aACE,CAAC,CAAC,iBACF,OAAO;AAAA,QACL,OAAK,MAAM,iBAAiB,EAAE,WAAW,GAAG,aAAa,GAAG;AAAA,MAC9D;AAAA,IAEJ;AAAA,IACA,KAAK,MAAM;AACT,aAAO,CAAC,CAAC,iBAAiB,OAAO,KAAK,OAAK,EAAE,WAAW,aAAa,CAAC;AAAA,IACxE;AAAA,IACA,KAAK,MAAM;AACT,aAAO,CAAC,CAAC,iBAAiB,OAAO,KAAK,OAAK,EAAE,SAAS,aAAa,CAAC;AAAA,IACtE;AAAA,IACA,KAAK,MAAM;AACT,aAAO,CAAC,CAAC,iBAAiB,OAAO,KAAK,OAAK,EAAE,SAAS,aAAa,CAAC;AAAA,IACtE;AAAA,IACA,KAAK;AAAA,IACL,SAAS;AACP,aAAO,OAAO,KAAK,OAAK,MAAM,aAAa;AAAA,IAC7C;AAAA,EACF;AACF;AAWO,IAAM,oBAAoB,CAAC,KAAK,MAAM,MAAM,CAAC,MAAM;AACxD,QAAM,UAAU,iBAAiB,IAAI,IAAI;AACzC,QAAM,EAAE,WAAW,cAAc,OAAO,IAAI;AAC5C,QAAM,EAAE,OAAO,QAAQ,IAAI;AAC3B,MAAI,EAAE,QAAQ,WAAW,WAAW,aAAa,IAAI;AAAA,IACnD;AAAA,IACA;AAAA,EACF;AACA,MACE,KAAK,cAAc,gBAAgB,gBAClC,CAAC,gBAAgB,iBAAiB,mCACnC,eAAe,KAAK,SAAS,GAC7B;AACA,gBAAY,UAAU,YAAY;AAClC,mBAAe,aAAa,YAAY;AAAA,EAC1C;AACA,MAAI,aAAa;AACjB,MAAI,gBAAgB;AAEpB,MAAI,UAAU,SAAS,GAAG,GAAG;AAC3B,KAAC,YAAY,aAAa,IAAI,UAAU,MAAM,GAAG;AAAA,EACnD,OAAO;AACL,iBAAa,UAAU;AACvB,oBAAgB;AAAA,EAClB;AACA,UAAQ,WAAW;AAAA,IACjB,KAAK,IAAI;AACP,UACE,CAAC,cACD,CAAC,iBACA,iBAAiB,OAAO,iBAAiB,gBAC1C;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA,IACA,KAAK,KAAK;AACR,UAAI,iBAAiB,OAAO,iBAAiB,eAAe;AAC1D,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA,IACA,SAAS;AACP,UAAI,CAAC,OAAO;AACV,YAAI,SAAS;AACX,iBAAO;AAAA,QACT;AACA,cAAM,UAAM,0BAAY,GAAG;AAC3B,cAAM,IAAI,aAAa,oBAAoB,GAAG,IAAI,UAAU;AAAA,MAC9D;AACA,YAAM,QAAQ,KAAK,mBAAmB,SAAS;AAC/C,YAAM,SAAS,KAAK,mBAAmB,UAAU;AACjD,UAAI,UAAU,UAAU,cAAc,YAAY;AAChD,YAAI,iBAAiB,OAAO,iBAAiB,eAAe;AAC1D,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,WAAW,CAAC,WAAW,CAAC,OAAO;AAC7B,cAAM,IAAI,aAAa,wBAAwB,SAAS,IAAI,UAAU;AAAA,MACxE;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;AC1VA,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,WAAW,oBAAI,IAAI,CAAC,GAAG,YAAY,YAAY,MAAM,CAAC;AAC5D,IAAM,oBAAoB,oBAAI,IAAI,CAAC,GAAG,YAAY,MAAM,CAAC;AACzD,IAAM,kBAAkB,IAAI,IAAI,WAAW;AAC3C,IAAM,wBAAwB,oBAAI,IAAI,CAAC,GAAG,YAAY,QAAQ,CAAC;AAC/D,IAAM,kBAAkB,oBAAI,IAAI,CAAC,GAAG,YAAY,UAAU,OAAO,CAAC;AAClE,IAAM,qBAAqB,oBAAI,IAAI,CAAC,GAAG,aAAa,GAAG,YAAY,MAAM,CAAC;AAC1E,IAAM,kBAAkB,oBAAI,IAAI,CAAC,UAAU,OAAO,CAAC;AACnD,IAAM,mBAAmB,oBAAI,IAAI,CAAC,SAAS,QAAQ,CAAC;AACpD,IAAM,iBAAiB,oBAAI,IAAI,CAAC,YAAY,WAAW,OAAO,QAAQ,OAAO,CAAC;AAC9E,IAAM,cAAc,oBAAI,IAAI;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AACD,IAAM,YAAY,oBAAI,IAAI,CAAC,UAAU,QAAQ,cAAc,CAAC;AAMrD,IAAM,SAAN,MAAa;AAAA;AAAA,EAElB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,QAAQ;AAClB,SAAK,UAAU;AACf,SAAK,YAAY,oBAAI,QAAQ;AAC7B,SAAK,iBAAiB,oBAAI,QAAQ;AAClC,SAAK,qBAAqB,oBAAI,QAAQ;AACtC,SAAK,WAAW,oBAAI,QAAQ;AAC5B,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,oBAAoB;AACzB,SAAK,iBAAiB,oBAAI,IAAI;AAAA,MAC5B;AAAA,QACE,MAAM,CAAC,SAAS,SAAS;AAAA,QACzB,SAAS,KAAK;AAAA,MAChB;AAAA,MACA;AAAA,QACE,MAAM,CAAC,WAAW,OAAO;AAAA,QACzB,SAAS,KAAK;AAAA,MAChB;AAAA,MACA;AAAA,QACE,MAAM,CAAC,aAAa,aAAa,WAAW,UAAU;AAAA,QACtD,SAAS,KAAK;AAAA,MAChB;AAAA,MACA;AAAA,QACE,MAAM,CAAC,OAAO;AAAA,QACd,SAAS,KAAK;AAAA,MAChB;AAAA,IACF,CAAC;AACD,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,UAAU,CAAC,GAAG,MAAM,CAAC,MAAM;AACzB,UAAM,WAAW,IAAI,YAAY,KAAK;AACtC,QAAI,UAAU;AACZ;AAAA,IACF;AACA,UAAM,iBACJ,aAAa,gBAAgB,aAAa,KAAK,QAAQ;AACzD,QAAI,gBAAgB;AAClB,UAAI,EAAE,SAAS,mBAAmB;AAChC,YAAI,KAAK,OAAO;AACd,kBAAQ,KAAK,EAAE,OAAO;AAAA,QACxB;AACA;AAAA,MACF;AACA,YAAM,IAAI,KAAK,QAAQ,aAAa,EAAE,SAAS,EAAE,IAAI;AAAA,IACvD;AACA,QAAI,EAAE,QAAQ,KAAK,SAAS;AAC1B,YAAM,IAAI,KAAK,QAAQ,EAAE,IAAI,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;AAAA,IACxD;AACA,UAAM;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,QAAQ,CAAC,UAAU,MAAM,MAAM,CAAC,MAAM;AACpC,UAAM,EAAE,OAAO,eAAe,UAAU,KAAK,IAAI;AACjD,SAAK,SAAS,CAAC,CAAC;AAChB,SAAK,iBAAiB;AACtB,SAAK,YAAY,CAAC,CAAC;AACnB,SAAK,QAAQ,CAAC,CAAC;AACf,KAAC,KAAK,WAAW,KAAK,OAAO,KAAK,OAAO,IAAI,eAAe,IAAI;AAChE,SAAK,eAAe,IAAI,IAAI,KAAK,UAAU,GAAG;AAC9C,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,KAAC,KAAK,MAAM,KAAK,MAAM,IAAI,KAAK,YAAY,QAAQ;AACpD,SAAK,qBAAqB,oBAAI,QAAQ;AACtC,SAAK,iBAAiB,CAAC;AACvB,SAAK,WAAW,oBAAI,QAAQ;AAC5B,SAAK,cAAc;AACnB,SAAK,cAAc;AAEnB,SAAK,oBAAoB;AACzB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,oBAAoB,SAAO;AACzB,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,uBAAuB,SAAO;AAC5B,UAAM,EAAE,IAAI,IAAI;AAChB,QAAI,CAAC,aAAa,IAAI,GAAG,GAAG;AAC1B,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,oBAAoB,SAAO;AACzB,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,oBAAoB,SAAO;AACzB,SAAK,SAAS;AACd,SAAK,qBAAqB,oBAAI,QAAQ;AACtC,SAAK,WAAW,oBAAI,QAAQ;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,0BAA0B,MAAM;AAC9B,UAAM,MAAM;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AACA,UAAM,OAAO,CAAC;AACd,eAAW,gBAAgB,KAAK,gBAAgB;AAC9C,YAAM,EAAE,MAAM,QAAQ,IAAI;AAC1B,YAAM,IAAI,KAAK;AACf,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAM,MAAM,KAAK,CAAC;AAClB,aAAK,KAAK,KAAK,QAAQ,iBAAiB,KAAK,SAAS,GAAG,CAAC;AAAA,MAC5D;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,2BAA2B,CAAC,UAAU,aAAa;AACjD,QAAI,aAAa;AACjB,QAAI,aAAa;AACjB,UAAM,MAAM,CAAC;AACb,UAAM,IAAI,SAAS;AACnB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,QAAQ,CAAC,GAAG,SAAS,CAAC,CAAC;AAC7B,YAAM,SAAS,CAAC;AAChB,UAAI,OAAO,MAAM,MAAM;AACvB,UAAI,QAAQ,KAAK,SAAS,YAAY;AACpC,cAAM,SAAS,oBAAI,IAAI;AACvB,eAAO,MAAM;AACX,cAAI,KAAK,SAAS,YAAY;AAC5B,kBAAM,CAAC,QAAQ,IAAI;AACnB,gBAAI,CAAC,YAAY,SAAS,SAAS,YAAY;AAC7C,oBAAM,MAAM,oBAAoB,QAAQ;AACxC,mBAAK,QAAQ,kBAAkB,KAAK,YAAY,KAAK,OAAO,CAAC;AAE7D,qBAAO,EAAE,KAAK,CAAC,GAAG,YAAY,OAAO,YAAY,MAAM;AAAA,YACzD;AACA,gBAAI,KAAK,SAAS,OAAO,KAAK,SAAS,KAAK;AAC1C,2BAAa;AAAA,YACf,OAAO;AACL,2BAAa;AAAA,YACf;AACA,mBAAO,KAAK,EAAE,OAAO,MAAM,QAAQ,QAAQ,MAAM,EAAE,CAAC;AACpD,mBAAO,MAAM;AAAA,UACf,OAAO;AACL,gBAAI,KAAK,QAAQ,OAAO,KAAK,SAAS,UAAU;AAC9C,oBAAM,gBAAgB,iBAAiB,KAAK,IAAI;AAChD,kBAAI,kBAAkB,KAAK,MAAM;AAC/B,qBAAK,OAAO;AAAA,cACd;AACA,kBAAI,OAAO,KAAK,aAAa,GAAG;AAC9B,qBAAK,YAAY;AAAA,cACnB;AAAA,YACF;AACA,mBAAO,IAAI,IAAI;AAAA,UACjB;AACA,cAAI,MAAM,QAAQ;AAChB,mBAAO,MAAM,MAAM;AAAA,UACrB,OAAO;AACL,mBAAO,KAAK,EAAE,OAAO,MAAM,QAAQ,QAAQ,MAAM,EAAE,CAAC;AACpD,mBAAO,MAAM;AACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,KAAK,EAAE,QAAQ,KAAK,MAAM,UAAU,OAAO,MAAM,MAAM,CAAC;AAAA,IAC9D;AACA,WAAO,EAAE,KAAK,YAAY,WAAW;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,cAAY;AACxB,UAAM,QAAQ,CAAC;AACf,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,QAAI;AACJ,QAAI,KAAK,eAAe,IAAI,KAAK,SAAS,GAAG;AAC3C,YAAM,aAAa,KAAK,eAAe,IAAI,KAAK,SAAS;AACzD,UAAI,cAAc,WAAW,IAAI,GAAG,QAAQ,EAAE,GAAG;AAC/C,cAAM,OAAO,WAAW,IAAI,GAAG,QAAQ,EAAE;AACzC,cAAM,KAAK;AACX,aAAK,cAAc,KAAK;AACxB,aAAK,cAAc,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,KAAK;AACP,YAAM,IAAI,IAAI;AACd,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAI,CAAC,EAAE,MAAM;AACb,YAAI,CAAC,EAAE,WAAW;AAClB,YAAI,CAAC,EAAE,OAAO;AACd,cAAM,CAAC,IAAI,CAAC;AAAA,MACd;AAAA,IACF,OAAO;AACL,UAAI;AACJ,UAAI;AACF,iBAAS,cAAc,QAAQ;AAAA,MACjC,SAAS,GAAG;AACV,eAAO,KAAK,QAAQ,CAAC;AAAA,MACvB;AACA,YAAM,EAAE,UAAU,KAAK,IAAI,QAAQ,MAAM;AACzC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,iBACJ,oBACA,uBACA,CAAC,EAAE,wBAAwB;AAC7B,YAAM,YAAY,KAAK,yBAAyB,UAAU,QAAQ;AAClE,YAAM,UAAU;AAChB,WAAK,cAAc,UAAU;AAC7B,WAAK,cAAc,kBAAkB,UAAU;AAC/C,UAAI;AACJ,UAAI,KAAK,eAAe,IAAI,KAAK,SAAS,GAAG;AAC3C,qBAAa,KAAK,eAAe,IAAI,KAAK,SAAS;AAAA,MACrD,OAAO;AACL,qBAAa,oBAAI,IAAI;AAAA,MACvB;AACA,iBAAW,IAAI,GAAG,QAAQ,IAAI;AAAA,QAC5B;AAAA,QACA,YAAY,KAAK;AAAA,QACjB,YAAY,KAAK;AAAA,MACnB,CAAC;AACD,WAAK,eAAe,IAAI,KAAK,WAAW,UAAU;AAElD,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAM,CAAC,IAAI,CAAC;AAAA,MACd;AAAA,IACF;AACA,WAAO,CAAC,KAAK,KAAK;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,oBAAoB,CAAC,MAAM,MAAM,CAAC,MAAM;AACtC,UAAM,EAAE,QAAQ,OAAO,aAAa,eAAe,IAAI;AACvD,QAAI,OAAO;AACT,aAAO,KAAK,UAAU,iBAAiB,MAAM,UAAU;AAAA,IACzD;AACA,QAAI,KAAK,SAAS,IAAI,IAAI,GAAG;AAC3B,aAAO,KAAK,SAAS,IAAI,IAAI;AAAA,IAC/B;AACA,UAAM,SAAS,KAAK,UAAU,iBAAiB,MAAM,UAAU;AAC/D,SAAK,SAAS,IAAI,MAAM,MAAM;AAC9B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,uBAAuB,cAAY;AACjC,QAAI,KAAK,UAAU,IAAI,QAAQ,GAAG;AAChC,aAAO,KAAK,UAAU,IAAI,QAAQ;AAAA,IACpC;AACA,UAAM,EAAE,SAAS,IAAI,QAAQ,QAAQ;AACrC,SAAK,UAAU,IAAI,UAAU,QAAQ;AACrC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,uBAAuB,CAAC,YAAY,kBAAkB,MAAM,CAAC,MAAM;AACjE,UAAM,WAAW,CAAC;AAClB,UAAM,SAAS,KAAK,kBAAkB,YAAY,EAAE,OAAO,KAAK,CAAC;AACjE,QAAI,YAAY,OAAO,WAAW;AAClC,WAAO,WAAW;AAChB,UAAI,kBAAkB;AACpB,YAAI,UAAU,SAAS,GAAG;AACxB,cAAI,UAAU;AACd,gBAAM,IAAI,iBAAiB;AAC3B,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,kBAAM,SAAS,iBAAiB,CAAC;AACjC,gBAAI,KAAK,aAAa,QAAQ,WAAW,GAAG,GAAG;AAC7C,wBAAU;AACV;AAAA,YACF;AAAA,UACF;AACA,cAAI,SAAS;AACX,qBAAS,KAAK,SAAS;AAAA,UACzB;AAAA,QACF;AAAA,MACF,OAAO;AACL,iBAAS,KAAK,SAAS;AAAA,MACzB;AACA,kBAAY,OAAO,YAAY;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,mBAAmB,CAAC,KAAK,MAAM,MAAM,CAAC,MAAM;AAC1C,UAAM,EAAE,GAAG,GAAG,SAAS,IAAI;AAC3B,UAAM,EAAE,WAAW,IAAI;AACvB,QAAI,CAAC,YAAY;AACf,YAAM,cAAc,oBAAI,IAAI;AAC5B,UAAI,SAAS,KAAK,SAAS,IAAI,IAAI,IAAI,MAAM,GAAG;AAC9C,YAAI,UAAU;AACZ,gBAAMC,oBAAmB,KAAK,qBAAqB,QAAQ;AAC3D,gBAAM,IAAIA,kBAAiB;AAC3B,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,kBAAM,SAASA,kBAAiB,CAAC;AACjC,gBAAI,KAAK,aAAa,QAAQ,MAAM,GAAG,GAAG;AACxC,0BAAY,IAAI,IAAI;AACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF,OAAO;AACL,sBAAY,IAAI,IAAI;AAAA,QACtB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,UAAM,mBAAmB,WACrB,KAAK,qBAAqB,QAAQ,IAClC;AACJ,UAAM,WAAW,KAAK;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,eAAe,iBAAiB,UAAU,GAAG;AACnD,WAAO,IAAI,IAAI,YAAY;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,oBAAoB,CAAC,KAAK,SAAS;AACjC,UAAM,EAAE,WAAW,IAAI;AACvB,QAAI,CAAC,YAAY;AACf,UAAI,SAAS,KAAK,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,GAAG;AACtD,eAAO,oBAAI,IAAI,CAAC,IAAI,CAAC;AAAA,MACvB;AACA,aAAO,oBAAI,IAAI;AAAA,IACjB;AACA,UAAM,gBAAgB,CAAC;AACvB,UAAM,SAAS,KAAK,kBAAkB,YAAY,EAAE,OAAO,KAAK,CAAC;AACjE,QAAI,UAAU,OAAO,WAAW;AAChC,WAAO,SAAS;AACd,UACE,QAAQ,cAAc,KAAK,aAC3B,QAAQ,iBAAiB,KAAK,gBAC9B,QAAQ,WAAW,KAAK,QACxB;AACA,sBAAc,KAAK,OAAO;AAAA,MAC5B;AACA,gBAAU,OAAO,YAAY;AAAA,IAC/B;AACA,UAAM,eAAe,iBAAiB,eAAe,GAAG;AACxD,WAAO,IAAI,IAAI,YAAY;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,gBAAgB,CAAC,KAAK,MAAM,SAAS,MAAM,CAAC,MAAM;AAChD,UAAM;AAAA,MACJ,KAAK,EAAE,GAAG,GAAG,MAAM,aAAa;AAAA,MAChC;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,oBAAI,IAAI;AACvB,QAAI,cAAc;AAChB,UAAI,iBAAiB,QAAQ;AAC3B,eAAO,IAAI,KAAK,CAAC;AACjB,eAAO,IAAI,KAAK,CAAC;AAAA,MACnB,WAAW,iBAAiB,OAAO;AACjC,eAAO,IAAI,KAAK,CAAC;AACjB,eAAO,IAAI,KAAK,CAAC;AAAA,MACnB;AACA,UAAI,QAAQ,QAAQ,MAAM,IAAI,IAAI;AAChC,eAAO,IAAI,WAAW,IAAI;AAAA,MAC5B;AAAA,IACF,OAAO;AACL,UAAI,OAAO,MAAM,YAAY,QAAQ,KAAK,CAAC,GAAG;AAC5C,eAAO,IAAI,KAAK,IAAI,CAAC;AAAA,MACvB,OAAO;AACL,eAAO,IAAI,KAAK,CAAC;AAAA,MACnB;AACA,UAAI,OAAO,MAAM,YAAY,QAAQ,KAAK,CAAC,GAAG;AAC5C,eAAO,IAAI,KAAK,IAAI,CAAC;AAAA,MACvB,OAAO;AACL,eAAO,IAAI,KAAK,CAAC;AAAA,MACnB;AACA,UAAI,QAAQ,QAAQ,MAAM,IAAI,IAAI;AAChC,eAAO,IAAI,WAAW,IAAI;AAAA,MAC5B;AAAA,IACF;AACA,QAAI,YAAY,eAAe,YAAY,kBAAkB;AAC3D,UAAI,UAAU;AACZ,eAAO,IAAI,YAAY,QAAQ;AAAA,MACjC;AACA,YAAM,MAAM,OAAO,YAAY,MAAM;AACrC,YAAM,QAAQ,KAAK,iBAAiB,KAAK,MAAM,GAAG;AAClD,aAAO;AAAA,IACT,WAAW,YAAY,iBAAiB,YAAY,oBAAoB;AACtE,YAAM,MAAM,OAAO,YAAY,MAAM;AACrC,YAAM,QAAQ,KAAK,kBAAkB,KAAK,IAAI;AAC9C,aAAO;AAAA,IACT;AACA,WAAO,oBAAI,IAAI;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,sBAAsB,CAAC,WAAW,MAAM,MAAM,CAAC,MAAM;AACnD,QAAI,MAAM,QAAQ,SAAS,KAAK,UAAU,QAAQ;AAEhD,YAAM,SAAS,CAAC,GAAG,SAAS;AAC5B,YAAM,CAAC,IAAI,IAAI;AACf,YAAM,EAAE,MAAM,SAAS,IAAI;AAC3B,UAAI;AACJ,UAAI,aAAa,YAAY;AAC3B,gBAAQ,OAAO,MAAM;AAAA,MACvB,OAAO;AACL,gBAAQ;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,MACF;AACA,YAAM,aAAa,CAAC;AACpB,aAAO,OAAO,QAAQ;AACpB,cAAM,CAAC,IAAI,IAAI;AACf,cAAM,EAAE,MAAM,SAAS,IAAI;AAC3B,YAAI,aAAa,YAAY;AAC3B;AAAA,QACF,OAAO;AACL,qBAAW,KAAK,OAAO,MAAM,CAAC;AAAA,QAChC;AAAA,MACF;AACA,YAAM,OAAO;AAAA,QACX;AAAA,QACA,QAAQ;AAAA,MACV;AACA,UAAI,MAAM;AACV,YAAM,QAAQ,KAAK,iBAAiB,MAAM,MAAM,GAAG;AACnD,UAAI,MAAM,MAAM;AACd,YAAI,OAAO,QAAQ;AACjB,cAAI,OAAO;AACX,qBAAW,YAAY,OAAO;AAC5B,mBAAO,KAAK,oBAAoB,QAAQ,UAAU,GAAG;AACrD,gBAAI,MAAM;AACR;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,sBAAsB,CAAC,SAAS,SAAS;AACvC,YAAQ,SAAS;AAAA,MACf,KAAK,iBAAiB;AACpB,cAAM,CAAC,KAAK,IAAI,KAAK,kBAAkB,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,IAAI;AAC3D,eAAO;AAAA,MACT;AAAA,MACA,KAAK,gBAAgB;AACnB,cAAM,CAAC,IAAI,IAAI,KAAK;AAAA,UAClB,EAAE,GAAG,GAAG,GAAG,GAAG,SAAS,KAAK;AAAA,UAC5B;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA;AAAA,MAEA,SAAS;AACP,cAAM,CAAC,KAAK,IAAI,KAAK,kBAAkB,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,IAAI;AAC3D,YAAI,UAAU,MAAM;AAClB,gBAAM,CAAC,IAAI,IAAI,KAAK;AAAA,YAClB,EAAE,GAAG,GAAG,GAAG,GAAG,SAAS,KAAK;AAAA,YAC5B;AAAA,UACF;AACA,cAAI,SAAS,MAAM;AACjB,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,4BAA4B,CAAC,SAAS,SAAS;AAC7C,QAAI,SAAS,KAAK,OAAO;AACvB,aAAO;AAAA,IACT;AACA,UAAM,EAAE,WAAW,IAAI;AACvB,YAAQ,SAAS;AAAA,MACf,KAAK,eAAe;AAClB,eAAO,SAAS,WAAW;AAAA,MAC7B;AAAA,MACA,KAAK,cAAc;AACjB,eAAO,SAAS,WAAW;AAAA,MAC7B;AAAA;AAAA,MAEA,SAAS;AACP,eACE,SAAS,WAAW,qBACpB,SAAS,WAAW;AAAA,MAExB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,mBAAmB,CAAC,SAAS,SAAS;AACpC,UAAM,EAAE,UAAU,IAAI;AACtB,UAAM,iBACH,cAAc,OAAO,cAAc,WAAW,KAAK,aAAa,MAAM;AACzE,QAAI,CAAC,eAAe;AAClB,aAAO;AAAA,IACT;AACA,YAAQ,SAAS;AAAA,MACf,KAAK,cAAc;AACjB,cAAM,EAAE,MAAM,QAAQ,SAAS,IAAI,KAAK;AACxC,cAAM,UAAU,IAAI,IAAI,KAAK,aAAa,MAAM,GAAG,IAAI;AACvD,eAAO,QAAQ,WAAW,UAAU,QAAQ,aAAa;AAAA,MAC3D;AAAA,MACA,KAAK,WAAW;AAEd,eAAO;AAAA,MACT;AAAA;AAAA,MAEA,SAAS;AACP,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,oBAAoB,CAAC,SAAS,SAAS;AACrC,YAAQ,SAAS;AAAA,MACf,KAAK,iBAAiB;AACpB,YAAI,SAAS,KAAK,UAAU,iBAAiB,gBAAgB,IAAI,GAAG;AAClE,cAAI,OAAO;AACX,cAAI,eAAe,IAAI,GAAG;AACxB,mBAAO;AAAA,UACT,WAAW,KAAK,QAAQ;AACtB,kBAAM,EAAE,eAAe,QAAQ,YAAY,IAAI,KAAK;AACpD,gBAAI,gBAAgB,MAAM;AACxB,kBAAI,eAAe,aAAa,GAAG;AACjC,uBAAO;AAAA,cACT,WAAW,KAAK,QAAQ;AACtB,sBAAM;AAAA,kBACJ,KAAK;AAAA,kBACL,QAAQ;AAAA,kBACR,MAAM;AAAA,gBACR,IAAI,KAAK;AAET,oBAAI,gBAAgB,eAAe;AACjC,sBAAI,KAAK,sBAAsB,MAAM;AACnC,2BAAO;AAAA,kBACT,WAAW,gBAAgB,KAAK,mBAAmB;AACjD,2BAAO;AAAA,kBACT;AAAA,gBACF,WAAW,aAAa,OAAO;AAC7B,sBACG,cAAc,aAAa,gBAAgB,QAC3C,cAAc,WAAW,gBAAgB,MAC1C;AACA,wBAAI,gBAAgB,aAAa;AAC/B,0BAAI,KAAK,sBAAsB,MAAM;AACnC,+BAAO;AAAA,sBACT,WACE,gBAAgB,KAAK,qBACrB,kBAAkB,MAClB;AACA,+BAAO;AAAA,sBACT;AAAA,oBACF,OAAO;AACL,6BAAO;AAAA,oBACT;AAAA,kBACF;AAAA,gBACF,WAAW,UAAU;AACnB,uBACG,cAAc,aAAa,cAAc,YAC1C,gBAAgB,MAChB;AACA,2BAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF,WACE,kBAAkB,QAClB,kBAAkB,KAAK,mBACvB;AACA,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,cAAI,MAAM;AACR,iBAAK,oBAAoB;AACzB,mBAAO;AAAA,UACT,WAAW,KAAK,sBAAsB,MAAM;AAC1C,iBAAK,oBAAoB;AAAA,UAC3B;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MACA,KAAK,gBAAgB;AACnB,YAAI,UAAU,KAAK,UAAU;AAC7B,YAAI,gBAAgB,OAAO,GAAG;AAC5B,iBAAO,SAAS;AACd,gBAAI,YAAY,MAAM;AACpB,qBAAO;AAAA,YACT;AACA,sBAAU,QAAQ;AAAA,UACpB;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA;AAAA,MAEA,SAAS;AACP,eAAO,SAAS,KAAK,UAAU,iBAAiB,gBAAgB,IAAI;AAAA,MACtE;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,0BAA0B,CAAC,SAAS,SAAS;AAC3C,UAAM,EAAE,QAAQ,MAAM,QAAQ,IAAI,KAAK,UAAU,CAAC;AAClD,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AACA,QAAI,YAAY,SAAS;AACvB,aACE,oCAAoC,KAAK,IAAI,KAAK,KAAK,SAAS,MAAM;AAAA,IAE1E;AACA,WAAO,SAAS,eAAe,UAAU,UAAU,KAAK,SAAS,MAAM;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,wBAAwB,CAAC,QAAQ,MAAM,QAAQ;AAC7C,QAAI,eAAe,oBAAI,IAAI,CAAC,IAAI,CAAC;AAEjC,aAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,YAAM,OAAO,OAAO,CAAC;AACrB,YAAM,eAAe,oBAAI,IAAI;AAC7B,UAAI,MAAM;AACV,iBAAW,eAAe,cAAc;AACtC,cAAM,SAAS,KAAK,iBAAiB,MAAM,aAAa,GAAG;AAC3D,YAAI,OAAO,MAAM;AACf,gBAAM,YAAY,CAAC,GAAG,MAAM;AAC5B,uBAAa,IAAI,GAAG,SAAS;AAAA,QAC/B;AAAA,MACF;AACA,UAAI,aAAa,SAAS,GAAG;AAC3B,eAAO;AAAA,MACT;AACA,qBAAe;AAAA,IACjB;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,qBAAqB,CAAC,SAAS,MAAM,QAAQ;AAC3C,UAAM,EAAE,SAAS,IAAI;AACrB,QAAI,OAAO;AACX,UAAM,IAAI,SAAS;AACnB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,SAAS,SAAS,CAAC;AACzB,aAAO,KAAK,oBAAoB,QAAQ,MAAM,GAAG;AACjD,UAAI,MAAM;AACR;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,SACG,IAAI,gBAAgB,KAAK,YAC1B,KAAK,aAAa,wBAClB;AACA,aAAO,KAAK,oBAAoB,OAAO;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,4BAA4B,CAAC,SAAS,MAAM,QAAQ;AAClD,UAAM,EAAE,SAAS,UAAU,aAAa,IAAI;AAC5C,UAAM,gBACH,IAAI,gBAAgB,KAAK,YAC1B,KAAK,aAAa;AACpB,QAAI,gBAAgB,CAAC,0BAA0B,SAAS,QAAQ,GAAG;AACjE,aAAO;AAAA,IACT;AACA,QAAI,UAAU,YAAY,QAAQ,YAAY;AAC9C,QAAI,gBAAgB;AACpB,UAAM,IAAI,aAAa;AACvB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,SAAS,aAAa,CAAC;AAC7B,YAAM,EAAE,QAAQ,eAAe,IAAI,OAAO,OAAO,SAAS,CAAC;AAC3D,YAAM,eAAe,KAAK,aAAa,gBAAgB,MAAM,GAAG;AAChE,UAAI,CAAC,cAAc;AACjB,wBAAgB;AAAA,MAClB,WAAW,OAAO,WAAW,GAAG;AAC9B,wBAAgB;AAChB;AAAA,MACF,OAAO;AAEL,wBAAgB,KAAK,sBAAsB,QAAQ,MAAM,GAAG;AAC5D,YAAI,eAAe;AACjB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,YAAY,OAAO;AACrB,aAAO,gBAAgB,OAAO;AAAA,IAChC;AACA,WAAO,gBAAgB,OAAO;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,0BAA0B,CAAC,SAAS,MAAM,MAAM,CAAC,MAAM;AACrD,UAAM,EAAE,QAAQ,IAAI;AAEpB,QAAI,YAAY,OAAO;AACrB,aAAO,KAAK,mBAAmB,SAAS,MAAM,GAAG;AAAA,IACnD;AAEA,WAAO,KAAK,0BAA0B,SAAS,MAAM,GAAG;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,mBAAmB,CAAC,SAAS,SAAS;AACpC,UAAM,EAAE,UAAU,IAAI;AACtB,YAAQ,SAAS;AAAA,MACf,KAAK,WAAW;AACd,cAAM,WAAW,KAAK,aAAa,MAAM;AACzC,YACE,KAAK,WACL,cAAc,YACb,aAAa,cAAc,aAAa,UACzC;AACA,iBAAO;AAAA,QACT,WAAW,KAAK,YAAY,cAAc,UAAU;AAClD,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MACA,KAAK,WAAW;AAEd,cAAM,WAAW,KAAK,aAAa,MAAM;AACzC,YACG,cAAc,YACb,EAAE,KAAK,aAAa,MAAM,KAAK,gBAAgB,IAAI,QAAQ,MAC5D,cAAc,WACb,KAAK,aAAa,MAAM,KACxB,iBAAiB,IAAI,QAAQ,GAC/B;AACA,cAAI,OAAO,KAAK;AAChB,iBAAO,MAAM;AACX,gBAAI,KAAK,cAAc,QAAQ;AAC7B;AAAA,YACF;AACA,mBAAO,KAAK;AAAA,UACd;AACA,cAAI,MAAM;AACR,kBAAM,SAAS,KAAK,kBAAkB,IAAI;AAC1C,gBAAI,UAAU,aAAa,MAAM,MAAM;AACvC,sBAAU,OAAO,WAAW;AAC5B,mBAAO,SAAS;AACd,oBAAM,WAAW,QAAQ;AACzB,oBAAM,eAAe,QAAQ,aAAa,MAAM;AAChD,kBAAI;AACJ,kBAAI,aAAa,UAAU;AACzB,oBAAI,EACF,QAAQ,aAAa,MAAM,KAC3B,gBAAgB,IAAI,YAAY;AAAA,cAEpC,WAAW,aAAa,SAAS;AAC/B,oBACE,QAAQ,aAAa,MAAM,KAC3B,iBAAiB,IAAI,YAAY;AAAA,cACrC;AACA,kBAAI,GAAG;AACL,uBAAO,YAAY;AAAA,cACrB;AACA,wBAAU,OAAO,SAAS;AAAA,YAC5B;AAAA,UACF;AAAA,QAEF,WACE,cAAc,WACd,KAAK,aAAa,MAAM,KACxB,gBAAgB,IAAI,QAAQ,KAC5B,KAAK,aAAa,SAAS,GAC3B;AACA,iBAAO;AAAA,QAET,WAAW,cAAc,YAAY,KAAK,aAAa,UAAU,GAAG;AAClE,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MACA,KAAK;AAAA,MACL,KAAK,WAAW;AACd,eAAO,2BAA2B,SAAS,IAAI;AAAA,MACjD;AAAA,MACA,KAAK;AAAA,MACL,KAAK,gBAAgB;AACnB,cAAM,WAAW,KAAK,aAAa,MAAM;AACzC,YACE,cAAc,WACd,EAAE,KAAK,YAAY,KAAK,aAAa,UAAU,MAC/C,EAAE,KAAK,YAAY,KAAK,aAAa,UAAU,MAC/C,gBAAgB,IAAI,QAAQ,GAC5B;AACA,gBAAM,SACJ,KAAK,SAAS,kBAAkB,KAAK,SAAS;AAChD,cAAI,YAAY,kBAAkB,QAAQ;AACxC,mBAAO;AAAA,UACT,WACE,YAAY,cACZ,CAAC,WACA,KAAK,aAAa,KAAK,KACtB,KAAK,aAAa,KAAK,KACvB,aAAa,UACf;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MACA,KAAK,iBAAiB;AACpB,YACG,KAAK,iBACJ,cAAc,WACd,KAAK,SAAS,cACf,cAAc,cAAc,CAAC,KAAK,aAAa,OAAO,GACvD;AACA,iBAAO;AAAA,QACT,WACE,cAAc,WACd,KAAK,SAAS,WACd,CAAC,KAAK,aAAa,SAAS,GAC5B;AACA,gBAAM,WAAW,KAAK;AACtB,cAAI,SAAS,KAAK;AAClB,iBAAO,QAAQ;AACb,gBAAI,OAAO,cAAc,QAAQ;AAC/B;AAAA,YACF;AACA,qBAAS,OAAO;AAAA,UAClB;AACA,cAAI,CAAC,QAAQ;AACX,qBAAS,KAAK,UAAU;AAAA,UAC1B;AACA,gBAAM,SAAS,KAAK,kBAAkB,MAAM;AAC5C,cAAI,UAAU,aAAa,QAAQ,MAAM;AACzC,oBAAU,OAAO,WAAW;AAC5B,cAAI;AACJ,iBAAO,SAAS;AACd,gBACE,QAAQ,cAAc,WACtB,QAAQ,aAAa,MAAM,MAAM,SACjC;AACA,kBAAI,QAAQ,aAAa,MAAM,GAAG;AAChC,oBAAI,QAAQ,aAAa,MAAM,MAAM,UAAU;AAC7C,4BAAU,CAAC,CAAC,QAAQ;AAAA,gBACtB;AAAA,cACF,OAAO;AACL,0BAAU,CAAC,CAAC,QAAQ;AAAA,cACtB;AACA,kBAAI,SAAS;AACX;AAAA,cACF;AAAA,YACF;AACA,sBAAU,OAAO,SAAS;AAAA,UAC5B;AACA,iBAAO,CAAC;AAAA,QACV;AACA,eAAO;AAAA,MACT;AAAA,MACA,KAAK;AAAA,MACL,KAAK,SAAS;AACZ,YAAI,kBAAkB,IAAI,SAAS,GAAG;AACpC,cAAI;AACJ,cAAI,KAAK,cAAc,GAAG;AACxB,gBAAI,KAAK,aAAa,GAAG;AACvB,kBAAI,KAAK,aAAa,KAAK,MAAM,QAAQ;AACvC,wBAAQ;AAAA,cACV;AAAA,YACF,OAAO;AACL,sBAAQ;AAAA,YACV;AAAA,UACF;AACA,cAAI,OAAO;AACT,mBAAO,YAAY;AAAA,UACrB;AACA,iBAAO,YAAY;AAAA,QACrB,WAAW,cAAc,YAAY;AACnC,gBAAM,SAAS,KAAK,kBAAkB,IAAI;AAC1C,cAAI,UAAU,aAAa,MAAM,MAAM;AACvC,oBAAU,OAAO,WAAW;AAC5B,cAAI;AACJ,cAAI,CAAC,SAAS;AACZ,oBAAQ;AAAA,UACV,OAAO;AACL,mBAAO,SAAS;AACd,kBAAI,kBAAkB,IAAI,QAAQ,SAAS,GAAG;AAC5C,oBAAI,QAAQ,cAAc,GAAG;AAC3B,sBAAI,QAAQ,aAAa,GAAG;AAC1B,4BAAQ,QAAQ,aAAa,QAAQ,MAAM;AAAA,kBAC7C,OAAO;AACL,4BAAQ;AAAA,kBACV;AAAA,gBACF,OAAO;AACL,0BAAQ;AAAA,gBACV;AACA,oBAAI,CAAC,OAAO;AACV;AAAA,gBACF;AAAA,cACF;AACA,wBAAU,OAAO,SAAS;AAAA,YAC5B;AAAA,UACF;AACA,cAAI,OAAO;AACT,mBAAO,YAAY;AAAA,UACrB;AACA,iBAAO,YAAY;AAAA,QACrB;AACA,eAAO;AAAA,MACT;AAAA,MACA,KAAK;AAAA,MACL,KAAK,YAAY;AACf,YAAI;AACJ,YAAI;AACJ,YAAI,cAAc,YAAY,cAAc,YAAY;AACtD,cAAI,KAAK,YAAY,KAAK,aAAa,UAAU,GAAG;AAClD,uBAAW;AAAA,UACb,OAAO;AACL,uBAAW;AAAA,UACb;AAAA,QACF,WAAW,cAAc,SAAS;AAChC,cAAI,KAAK,aAAa,MAAM,GAAG;AAC7B,kBAAM,WAAW,KAAK,aAAa,MAAM;AACzC,gBAAI,mBAAmB,IAAI,QAAQ,GAAG;AACpC,kBAAI,KAAK,YAAY,KAAK,aAAa,UAAU,GAAG;AAClD,2BAAW;AAAA,cACb,OAAO;AACL,2BAAW;AAAA,cACb;AAAA,YACF,OAAO;AACL,yBAAW;AAAA,YACb;AAAA,UACF,WAAW,KAAK,YAAY,KAAK,aAAa,UAAU,GAAG;AACzD,uBAAW;AAAA,UACb,OAAO;AACL,uBAAW;AAAA,UACb;AAAA,QACF;AACA,YAAI,YAAY,cAAc,UAAU;AACtC,iBAAO;AAAA,QACT,WAAW,YAAY,cAAc,UAAU;AAC7C,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MACA,KAAK,qBAAqB;AACxB,YAAI;AACJ,YAAI,KAAK,aAAa;AACpB,wBAAc,KAAK;AAAA,QACrB,WAAW,KAAK,aAAa,aAAa,GAAG;AAC3C,wBAAc,KAAK,aAAa,aAAa;AAAA,QAC/C;AACA,YAAI,OAAO,gBAAgB,YAAY,CAAC,SAAS,KAAK,WAAW,GAAG;AAClE,cAAI;AACJ,cAAI,cAAc,YAAY;AAC5B,yBAAa;AAAA,UACf,WAAW,cAAc,SAAS;AAChC,gBAAI,KAAK,aAAa,MAAM,GAAG;AAC7B,kBAAI,sBAAsB,IAAI,KAAK,aAAa,MAAM,CAAC,GAAG;AACxD,6BAAa;AAAA,cACf;AAAA,YACF,OAAO;AACL,2BAAa;AAAA,YACf;AAAA,UACF;AACA,cAAI,cAAc,KAAK,UAAU,IAAI;AACnC,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MACA,KAAK;AAAA,MACL,KAAK,cAAc;AACjB,eAAO,yBAAyB,SAAS,IAAI;AAAA,MAC/C;AAAA,MACA,SAAS;AACP,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,sBAAsB,CAAC,SAAS,SAAS;AACvC,UAAM,EAAE,UAAU,IAAI;AACtB,YAAQ,SAAS;AAAA,MACf,KAAK,gBAAgB;AACnB,eAAO,KAAK,WAAW,UAAU,IAAI;AAAA,MACvC;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,SAAS;AACP,YAAI,cAAc,aAAa,cAAc,UAAU;AACrD,cAAI,KAAK,aAAa,MAAM,GAAG;AAC7B,mBAAO,YAAY;AAAA,UACrB;AACA,iBAAO,YAAY;AAAA,QACrB;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,4BAA4B,CAAC,KAAK,MAAM,MAAM,CAAC,MAAM;AACnD,UAAM,EAAE,UAAU,aAAa,MAAM,QAAQ,IAAI;AACjD,UAAM,EAAE,UAAU,IAAI;AACtB,UAAM,EAAE,SAAS,OAAO,KAAK,MAAM,IAAI;AACvC,UAAM,UAAU,oBAAI,IAAI;AAExB,QAAI,MAAM,QAAQ,WAAW,KAAK,YAAY,IAAI,OAAO,GAAG;AAC1D,UAAI,CAAC,YAAY,UAAU,YAAY,QAAQ,YAAY,SAAS;AAClE,cAAM,UAAM,0BAAY,GAAG;AAC3B,cAAM,MAAM,oBAAoB,GAAG;AACnC,eAAO,KAAK,QAAQ,kBAAkB,KAAK,YAAY,KAAK,OAAO,CAAC;AAAA,MACtE;AACA,UAAI;AACJ,UAAI,KAAK,UAAU,IAAI,GAAG,GAAG;AAC3B,kBAAU,KAAK,UAAU,IAAI,GAAG;AAAA,MAClC,OAAO;AACL,cAAM,EAAE,SAAS,IAAI,QAAQ,GAAG;AAChC,YAAI,YAAY,OAAO;AAErB,cAAI,WAAW;AACf,gBAAM,IAAI,YAAY;AACtB,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,kBAAM,QAAQ,YAAY,CAAC;AAC3B,kBAAM,WAAO,sBAAQ,OAAO,wBAAwB;AACpD,gBAAI,MAAM;AACR,oBAAM,WAAW,KAAK;AACtB,kBAAI,aAAa,QAAQ,aAAa,SAAS;AAC7C,2BAAW;AACX;AAAA,cACF,OAAO;AACL,sBAAM,UAAM,0BAAY,GAAG;AAC3B,sBAAM,MAAM,oBAAoB,GAAG;AACnC,uBAAO,KAAK;AAAA,kBACV,kBAAkB,KAAK,YAAY,KAAK,OAAO;AAAA,gBACjD;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,UAAU;AACZ,mBAAO;AAAA,UACT;AACA,oBAAU;AAAA,YACR;AAAA,YACA;AAAA,UACF;AAAA,QACF,OAAO;AACL,gBAAM,eAAe,CAAC;AACtB,gBAAM,IAAI,SAAS;AACnB,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,kBAAM,CAAC,GAAG,MAAM,IAAI,SAAS,CAAC;AAC9B,kBAAM,SAAS,CAAC;AAChB,kBAAM,YAAY,oBAAI,IAAI;AAC1B,gBAAI,OAAO,OAAO,MAAM;AACxB,mBAAO,MAAM;AACX,kBAAI,KAAK,SAAS,YAAY;AAC5B,uBAAO,KAAK;AAAA,kBACV,OAAO;AAAA,kBACP,QAAQ,CAAC,GAAG,SAAS;AAAA,gBACvB,CAAC;AACD,0BAAU,MAAM;AAAA,cAClB,WAAW,MAAM;AACf,0BAAU,IAAI,IAAI;AAAA,cACpB;AACA,kBAAI,OAAO,QAAQ;AACjB,uBAAO,OAAO,MAAM;AAAA,cACtB,OAAO;AACL,uBAAO,KAAK;AAAA,kBACV,OAAO;AAAA,kBACP,QAAQ,CAAC,GAAG,SAAS;AAAA,gBACvB,CAAC;AACD,0BAAU,MAAM;AAChB;AAAA,cACF;AAAA,YACF;AACA,yBAAa,KAAK,MAAM;AAAA,UAC1B;AACA,oBAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,eAAK,UAAU,IAAI,KAAK,OAAO;AAAA,QACjC;AAAA,MACF;AACA,YAAM,MAAM,KAAK,wBAAwB,SAAS,MAAM,GAAG;AAC3D,UAAI,KAAK;AACP,gBAAQ,IAAI,GAAG;AAAA,MACjB;AAAA,IACF,WAAW,MAAM,QAAQ,WAAW,GAAG;AAErC,UAAI,oCAAoC,KAAK,OAAO,GAAG;AACrD,YAAI,YAAY,WAAW,GAAG;AAC5B,gBAAM,UAAM,0BAAY,GAAG;AAC3B,gBAAM,MAAM,oBAAoB,GAAG;AACnC,iBAAO,KAAK,QAAQ,kBAAkB,KAAK,YAAY,KAAK,OAAO,CAAC;AAAA,QACtE;AACA,cAAM,CAAC,MAAM,IAAI;AACjB,cAAM,QAAQ,KAAK,cAAc,QAAQ,MAAM,SAAS,GAAG;AAC3D,eAAO;AAAA,MACT,OAAO;AACL,gBAAQ,SAAS;AAAA,UACf,KAAK,OAAO;AACV,gBAAI,YAAY,WAAW,GAAG;AAC5B,oBAAM,UAAM,0BAAY,GAAG;AAC3B,oBAAM,MAAM,oBAAoB,GAAG;AACnC,qBAAO,KAAK;AAAA,gBACV,kBAAkB,KAAK,YAAY,KAAK,OAAO;AAAA,cACjD;AAAA,YACF;AACA,kBAAM,CAAC,QAAQ,IAAI;AACnB,kBAAM,MAAM,0BAA0B,UAAU,IAAI;AACpD,gBAAI,KAAK;AACP,sBAAQ,IAAI,IAAI;AAAA,YAClB;AACA;AAAA,UACF;AAAA,UACA,KAAK,QAAQ;AACX,gBAAI,CAAC,YAAY,QAAQ;AACvB,oBAAM,UAAM,0BAAY,GAAG;AAC3B,oBAAM,MAAM,oBAAoB,GAAG;AACnC,qBAAO,KAAK;AAAA,gBACV,kBAAkB,KAAK,YAAY,KAAK,OAAO;AAAA,cACjD;AAAA,YACF;AACA,gBAAI;AACJ,kBAAM,IAAI,YAAY;AACtB,qBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,oBAAM,WAAW,YAAY,CAAC;AAC9B,qBAAO,yBAAyB,UAAU,IAAI;AAC9C,kBAAI,MAAM;AACR;AAAA,cACF;AAAA,YACF;AACA,gBAAI,MAAM;AACR,sBAAQ,IAAI,IAAI;AAAA,YAClB;AACA;AAAA,UACF;AAAA,UACA,KAAK,SAAS;AACZ,gBAAI,gBAAgB,IAAI,GAAG;AACzB,oBAAM,CAAC,EAAE,OAAO,WAAW,CAAC,IAAI;AAChC,kBAAI,YAAY;AACd,oBAAI,KAAK,UAAU,GAAG;AACpB,0BAAQ,IAAI,IAAI;AAAA,gBAClB,OAAO;AACL,6BAAW,KAAK,MAAM;AACpB,0BAAM,OAAO,KAAK,CAAC;AACnB,wBAAI,gBAAgB,KAAK,QAAQ,kBAAkB;AACjD,0BAAI,MAAM,QAAQ,IAAI,UAAU,GAAG;AACjC,gCAAQ,IAAI,IAAI;AAAA,sBAClB;AACA;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,gBAAgB;AACnB,gBAAI,MAAM;AACR,oBAAM,MAAM,6BAA6B,OAAO;AAChD,mBAAK;AAAA,gBACH,kBAAkB,KAAK,mBAAmB,KAAK,OAAO;AAAA,cACxD;AAAA,YACF;AACA;AAAA,UACF;AAAA;AAAA,UAEA,KAAK;AAAA,UACL,KAAK,gBAAgB;AACnB;AAAA,UACF;AAAA;AAAA,UAEA,KAAK,YAAY;AACf,gBAAI,MAAM;AACR,oBAAM,MAAM,yBAAyB,OAAO;AAC5C,mBAAK;AAAA,gBACH,kBAAkB,KAAK,mBAAmB,KAAK,OAAO;AAAA,cACxD;AAAA,YACF;AACA;AAAA,UACF;AAAA,UACA,SAAS;AACP,gBAAI,CAAC,SAAS;AACZ,oBAAM,MAAM,yBAAyB,OAAO;AAC5C,mBAAK,QAAQ,kBAAkB,KAAK,YAAY,KAAK,OAAO,CAAC;AAAA,YAC/D;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AACL,UAAI,UAAU;AACd,UAAI,YAAY,IAAI,OAAO,GAAG;AAC5B,kBAAU,KAAK,iBAAiB,SAAS,IAAI;AAAA,MAC/C,WAAW,UAAU,IAAI,OAAO,GAAG;AACjC,kBAAU,KAAK,oBAAoB,SAAS,IAAI;AAAA,MAClD,OAAO;AACL,gBAAQ,SAAS;AAAA,UACf,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,WAAW;AACd,gBAAI,KAAK,iBAAiB,SAAS,IAAI,GAAG;AACxC,wBAAU;AAAA,YACZ;AACA;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK,UAAU;AACb,gBAAI,KAAK,wBAAwB,SAAS,IAAI,GAAG;AAC/C,wBAAU;AAAA,YACZ;AACA;AAAA,UACF;AAAA,UACA,KAAK,UAAU;AACb,kBAAM,EAAE,KAAK,IAAI,KAAK;AACtB,gBACE,KAAK,MACL,SAAS,IAAI,KAAK,EAAE,MACpB,KAAK,UAAU,SAAS,IAAI,GAC5B;AACA,wBAAU;AAAA,YACZ;AACA;AAAA,UACF;AAAA,UACA,KAAK,iBAAiB;AACpB,kBAAM,EAAE,KAAK,IAAI,KAAK;AACtB,gBAAI,MAAM;AACR,oBAAM,KAAK,KAAK,QAAQ,MAAM,EAAE;AAChC,kBAAI,UAAU,KAAK,UAAU,eAAe,EAAE;AAC9C,qBAAO,SAAS;AACd,oBAAI,YAAY,MAAM;AACpB,4BAAU;AACV;AAAA,gBACF;AACA,0BAAU,QAAQ;AAAA,cACpB;AAAA,YACF;AACA;AAAA,UACF;AAAA,UACA,KAAK,SAAS;AACZ,gBAAI,KAAK,MAAM,aAAa,cAAc;AACxC,kBAAI,CAAC,KAAK,WAAW,SAAS,KAAK,OAAO;AACxC,0BAAU;AAAA,cACZ;AAAA,YACF,WAAW,SAAS,KAAK,UAAU,iBAAiB;AAClD,wBAAU;AAAA,YACZ;AACA;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,gBAAgB;AACnB,gBAAI,KAAK,kBAAkB,SAAS,IAAI,GAAG;AACzC,wBAAU;AAAA,YACZ;AACA;AAAA,UACF;AAAA,UACA,KAAK,QAAQ;AACX,gBAAI,SAAS,KAAK,UAAU,iBAAiB;AAC3C,wBAAU;AAAA,YACZ;AACA;AAAA,UACF;AAAA,UACA,KAAK,SAAS;AACZ,gBAAI,KAAK,cAAc,GAAG;AACxB,oBAAM,SAAS,KAAK,kBAAkB,MAAM;AAAA,gBAC1C,OAAO;AAAA,gBACP,YAAY;AAAA,cACd,CAAC;AACD,kBAAI,UAAU,OAAO,WAAW;AAChC,kBAAI;AACJ,qBAAO,SAAS;AACd,uBACE,QAAQ,aAAa,gBACrB,QAAQ,aAAa;AACvB,oBAAI,CAAC,MAAM;AACT;AAAA,gBACF;AACA,0BAAU,OAAO,YAAY;AAAA,cAC/B;AACA,kBAAI,MAAM;AACR,0BAAU;AAAA,cACZ;AAAA,YACF,OAAO;AACL,wBAAU;AAAA,YACZ;AACA;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,cAAc;AACjB,gBAAI,KAAK,0BAA0B,SAAS,IAAI,GAAG;AACjD,wBAAU;AAAA,YACZ;AACA;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,gBAAgB;AACnB,gBAAI,SAAS,KAAK,OAAO;AACvB,sBAAQ,IAAI,IAAI;AAAA,YAClB,OAAO;AACL,oBAAM,cAAc,KAAK,oBAAoB,SAAS,IAAI;AAC1D,kBAAI,aAAa;AACf,wBAAQ,IAAI,WAAW;AAAA,cACzB;AAAA,YACF;AACA;AAAA,UACF;AAAA,UACA,KAAK,WAAW;AACd,gBAAI,KAAK,aAAa,IAAI,KAAK,UAAU,SAAS,GAAG,GAAG;AACtD,kBAAI,gBAAgB,IAAI,GAAG;AACzB,0BAAU;AAAA,cACZ;AAAA,YAEF,WACE,gBAAgB,KAAK,QAAQ,eAC7B,gBAAgB,KAAK,QAAQ,YAC7B;AACA,wBAAU;AAAA,YACZ;AACA;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK,gBAAgB;AAEnB;AAAA,UACF;AAAA;AAAA,UAEA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,cAAc;AACjB,gBAAI,MAAM;AACR,oBAAM,MAAM,gCAAgC,OAAO;AACnD,mBAAK;AAAA,gBACH,kBAAkB,KAAK,mBAAmB,KAAK,OAAO;AAAA,cACxD;AAAA,YACF;AACA;AAAA,UACF;AAAA;AAAA,UAEA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,oBAAoB;AACvB,gBAAI,MAAM;AACR,oBAAM,MAAM,6BAA6B,OAAO;AAChD,mBAAK;AAAA,gBACH,kBAAkB,KAAK,mBAAmB,KAAK,OAAO;AAAA,cACxD;AAAA,YACF;AACA;AAAA,UACF;AAAA,UACA,SAAS;AACP,gBAAI,QAAQ,WAAW,UAAU,GAAG;AAClC,kBAAI,MAAM;AACR,sBAAM,MAAM,6BAA6B,OAAO;AAChD,qBAAK;AAAA,kBACH,kBAAkB,KAAK,mBAAmB,KAAK,OAAO;AAAA,gBACxD;AAAA,cACF;AAAA,YACF,WAAW,CAAC,SAAS;AACnB,oBAAM,MAAM,yBAAyB,OAAO;AAC5C,mBAAK,QAAQ,kBAAkB,KAAK,YAAY,KAAK,OAAO,CAAC;AAAA,YAC/D;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,SAAS;AACX,gBAAQ,IAAI,IAAI;AAAA,MAClB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,sBAAsB,CAAC,QAAQ,MAAM,QAAQ;AAC3C,UAAM,IAAI,OAAO;AACjB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,OAAO,OAAO,CAAC;AACrB,UAAI,KAAK,SAAS,YAAY;AAC5B,cAAM,UAAM,0BAAY,GAAG;AAC3B,cAAM,MAAM,oBAAoB,GAAG;AACnC,aAAK,QAAQ,kBAAkB,KAAK,YAAY,KAAK,OAAO,CAAC;AAC7D,eAAO;AAAA,MACT;AACA,UAAI,CAAC,KAAK,eAAe,MAAM,IAAI,EAAE,IAAI,IAAI,GAAG;AAC9C,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,6BAA6B,CAAC,QAAQ,MAAM,QAAQ;AAClD,QAAI,SAAS;AACb,WAAO,QAAQ;AACb,UAAI;AACJ,YAAM,IAAI,OAAO;AACjB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAM,OAAO,OAAO,CAAC;AACrB,YAAI,KAAK,SAAS,YAAY;AAC5B,gBAAM,UAAM,0BAAY,GAAG;AAC3B,gBAAM,MAAM,oBAAoB,GAAG;AACnC,eAAK,QAAQ,kBAAkB,KAAK,YAAY,KAAK,OAAO,CAAC;AAC7D,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,eAAe,MAAM,MAAM,EAAE,IAAI,MAAM;AACnD,YAAI,CAAC,MAAM;AACT;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAM;AACR,eAAO;AAAA,MACT;AACA,eAAS,OAAO;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,8BAA8B,CAAC,KAAK,SAAS;AAC3C,UAAM,EAAE,UAAU,aAAa,MAAM,QAAQ,IAAI;AAEjD,QAAI,CAAC,MAAM,QAAQ,WAAW,GAAG;AAC/B,UAAI,YAAY,QAAQ;AACtB,eAAO;AAAA,MACT;AACA,YAAM,MAAM,qBAAqB,OAAO;AACxC,aAAO,KAAK,QAAQ,kBAAkB,KAAK,YAAY,KAAK,OAAO,CAAC;AAAA,IACtE;AAEA,QAAI,YAAY,UAAU,YAAY,gBAAgB;AACpD,YAAM,MAAM,qBAAqB,OAAO;AACxC,aAAO,KAAK,QAAQ,kBAAkB,KAAK,YAAY,KAAK,OAAO,CAAC;AAAA,IACtE;AACA,QAAI,YAAY,WAAW,GAAG;AAC5B,YAAM,UAAM,0BAAY,GAAG;AAC3B,YAAM,MAAM,oBAAoB,GAAG;AACnC,aAAO,KAAK,QAAQ,kBAAkB,KAAK,YAAY,KAAK,OAAO,CAAC;AAAA,IACtE;AACA,UAAM,EAAE,KAAK,IAAI;AACjB,UAAM,EAAE,SAAS,IAAI,QAAQ,YAAY,CAAC,CAAC;AAC3C,UAAM,CAAC,MAAM,IAAI;AACjB,UAAM,CAAC,GAAG,MAAM,IAAI;AACpB,QAAI,YAAY,QAAQ;AACtB,YAAMC,WAAU,KAAK,oBAAoB,QAAQ,MAAM,GAAG;AAC1D,aAAOA,WAAU,OAAO;AAAA,IAC1B;AAEA,UAAM,UAAU,KAAK,2BAA2B,QAAQ,MAAM,GAAG;AACjE,WAAO,UAAU,OAAO;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,2BAA2B,CAAC,KAAK,MAAM,MAAM,CAAC,MAAM;AAClD,UAAM,EAAE,MAAM,QAAQ,IAAI;AAC1B,UAAM,UAAU,iBAAiB,IAAI,IAAI;AACzC,UAAM,UAAU,oBAAI,IAAI;AACxB,YAAQ,SAAS;AAAA,MACf,KAAK,eAAe;AAClB,YAAI,uBAAuB,KAAK,MAAM,GAAG,GAAG;AAC1C,kBAAQ,IAAI,IAAI;AAAA,QAClB;AACA;AAAA,MACF;AAAA,MACA,KAAK,aAAa;AAChB,YAAI,KAAK,OAAO,SAAS;AACvB,kBAAQ,IAAI,IAAI;AAAA,QAClB;AACA;AAAA,MACF;AAAA,MACA,KAAK,gBAAgB;AACnB,YAAI,KAAK,UAAU,SAAS,OAAO,GAAG;AACpC,kBAAQ,IAAI,IAAI;AAAA,QAClB;AACA;AAAA,MACF;AAAA,MACA,KAAK,mBAAmB;AACtB,eAAO,KAAK,0BAA0B,KAAK,MAAM,GAAG;AAAA,MACtD;AAAA,MACA,KAAK,eAAe;AAClB,YAAI,kBAAkB,KAAK,MAAM,GAAG,GAAG;AACrC,kBAAQ,IAAI,IAAI;AAAA,QAClB;AACA;AAAA,MACF;AAAA,MACA,KAAK;AAAA,MACL,SAAS;AACP,YAAI;AACF,cAAI,IAAI,OAAO;AACb,kBAAM,UAAM,0BAAY,GAAG;AAC3B,iBAAK,eAAe,KAAK,GAAG;AAC5B,oBAAQ,IAAI,IAAI;AAAA,UAClB,OAAO;AACL,uCAA2B,SAAS,SAAS,GAAG;AAAA,UAClD;AAAA,QACF,SAAS,GAAG;AACV,eAAK,QAAQ,CAAC;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,8BAA8B,CAAC,KAAK,MAAM,MAAM,CAAC,MAAM;AACrD,UAAM,EAAE,MAAM,QAAQ,IAAI;AAC1B,UAAM,UAAU,oBAAI,IAAI;AACxB,QAAI,YAAY,IAAI,OAAO,GAAG;AAC5B,UAAI,eAAe;AACnB,aAAO,KAAK,0BAA0B,KAAK,MAAM,GAAG;AAAA,IACtD,WAAW,YAAY,UAAU,YAAY,gBAAgB;AAC3D,YAAM,MAAM,KAAK,4BAA4B,KAAK,MAAM,GAAG;AAC3D,UAAI,KAAK;AACP,aAAK,oBAAoB;AACzB,gBAAQ,IAAI,GAAG;AAAA,MACjB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,iBAAiB,CAAC,KAAK,MAAM,MAAM,CAAC,MAAM;AACxC,QAAI,KAAK,aAAa,cAAc;AAClC,aAAO,KAAK,yBAAyB,KAAK,MAAM,GAAG;AAAA,IACrD;AACA,QACE,KAAK,WACL,KAAK,aAAa,0BAClB,IAAI,SAAS,mBACb;AACA,aAAO,KAAK,4BAA4B,KAAK,MAAM,GAAG;AAAA,IACxD;AACA,WAAO,oBAAI,IAAI;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,CAAC,QAAQ,MAAM,MAAM,CAAC,MAAM;AACzC,UAAM,UAAU,KAAK,cAAc,KAAK,qBAAqB,KAAK;AAClE,QAAI,SAAS,QAAQ,IAAI,MAAM;AAC/B,QAAI,UAAU,OAAO,IAAI,IAAI,GAAG;AAC9B,YAAM,EAAE,QAAQ,IAAI,OAAO,IAAI,IAAI;AACnC,aAAO;AAAA,IACT;AACA,QAAI,YAAY;AAChB,QAAI,KAAK,aAAa,gBAAgB,SAAS,IAAI,KAAK,SAAS,GAAG;AAClE,kBAAY;AAAA,IACd;AACA,QAAI;AACJ,UAAM,IAAI,OAAO;AACjB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,OAAO,OAAO,CAAC;AACrB,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK;AAAA,QACL,KAAK,aAAa;AAChB,sBAAY;AACZ;AAAA,QACF;AAAA,QACA,KAAK,mBAAmB;AACtB,cAAI,eAAe,IAAI,KAAK,IAAI,GAAG;AACjC,wBAAY;AAAA,UACd;AACA;AAAA,QACF;AAAA,QACA,SAAS;AAAA,QAET;AAAA,MACF;AACA,aAAO,KAAK,eAAe,MAAM,MAAM,GAAG,EAAE,IAAI,IAAI;AACpD,UAAI,CAAC,MAAM;AACT;AAAA,MACF;AAAA,IACF;AACA,QAAI,WAAW;AACb,UAAI,CAAC,QAAQ;AACX,iBAAS,oBAAI,QAAQ;AAAA,MACvB;AACA,aAAO,IAAI,MAAM;AAAA,QACf,SAAS;AAAA,MACX,CAAC;AACD,cAAQ,IAAI,QAAQ,MAAM;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,0BAA0B,CAAC,UAAU,QAAQ,MAAM,CAAC,MAAM;AACxD,UAAM,SAAS,KAAK,kBAAkB,QAAQ;AAC9C,iBAAa,UAAU,MAAM;AAC7B,QAAI,cAAc,OAAO,WAAW;AACpC,UAAM,QAAQ,oBAAI,IAAI;AACtB,WAAO,aAAa;AAClB,UAAI,KAAK,aAAa,QAAQ,aAAa,GAAG,GAAG;AAC/C,cAAM,IAAI,WAAW;AAAA,MACvB;AACA,oBAAc,OAAO,SAAS;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,uBAAuB,CAAC,QAAQ,UAAU,MAAM,CAAC,MAAM;AACrD,UAAM,CAAC,MAAM,GAAG,YAAY,IAAI;AAChC,UAAM,EAAE,MAAM,SAAS,IAAI;AAC3B,YAAQ,UAAU;AAAA,MAChB,KAAK,aAAa;AAChB,cAAM,uBACJ,CAAC,KAAK,WACN,SAAS,aAAa,gBACtB,KAAK,MAAM,aAAa;AAC1B,YAAI,sBAAsB;AACxB,gBAAM,WAAW,iBAAiB,KAAK,IAAI;AAC3C,gBAAM,QAAQ,oBAAI,IAAI;AACtB,gBAAM,YAAY,KAAK,MAAM,eAAe,QAAQ;AACpD,cACE,aACA,cAAc,YACd,SAAS,SAAS,SAAS,GAC3B;AACA,kBAAM,qBAAqB,aAAa,SAAS;AACjD,gBACE,CAAC,sBACD,KAAK,aAAa,cAAc,WAAW,GAAG,GAC9C;AACA,oBAAM,IAAI,SAAS;AAAA,YACrB;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,KAAK,wBAAwB,UAAU,QAAQ,GAAG;AAAA,MAC3D;AAAA,MACA,KAAK,qBAAqB;AACxB,cAAM,WAAW,iBAAiB,KAAK,IAAI;AAC3C,mCAA2B,UAAU,UAAU,GAAG;AAClD,eAAO,oBAAI,IAAI;AAAA,MACjB;AAAA,MACA,SAAS;AACP,eAAO,KAAK,wBAAwB,UAAU,QAAQ,GAAG;AAAA,MAC3D;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,6BAA6B,CAAC,MAAM,MAAM,MAAM,CAAC,MAAM;AACrD,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,EAAE,WAAW,IAAI;AACvB,UAAM,EAAE,IAAI,IAAI;AAChB,QAAI,QAAQ,UAAU;AACpB,aAAO,KAAK,qBAAqB,QAAQ,MAAM,GAAG;AAAA,IACpD;AAEA,UAAM,YAAY,CAAC;AACnB,QAAI,UAAU;AACd,WAAO,SAAS;AACd,UAAI,KAAK,aAAa,QAAQ,SAAS,GAAG,GAAG;AAC3C,kBAAU,KAAK,OAAO;AAAA,MACxB;AACA,gBAAU,QAAQ;AAAA,IACpB;AACA,QAAI,UAAU,QAAQ;AAEpB,aAAO,IAAI,IAAI,UAAU,QAAQ,CAAC;AAAA,IACpC;AACA,WAAO,oBAAI,IAAI;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,wBAAwB,CAAC,MAAM,MAAM,MAAM,CAAC,MAAM;AAChD,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,EAAE,IAAI,IAAI;AAChB,UAAM,EAAE,WAAW,IAAI;AACvB,UAAM,UAAU,oBAAI,IAAI;AACxB,QAAI,QAAQ,UAAU;AACpB,UAAI,UAAU,KAAK;AACnB,aAAO,SAAS;AACd,YAAI,KAAK,aAAa,QAAQ,SAAS,GAAG,GAAG;AAC3C,kBAAQ,IAAI,OAAO;AAAA,QACrB;AACA,kBAAU,QAAQ;AAAA,MACpB;AAAA,IACF,OAAO;AAEL,UAAI,cAAc,KAAK,aAAa,QAAQ,YAAY,GAAG,GAAG;AAC5D,gBAAQ,IAAI,UAAU;AAAA,MACxB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,kCAAkC,CAAC,MAAM,MAAM,MAAM,CAAC,MAAM;AAC1D,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,EAAE,IAAI,IAAI;AAChB,UAAM,UAAU,oBAAI,IAAI;AACxB,UAAM,UACJ,QAAQ,WAAW,KAAK,qBAAqB,KAAK;AACpD,QAAI,WAAW,KAAK,aAAa,QAAQ,SAAS,GAAG,GAAG;AACtD,cAAQ,IAAI,OAAO;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,iCAAiC,CAAC,MAAM,MAAM,MAAM,CAAC,MAAM;AACzD,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,EAAE,IAAI,IAAI;AAChB,UAAM,UAAU,oBAAI,IAAI;AACxB,QAAI,UACF,QAAQ,WAAW,KAAK,qBAAqB,KAAK;AACpD,WAAO,SAAS;AACd,UAAI,KAAK,aAAa,QAAQ,SAAS,GAAG,GAAG;AAC3C,gBAAQ,IAAI,OAAO;AAAA,MACrB;AACA,gBACE,QAAQ,WACJ,QAAQ,qBACR,QAAQ;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,mBAAmB,CAAC,MAAM,MAAM,MAAM,CAAC,MAAM;AAC3C,UAAM;AAAA,MACJ,OAAO,EAAE,MAAM,UAAU;AAAA,IAC3B,IAAI;AACJ,YAAQ,WAAW;AAAA,MACjB,KAAK,KAAK;AACR,eAAO,KAAK,gCAAgC,MAAM,MAAM,GAAG;AAAA,MAC7D;AAAA,MACA,KAAK,KAAK;AACR,eAAO,KAAK,+BAA+B,MAAM,MAAM,GAAG;AAAA,MAC5D;AAAA,MACA,KAAK,KAAK;AACR,eAAO,KAAK,sBAAsB,MAAM,MAAM,GAAG;AAAA,MACnD;AAAA,MACA,KAAK;AAAA,MACL,SAAS;AACP,eAAO,KAAK,2BAA2B,MAAM,MAAM,GAAG;AAAA,MACxD;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,2BAA2B,CAAC,QAAQ,QAAQ,YAAY;AACtD,UAAM,EAAE,cAAc,OAAO,WAAW,WAAW,IAAI;AACvD,UAAM,iBAAiB,CAAC;AACxB,QAAI,cAAc,aAAa,WAAW,QAAQ,CAAC,CAAC,KAAK;AACzD,QAAI,CAAC,aAAa;AAChB,aAAO,CAAC;AAAA,IACV;AAEA,QAAI,YAAY,aAAa,cAAc;AACzC,oBAAc,OAAO,SAAS;AAAA,IAChC,WAAW,gBAAgB,aAAa,gBAAgB,KAAK,OAAO;AAClE,oBAAc,OAAO,SAAS;AAAA,IAChC;AACA,WAAO,aAAa;AAElB,UAAI,gBAAgB,gBAAgB,cAAc;AAChD;AAAA,MACF;AACA,UAAI,KAAK,aAAa,QAAQ,aAAa,EAAE,MAAM,KAAK,MAAM,CAAC,GAAG;AAChE,uBAAe,KAAK,WAAW;AAE/B,YAAI,eAAe,YAAY;AAC7B;AAAA,QACF;AAAA,MACF;AACA,oBAAc,OAAO,SAAS;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,eAAe,CAAC,QAAQ,MAAM,MAAM,CAAC,MAAM;AACzC,UAAM,EAAE,OAAO,WAAW,IAAI;AAC9B,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,cAAc,KAAK,kBAAkB,KAAK,KAAK;AAAA,IACtD;AACA,WAAO,KAAK,yBAAyB,KAAK,aAAa,QAAQ;AAAA,MAC7D;AAAA,MACA;AAAA,MACA,cAAc,KAAK;AAAA,MACnB,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,kBAAkB,CAAC,QAAQ,MAAM,MAAM,CAAC,MAAM;AAC5C,UAAM,EAAE,SAAS,GAAG,cAAc,IAAI;AACtC,QAAI,SAAS;AACX,YAAM,eAAe,KAAK,aAAa,QAAQ,KAAK,OAAO,GAAG;AAC9D,UAAI,aAAa,QAAQ;AACvB,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,cAAc,KAAK,kBAAkB,KAAK,KAAK;AAAA,IACtD;AACA,WAAO,KAAK,yBAAyB,KAAK,aAAa,QAAQ;AAAA,MAC7D,WAAW;AAAA,MACX,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,CAAC,QAAQ,QAAQ,UAAU;AACtC,UAAM,UAAU,EAAE,OAAO,MAAM,KAAK,MAAM;AAC1C,UAAM,UAAU,KAAK,aAAa,QAAQ,KAAK,OAAO,OAAO;AAC7D,UAAM,QAAQ,UAAU,CAAC,KAAK,KAAK,IAAI,CAAC;AACxC,WAAO,CAAC,OAAO,SAAS,KAAK,cAAc;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,CAAC,QAAQ,QAAQ;AAC7B,UAAM,EAAE,QAAQ,IAAI;AACpB,UAAM,QAAQ,CAAC;AACf,UAAM,UAAU,EAAE,MAAM,KAAK,MAAM;AACnC,UAAM,cAAc,KAAK,aAAa,QAAQ,KAAK,OAAO,OAAO;AACjE,QAAI,aAAa;AACf,YAAM,KAAK,KAAK,KAAK;AAAA,IACvB;AACA,QAAI,CAAC,eAAe,SAAS;AAC3B,UAAI,cAAc,KAAK,MAAM;AAC7B,aAAO,aAAa;AAClB,YAAI,KAAK,aAAa,QAAQ,aAAa,OAAO,GAAG;AACnD,gBAAM,KAAK,WAAW;AAAA,QACxB;AACA,sBAAc,YAAY;AAAA,MAC5B;AAAA,IACF;AACA,UAAM,WAAW,MAAM,SAAS;AAChC,WAAO,CAAC,OAAO,QAAQ;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,kCAAkC,CAAC,MAAM,cAAc,eAAe;AACpE,QAAI,QAAQ,CAAC;AACb,QAAI,WAAW;AACf,QAAI,eAAe,eAAe,KAAK,QAAQ;AAC7C,YAAM,UAAM,0BAAY,IAAI;AAC5B,WAAK,eAAe,KAAK,GAAG;AAC5B,UAAI,aAAa,QAAQ;AACvB,SAAC,OAAO,QAAQ,IAAI,KAAK,WAAW,cAAc,KAAK,MAAM;AAAA,MAC/D,OAAO;AACL,cAAM,KAAK,KAAK,KAAK;AACrB,mBAAW;AAAA,MACb;AAAA,IACF,OAAO;AACL,iCAA2B,KAAK,MAAM,KAAK,MAAM,EAAE,MAAM,KAAK,MAAM,CAAC;AAAA,IACvE;AACA,WAAO,EAAE,OAAO,UAAU,SAAS,MAAM;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,uBAAuB,CAAC,MAAM,YAAY,QAAQ;AAChD,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,CAAC,MAAM,GAAG,YAAY,IAAI;AAChC,UAAM,EAAE,SAAS,QAAQ,IAAI;AAC7B,QAAI,QAAQ,CAAC;AACb,QAAI,WAAW;AACf,QAAI,eAAe,aAAa;AAC9B,OAAC,OAAO,QAAQ,IAAI,KAAK,WAAW,MAAM;AAAA,IAC5C,WAAW,eAAe,eAAe;AACvC,OAAC,OAAO,QAAQ,IAAI,KAAK,YAAY,QAAQ,EAAE,QAAQ,CAAC;AAAA,IAC1D,WACE,eAAe,gBACf,KAAK,MAAM,aAAa,cACxB;AACA,YAAM,OAAO,KAAK,MAAM,eAAe,KAAK,IAAI;AAChD,UAAI,MAAM;AACR,YAAI,aAAa,QAAQ;AACvB,cAAI,KAAK,aAAa,cAAc,MAAM,EAAE,MAAM,KAAK,MAAM,CAAC,GAAG;AAC/D,kBAAM,KAAK,IAAI;AACf,uBAAW;AAAA,UACb;AAAA,QACF,OAAO;AACL,gBAAM,KAAK,IAAI;AACf,qBAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF,OAAO;AACL,cAAQ,KAAK,gBAAgB,QAAQ,KAAK,OAAO,EAAE,SAAS,WAAW,CAAC;AACxE,iBAAW,MAAM,SAAS;AAAA,IAC5B;AACA,WAAO,EAAE,OAAO,UAAU,SAAS,MAAM;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,0BAA0B,CAAC,QAAQ,YAAY,QAAQ;AACrD,UAAM,EAAE,SAAS,QAAQ,IAAI;AAC7B,QAAI,QAAQ,CAAC;AACb,QAAI,WAAW;AACf,QAAI,eAAe,aAAa;AAC9B,OAAC,OAAO,QAAQ,IAAI,KAAK,WAAW,MAAM;AAAA,IAC5C,WAAW,eAAe,eAAe;AACvC,OAAC,OAAO,QAAQ,IAAI,KAAK,YAAY,QAAQ,EAAE,QAAQ,CAAC;AAAA,IAC1D,OAAO;AACL,cAAQ,KAAK,gBAAgB,QAAQ,KAAK,OAAO,EAAE,SAAS,WAAW,CAAC;AACxE,iBAAW,MAAM,SAAS;AAAA,IAC5B;AACA,WAAO,EAAE,OAAO,UAAU,SAAS,MAAM;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,yBAAyB,CAAC,QAAQ,YAAY,QAAQ;AACpD,UAAM,EAAE,SAAS,QAAQ,IAAI;AAC7B,QAAI,QAAQ,CAAC;AACb,QAAI,WAAW;AACf,QAAI,eAAe,aAAa;AAC9B,OAAC,OAAO,QAAQ,IAAI,KAAK,WAAW,MAAM;AAAA,IAC5C,WAAW,eAAe,eAAe;AACvC,OAAC,OAAO,QAAQ,IAAI,KAAK,YAAY,QAAQ,EAAE,QAAQ,CAAC;AAAA,IAC1D,OAAO;AACL,cAAQ,KAAK,gBAAgB,QAAQ,KAAK,OAAO,EAAE,SAAS,WAAW,CAAC;AACxE,iBAAW,MAAM,SAAS;AAAA,IAC5B;AACA,WAAO,EAAE,OAAO,UAAU,SAAS,MAAM;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,0BAA0B,CAAC,MAAM,YAAY,QAAQ;AACnD,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,CAAC,MAAM,GAAG,YAAY,IAAI;AAChC,UAAM,EAAE,SAAS,QAAQ,IAAI;AAC7B,QAAI,QAAQ,CAAC;AACb,QAAI,WAAW;AACf,QAAI,UAAU;AACd,QAAI,eAAe,iBAAiB,oBAAoB,KAAK,KAAK,IAAI,GAAG;AACvE,UAAI,aAAa;AACjB,UAAI,KAAK,WAAW,KAAK,MAAM,aAAa,wBAAwB;AAClE,qBAAa,KAAK,4BAA4B,MAAM,KAAK,KAAK;AAAA,MAChE,WAAW,aAAa,UAAU,KAAK,MAAM,aAAa,cAAc;AACtE,qBAAa,KAAK;AAAA,UAChB;AAAA,UACA,KAAK,MAAM;AAAA,QACb;AAAA,MACF;AACA,UAAI,YAAY;AACd,YAAI,OAAO;AACX,cAAM,IAAI,aAAa;AACvB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAM,aAAa,aAAa,CAAC;AACjC,kBAAQ,WAAW,MAAM;AAAA,YACvB,KAAK;AAAA,YACL,KAAK,gBAAgB;AACnB,oBAAM,cAAc,KAAK;AAAA,gBACvB;AAAA,gBACA;AAAA,cACF;AACA,qBAAO,gBAAgB;AACvB;AAAA,YACF;AAAA,YACA,KAAK,OAAO;AACV,qBAAO,KAAK;AAAA,gBACV;AAAA,gBACA;AAAA,gBACA,CAAC;AAAA,cACH,EAAE,IAAI,UAAU;AAChB;AAAA,YACF;AAAA,YACA,SAAS;AACP,qBAAO;AAAA,YACT;AAAA,UACF;AACA,cAAI,CAAC,MAAM;AACT;AAAA,UACF;AAAA,QACF;AACA,YAAI,MAAM;AACR,gBAAM,KAAK,UAAU;AACrB,qBAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF,WAAW,eAAe,aAAa;AACrC,OAAC,OAAO,QAAQ,IAAI,KAAK,WAAW,MAAM;AAAA,IAC5C,WAAW,eAAe,eAAe;AACvC,OAAC,OAAO,QAAQ,IAAI,KAAK,YAAY,QAAQ,EAAE,QAAQ,CAAC;AAAA,IAC1D,WAAW,eAAe,cAAc;AACtC,cAAQ,KAAK,gBAAgB,QAAQ,KAAK,OAAO,EAAE,SAAS,WAAW,CAAC;AACxE,iBAAW,MAAM,SAAS;AAAA,IAC5B,OAAO;AACL,gBAAU;AAAA,IACZ;AACA,WAAO,EAAE,OAAO,UAAU,QAAQ;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,kBAAkB,CAAC,MAAM,YAAY,MAAM,CAAC,MAAM;AAChD,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,CAAC,MAAM,GAAG,YAAY,IAAI;AAChC,UAAM,EAAE,UAAU,OAAO,MAAM,SAAS,IAAI;AAC5C,UAAM,UACJ,QAAQ,YACR,KAAK,MAAM,aAAa,gBACxB,KAAK,UAAU,KAAK;AACtB,QAAI;AACJ,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK,qBAAqB;AACxB,iBAAS,KAAK;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA;AAAA,MACF;AAAA,MACA,KAAK,aAAa;AAChB,iBAAS,KAAK,qBAAqB,MAAM,YAAY;AAAA,UACnD;AAAA,UACA;AAAA,QACF,CAAC;AACD;AAAA,MACF;AAAA,MACA,KAAK,gBAAgB;AACnB,iBAAS,KAAK,wBAAwB,QAAQ,YAAY;AAAA,UACxD;AAAA,UACA;AAAA,QACF,CAAC;AACD;AAAA,MACF;AAAA,MACA,KAAK,eAAe;AAClB,iBAAS,KAAK,uBAAuB,QAAQ,YAAY;AAAA,UACvD;AAAA,UACA;AAAA,QACF,CAAC;AACD;AAAA,MACF;AAAA,MACA,SAAS;AACP,iBAAS,KAAK,wBAAwB,MAAM,YAAY;AAAA,UACtD;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,MACL,UAAU,aAAa,SAAS;AAAA,MAChC,UAAU,OAAO;AAAA,MACjB,OAAO,OAAO;AAAA,MACd,SAAS,OAAO;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,8BAA8B,CAAC,QAAQ,eAAe;AACpD,UAAM,YAAY,OAAO;AACzB,UAAM,YAAY,OAAO,CAAC;AAC1B,UAAM,WAAW,OAAO,YAAY,CAAC;AACrC,QAAI,cAAc,GAAG;AACnB,aAAO,EAAE,KAAK,UAAU,MAAM,UAAU;AAAA,IAC1C;AAEA,UAAM;AAAA,MACJ,QAAQ,CAAC,EAAE,MAAM,WAAW,MAAM,UAAU,CAAC;AAAA,IAC/C,IAAI;AACJ,UAAM;AAAA,MACJ,QAAQ,CAAC,EAAE,MAAM,UAAU,MAAM,SAAS,CAAC;AAAA,IAC7C,IAAI;AACJ,UAAM,EAAE,OAAO,WAAW,IAAI;AAC9B,QACE,KAAK,UAAU,SAAS,QAAQ,KAChC,aAAa,uBACb,aAAa,aACb;AACA,aAAO,EAAE,KAAK,UAAU,MAAM,SAAS;AAAA,IACzC;AACA,QAAI,cAAc,aAAa;AAC7B,aAAO,EAAE,KAAK,UAAU,MAAM,UAAU;AAAA,IAC1C;AACA,QAAI,cAAc,OAAO,cAAc,eAAe;AACpD,aAAO,EAAE,KAAK,UAAU,MAAM,SAAS;AAAA,IACzC;AACA,QAAI,aAAa,OAAO,aAAa,eAAe;AAClD,aAAO,EAAE,KAAK,UAAU,MAAM,UAAU;AAAA,IAC1C;AACA,QAAI,cAAc,GAAG;AACnB,UAAI,eAAe,cAAc;AAC/B,eAAO,EAAE,KAAK,UAAU,MAAM,SAAS;AAAA,MACzC;AACA,YAAM,EAAE,MAAM,UAAU,IAAI;AAC5B,UAAI,cAAc,OAAO,cAAc,KAAK;AAC1C,eAAO,EAAE,KAAK,UAAU,MAAM,SAAS;AAAA,MACzC;AAAA,IACF;AAEA,WAAO,EAAE,KAAK,UAAU,MAAM,UAAU;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,uBAAuB,kBAAgB;AACrC,QAAI,CAAC,aAAa,MAAM;AACtB;AAAA,IACF;AACA,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,cAAc,KAAK,kBAAkB,KAAK,KAAK;AAAA,IACtD;AACA,UAAM,kBACJ,KAAK,UAAU,KAAK,SAAS,KAAK,MAAM,aAAa;AACvD,UAAM,SAAS,KAAK;AACpB,QAAI,OAAO,KAAK;AAChB,QAAI,iBAAiB;AACnB,aAAO,KAAK;AAAA,IACd;AACA,QAAI,WAAW,aAAa,MAAM,MAAM;AACxC,WAAO,UAAU;AACf,YAAM,gBACJ,KAAK,MAAM,aAAa,gBACxB,aAAa,KAAK,SAClB,KAAK,MAAM,SAAS,QAAQ;AAC9B,UAAI,eAAe;AACjB,mBAAW,eAAe,cAAc;AACtC,gBAAM,EAAE,OAAO,IAAI,YAAY,IAAI,MAAM;AACzC,cAAI,KAAK,aAAa,QAAQ,UAAU,EAAE,MAAM,KAAK,MAAM,CAAC,GAAG;AAC7D,kBAAM,QAAQ,YAAY,IAAI,OAAO;AACrC,iBAAK,KAAK,KAAK,EAAE,WAAW;AAC5B,iBAAK,KAAK,KAAK,EAAE,OAAO;AACxB,iBAAK,OAAO,KAAK,EAAE,KAAK,QAAQ;AAAA,UAClC;AAAA,QACF;AAAA,MACF,WAAW,iBAAiB;AAC1B;AAAA,MACF;AACA,iBAAW,OAAO,SAAS;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,gBAAc;AAC5B,UAAM,MAAM,KAAK,KAAK,OAAO;AAC7B,QAAI,eAAe,cAAc,eAAe,cAAc;AAC5D,YAAM,eAAe,oBAAI,IAAI;AAC7B,UAAI,IAAI;AACR,iBAAW,EAAE,OAAO,KAAK,KAAK;AAC5B,cAAM,UAAU,OAAO,SAAS;AAChC,cAAM,EAAE,KAAK,KAAK,IAAI,KAAK;AAAA,UACzB;AAAA,UACA;AAAA,QACF;AACA,cAAM,EAAE,UAAU,UAAU,OAAO,QAAQ,IAAI,KAAK;AAAA,UAClD;AAAA,UACA;AAAA,UACA,EAAE,SAAS,IAAI;AAAA,QACjB;AACA,YAAI,MAAM,QAAQ;AAChB,eAAK,KAAK,CAAC,EAAE,OAAO;AACpB,eAAK,OAAO,CAAC,IAAI;AAAA,QACnB,WAAW,SAAS;AAClB,uBAAa;AAAA,YACX,oBAAI,IAAI;AAAA,cACN,CAAC,SAAS,CAAC;AAAA,cACX,CAAC,QAAQ,IAAI;AAAA,YACf,CAAC;AAAA,UACH;AAAA,QACF;AACA,aAAK,KAAK,CAAC,EAAE,MAAM;AACnB,aAAK,KAAK,CAAC,EAAE,WAAW,YAAY,CAAC;AACrC;AAAA,MACF;AACA,WAAK,qBAAqB,YAAY;AAAA,IACxC,OAAO;AACL,UAAI,IAAI;AACR,iBAAW,EAAE,OAAO,KAAK,KAAK;AAC5B,cAAM,OAAO,OAAO,OAAO,SAAS,CAAC;AACrC,cAAM,UAAU,OAAO,SAAS;AAChC,cAAM,MAAM;AACZ,cAAM,EAAE,UAAU,UAAU,MAAM,IAAI,KAAK;AAAA,UACzC;AAAA,UACA;AAAA,UACA,EAAE,SAAS,IAAI;AAAA,QACjB;AACA,YAAI,MAAM,QAAQ;AAChB,eAAK,KAAK,CAAC,EAAE,OAAO;AACpB,eAAK,OAAO,CAAC,IAAI;AAAA,QACnB;AACA,aAAK,KAAK,CAAC,EAAE,MAAM;AACnB,aAAK,KAAK,CAAC,EAAE,WAAW,YAAY,CAAC;AACrC;AAAA,MACF;AAAA,IACF;AACA,WAAO,CAAC,KAAK,MAAM,KAAK,MAAM;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,oBAAoB,CAAC,MAAM,OAAO,QAAQ;AACxC,UAAM,MAAM,CAAC;AACb,UAAM,UAAU;AAAA,MACd;AAAA,MACA,MAAM,KAAK;AAAA,IACb;AACA,eAAW,QAAQ,OAAO;AACxB,YAAM,UAAU,KAAK,iBAAiB,MAAM,MAAM,OAAO;AACzD,UAAI,QAAQ,MAAM;AAChB,YAAI,KAAK,GAAG,OAAO;AAAA,MACrB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,iBAAiB,CAAC,QAAQ,OAAO,QAAQ;AACvC,UAAM,EAAE,OAAO,MAAM,IAAI;AACzB,UAAM,EAAE,OAAO,WAAW,OAAO,IAAI,OAAO,KAAK;AACjD,UAAM,OAAO;AAAA,MACX;AAAA,MACA;AAAA,IACF;AACA,UAAM,YAAY,IAAI,IAAI,KAAK,kBAAkB,MAAM,OAAO,QAAQ,CAAC;AACvE,QAAI,UAAU,MAAM;AAClB,UAAI,UAAU,OAAO,SAAS,GAAG;AAC/B,cAAM,CAAC,QAAQ,IAAI,UAAU,SAAS;AACtC,eAAO;AAAA,MACT;AACA,aAAO,KAAK,eAAe,QAAQ,WAAW;AAAA,QAC5C,OAAO;AAAA,QACP,OAAO,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,iBAAiB,CAAC,QAAQ,MAAM,QAAQ;AACtC,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,OAAO,OAAO,KAAK;AACzB,UAAM,QAAQ,oBAAI,IAAI,CAAC,IAAI,CAAC;AAC5B,UAAM,YAAY,IAAI,IAAI,KAAK,kBAAkB,MAAM,OAAO,QAAQ,CAAC;AACvE,QAAI,UAAU,MAAM;AAClB,UAAI,UAAU,GAAG;AACf,eAAO;AAAA,MACT;AACA,UAAI;AACJ,iBAAW,YAAY,WAAW;AAChC,kBAAU,KAAK,eAAe,QAAQ,UAAU;AAAA,UAC9C,OAAO,QAAQ;AAAA,QACjB,CAAC;AACD,YAAI,SAAS;AACX;AAAA,QACF;AAAA,MACF;AACA,UAAI,SAAS;AACX,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,2BAA2B,CAAC,QAAQ,YAAY,QAAQ;AACtD,UAAM,eAAe,oBAAI,IAAI;AAC7B,UAAM,YAAY,OAAO;AACzB,UAAM,YAAY,YAAY;AAE9B,QAAI,QAAQ,UAAU;AACpB,YAAM,EAAE,OAAO,WAAW,IAAI,OAAO,CAAC;AACtC,iBAAW,QAAQ,YAAY;AAC7B,YAAI,QAAQ;AACZ,YAAI,YAAY,oBAAI,IAAI,CAAC,IAAI,CAAC;AAC9B,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,gBAAM,EAAE,OAAO,WAAW,OAAO,IAAI,OAAO,CAAC;AAC7C,gBAAM,OAAO,EAAE,OAAO,OAAO;AAC7B,gBAAM,WAAW,KAAK,kBAAkB,MAAM,WAAW,GAAG;AAC5D,cAAI,SAAS,QAAQ;AACnB,gBAAI,MAAM,WAAW;AACnB,yBAAW,YAAY,UAAU;AAC/B,6BAAa,IAAI,QAAQ;AAAA,cAC3B;AAAA,YACF;AACA,oBAAQ;AACR,wBAAY,IAAI,IAAI,QAAQ;AAAA,UAC9B,OAAO;AAEL,sBAAU,MAAM;AAChB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IAEF,OAAO;AACL,iBAAW,QAAQ,YAAY;AAC7B,YAAI,YAAY,oBAAI,IAAI,CAAC,IAAI,CAAC;AAC9B,iBAAS,IAAI,YAAY,GAAG,KAAK,GAAG,KAAK;AACvC,gBAAM,OAAO,OAAO,CAAC;AACrB,gBAAM,WAAW,KAAK,kBAAkB,MAAM,WAAW,GAAG;AAC5D,cAAI,SAAS,QAAQ;AAEnB,gBAAI,MAAM,GAAG;AACX,2BAAa,IAAI,IAAI;AAAA,YACvB;AACA,wBAAY,IAAI,IAAI,QAAQ;AAAA,UAC9B,OAAO;AAEL,sBAAU,MAAM;AAChB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gCAAgC,cAAY;AAC1C,QAAI,cAAc;AAClB,QAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,YAAM,IAAI,SAAS;AACnB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAM,OAAO,SAAS,CAAC;AACvB,YAAI,KAAK,MAAM,SAAS,IAAI,GAAG;AAC7B,wBAAc;AACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,6BAA6B,CAAC,QAAQ,YAAY,KAAK,eAAe;AACpE,UAAM,YAAY,OAAO;AACzB,UAAM,YAAY,YAAY;AAE9B,QAAI,QAAQ,UAAU;AACpB,YAAM,EAAE,OAAO,WAAW,IAAI,OAAO,CAAC;AACtC,iBAAW,QAAQ,YAAY;AAC7B,cAAM,cAAc,KAAK,eAAe,QAAQ,oBAAI,IAAI,CAAC,IAAI,CAAC,GAAG;AAAA,UAC/D,OAAO;AAAA,UACP,OAAO;AAAA,QACT,CAAC;AACD,YAAI,aAAa;AACf,cAAI,KAAK,MAAM,aAAa,cAAc;AACxC,gBACE,gBAAgB,KAAK,SACrB,KAAK,MAAM,SAAS,WAAW,GAC/B;AACA,qBAAO;AAAA,YACT;AAAA,UACF,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,YAAM,EAAE,QAAQ,YAAY,IAAI,OAAO,CAAC;AACxC,YAAM,CAAC,SAAS,IAAI;AACpB,UAAI,KAAK,MAAM,SAAS,SAAS,GAAG;AAClC,YAAI,CAAC,OAAO,IAAI,KAAK,gBAAgB,aAAa,WAAW;AAAA,UAC3D;AAAA,QACF,CAAC;AACD,eAAO,SAAS;AACd,gBAAM,cAAc,KAAK,eAAe,QAAQ,oBAAI,IAAI,CAAC,OAAO,CAAC,GAAG;AAAA,YAClE,OAAO;AAAA,YACP,OAAO;AAAA,UACT,CAAC;AACD,cAAI,aAAa;AACf,gBAAI,KAAK,MAAM,aAAa,cAAc;AACxC,kBACE,gBAAgB,KAAK,SACrB,KAAK,MAAM,SAAS,WAAW,GAC/B;AACA,uBAAO;AAAA,cACT;AAAA,YACF,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF;AACA,WAAC,OAAO,IAAI,KAAK,gBAAgB,aAAa,SAAS;AAAA,YACrD;AAAA,YACA,OAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,cAAM,EAAE,OAAO,WAAW,IAAI,OAAO,CAAC;AACtC,YAAI,QAAQ;AACZ,YAAI,YAAY,oBAAI,IAAI,CAAC,SAAS,CAAC;AACnC,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,gBAAM,EAAE,OAAO,WAAW,OAAO,IAAI,OAAO,CAAC;AAC7C,gBAAM,OAAO,EAAE,OAAO,OAAO;AAC7B,gBAAM,WAAW,KAAK,kBAAkB,MAAM,WAAW,GAAG;AAC5D,cAAI,SAAS,QAAQ;AACnB,gBAAI,MAAM,WAAW;AACnB,qBAAO,KAAK,8BAA8B,QAAQ;AAAA,YACpD;AACA,oBAAQ;AACR,wBAAY,IAAI,IAAI,QAAQ;AAAA,UAC9B,OAAO;AACL;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IAEF,OAAO;AACL,iBAAW,QAAQ,YAAY;AAC7B,cAAM,cAAc,KAAK,eAAe,QAAQ,MAAM;AAAA,UACpD,OAAO,YAAY;AAAA,QACrB,CAAC;AACD,YAAI,aAAa;AACf,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,UAAI,eAAe,cAAc;AAC/B,cAAM,EAAE,QAAQ,YAAY,IAAI,OAAO,SAAS;AAChD,cAAM,CAAC,SAAS,IAAI;AACpB,YAAI,CAAC,OAAO,IAAI,KAAK,gBAAgB,aAAa,WAAW;AAAA,UAC3D;AAAA,QACF,CAAC;AACD,eAAO,SAAS;AACd,gBAAM,cAAc,KAAK,eAAe,QAAQ,SAAS;AAAA,YACvD,OAAO,YAAY;AAAA,UACrB,CAAC;AACD,cAAI,aAAa;AACf,mBAAO;AAAA,UACT;AACA,WAAC,OAAO,IAAI,KAAK,gBAAgB,aAAa,SAAS;AAAA,YACrD;AAAA,YACA,OAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,gBAAc;AACnB,UAAM,CAAC,CAAC,GAAG,QAAQ,GAAG,cAAc,IAAI,KAAK,cAAc,UAAU;AACrE,UAAM,IAAI,SAAS;AACnB,QAAI,OAAO;AACX,QAAI,QAAQ,oBAAI,IAAI;AACpB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,EAAE,QAAQ,KAAK,MAAAC,MAAK,IAAI,SAAS,CAAC;AACxC,UAAI,CAAC,OAAO,UAAU,CAACA,OAAM;AAC3B;AAAA,MACF;AACA,YAAM,aAAa,eAAe,CAAC;AACnC,YAAM,YAAY,OAAO,SAAS;AAElC,UAAI,cAAc,GAAG;AACnB,aACG,eAAe,cAAc,eAAe,iBAC7C,KAAK,MAAM,aAAa,cACxB;AACA,qBAAW,QAAQ,YAAY;AAC7B,gBAAI,SAAS,KAAK,SAAS,KAAK,MAAM,SAAS,IAAI,GAAG;AACpD,oBAAM,IAAI,IAAI;AACd,kBAAI,eAAe,cAAc;AAC/B;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF,WAAW,eAAe,YAAY;AACpC,cAAI,MAAM,MAAM;AACd,uBAAW,QAAQ,YAAY;AAC7B,oBAAM,IAAI,IAAI;AAAA,YAChB;AACA,mBAAO;AAAA,UACT,OAAO;AACL,oBAAQ,IAAI,IAAI,UAAU;AAAA,UAC5B;AAAA,QACF,OAAO;AACL,cAAI,WAAW,QAAQ;AACrB,kBAAM,IAAI,WAAW,CAAC,CAAC;AAAA,UACzB;AAAA,QACF;AAAA,MAEF,OAAO;AACL,YAAI,eAAe,YAAY;AAC7B,gBAAM,WAAW,KAAK;AAAA,YACpB;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,cAAI,MAAM,MAAM;AACd,uBAAW,WAAW,UAAU;AAC9B,oBAAM,IAAI,OAAO;AAAA,YACnB;AACA,mBAAO;AAAA,UACT,OAAO;AACL,oBAAQ;AAAA,UACV;AAAA,QACF,OAAO;AACL,gBAAM,cAAc,KAAK;AAAA,YACvB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,cAAI,aAAa;AACf,kBAAM,IAAI,WAAW;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,QAAQ;AACf,YAAM,QAAQ,CAAC,CAAC,MAAM;AACtB,UAAI;AACJ,UAAI,KAAK,eAAe,QAAQ;AAC9B,wBAAgB,KAAK,eAAe,KAAK,EAAE;AAAA,MAC7C,OAAO;AACL,wBAAgB;AAAA,MAClB;AACA,aAAO,EAAE,OAAO,cAAc;AAAA,IAChC;AACA,QAAI,eAAe,gBAAgB,eAAe,YAAY;AAC5D,YAAM,OAAO,KAAK,KAAK;AAAA,IACzB;AACA,SAAK,QAAQ,eAAe,iBAAiB,MAAM,OAAO,GAAG;AAC3D,aAAO,IAAI,IAAI,UAAU,KAAK,CAAC;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AACF;;;ALvhGA,IAAM,YAAY;AAGX,IAAM,cAAN,MAAkB;AAAA;AAAA,EAEvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,QAAQ,UAAU,MAAM,CAAC,GAAG;AACtC,UAAM,EAAE,SAAS,IAAI;AACrB,SAAK,UAAU;AACf,SAAK,YAAY,YAAY,OAAO;AACpC,SAAK,UAAU,IAAI,OAAO,MAAM;AAChC,SAAK,YAAY;AACjB,SAAK,UAAU,WAAW,QAAQ,QAAQ;AAC1C,SAAK,SAAS,IAAI,0BAAS;AAAA,MACzB,KAAK;AAAA,IACP,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,UAAU,MAAM,MAAM,CAAC,GAAG;AAC9B,QAAI,CAAC,MAAM,UAAU;AACnB,YAAM,IAAI,IAAI,KAAK,QAAQ,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AACvE,aAAO,KAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IACpC,WAAW,KAAK,aAAa,cAAc;AACzC,YAAM,IAAI,IAAI,KAAK,QAAQ,UAAU,mBAAmB,KAAK,QAAQ,EAAE;AACvE,aAAO,KAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IACpC;AACA,QAAI,KAAK,WAAW;AAClB,aAAO,KAAK,UAAU,eAAe,IAAI;AAAA,IAC3C;AACA,UAAM,WAAW,KAAK;AACtB,QACE,aAAa,KAAK,aAClB,SAAS,gBAAgB,eACzB,SAAS,mBACT,KAAK,YACL;AACA,YAAM,WAAW,SAAS,QAAQ;AAClC,UAAI,gBAAgB;AACpB,UAAI,KAAK,OAAO,IAAI,QAAQ,GAAG;AAC7B,wBAAgB,KAAK,OAAO,IAAI,QAAQ;AAAA,MAC1C,OAAO;AACL,wBAAgB,eAAe,UAAU,WAAW;AACpD,aAAK,OAAO,IAAI,UAAU,aAAa;AAAA,MACzC;AACA,UAAI,eAAe;AACjB,YAAI;AACF,gBAAM,QAAQ,KAAK,QAAQ,MAAM,UAAU,IAAI;AAC/C,iBAAO;AAAA,YACL;AAAA,YACA,eAAe;AAAA,UACjB;AAAA,QACF,SAAS,GAAG;AAAA,QAEZ;AAAA,MACF;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AACF,UAAI,QAAQ;AACZ,UAAI,UAAU;AACd,UAAI,OAAO;AACX,WAAK,QAAQ,MAAM,UAAU,MAAM,GAAG;AACtC,YAAM,KAAK,QAAQ,KAAK,WAAW;AAAA,IACrC,SAAS,GAAG;AACV,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,UAAU,MAAM,MAAM,CAAC,GAAG;AAChC,QAAI,CAAC,MAAM,UAAU;AACnB,YAAM,IAAI,IAAI,KAAK,QAAQ,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AACvE,aAAO,KAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IACpC,WAAW,KAAK,aAAa,cAAc;AACzC,YAAM,IAAI,IAAI,KAAK,QAAQ,UAAU,mBAAmB,KAAK,QAAQ,EAAE;AACvE,aAAO,KAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IACpC;AACA,QAAI,KAAK,WAAW;AAClB,aAAO,KAAK,UAAU,eAAe,IAAI;AAAA,IAC3C;AACA,UAAM,WAAW,KAAK;AACtB,QACE,aAAa,KAAK,aAClB,SAAS,gBAAgB,eACzB,SAAS,mBACT,KAAK,YACL;AACA,YAAM,WAAW,WAAW,QAAQ;AACpC,UAAI,gBAAgB;AACpB,UAAI,KAAK,OAAO,IAAI,QAAQ,GAAG;AAC7B,wBAAgB,KAAK,OAAO,IAAI,QAAQ;AAAA,MAC1C,OAAO;AACL,wBAAgB,eAAe,UAAU,WAAW;AACpD,aAAK,OAAO,IAAI,UAAU,aAAa;AAAA,MACzC;AACA,UAAI,eAAe;AACjB,YAAI;AACF,gBAAMC,OAAM,KAAK,QAAQ,MAAM,UAAU,IAAI;AAC7C,iBAAOA;AAAA,QACT,SAAS,GAAG;AAAA,QAEZ;AAAA,MACF;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AACF,WAAK,QAAQ,MAAM,UAAU,MAAM,GAAG;AACtC,YAAM,QAAQ,KAAK,QAAQ,KAAK,WAAW;AAC3C,YAAM,MAAM;AAAA,IACd,SAAS,GAAG;AACV,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B;AACA,WAAO,CAAC,CAAC;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,UAAU,MAAM,MAAM,CAAC,GAAG;AAChC,QAAI,CAAC,MAAM,UAAU;AACnB,YAAM,IAAI,IAAI,KAAK,QAAQ,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AACvE,aAAO,KAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IACpC,WAAW,KAAK,aAAa,cAAc;AACzC,YAAM,IAAI,IAAI,KAAK,QAAQ,UAAU,mBAAmB,KAAK,QAAQ,EAAE;AACvE,aAAO,KAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IACpC;AACA,QAAI,KAAK,WAAW;AAClB,aAAO,KAAK,UAAU,eAAe,IAAI;AAAA,IAC3C;AACA,UAAM,WAAW,KAAK;AACtB,QACE,aAAa,KAAK,aAClB,SAAS,gBAAgB,eACzB,SAAS,mBACT,KAAK,YACL;AACA,YAAM,WAAW,WAAW,QAAQ;AACpC,UAAI,gBAAgB;AACpB,UAAI,KAAK,OAAO,IAAI,QAAQ,GAAG;AAC7B,wBAAgB,KAAK,OAAO,IAAI,QAAQ;AAAA,MAC1C,OAAO;AACL,wBAAgB,eAAe,UAAU,aAAa;AACtD,aAAK,OAAO,IAAI,UAAU,aAAa;AAAA,MACzC;AACA,UAAI,eAAe;AACjB,YAAI;AACF,gBAAMA,OAAM,KAAK,QAAQ,QAAQ,UAAU,IAAI;AAC/C,iBAAOA;AAAA,QACT,SAAS,GAAG;AAAA,QAEZ;AAAA,MACF;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AACF,WAAK,QAAQ,MAAM,UAAU,MAAM,GAAG;AACtC,YAAM,QAAQ,KAAK,QAAQ,KAAK,aAAa;AAC7C,UAAI,MAAM,MAAM;AACd,YAAI,UAAU;AACd,eAAO,SAAS;AACd,cAAI,MAAM,IAAI,OAAO,GAAG;AACtB,kBAAM;AACN;AAAA,UACF;AACA,oBAAU,QAAQ;AAAA,QACpB;AAAA,MACF;AAAA,IACF,SAAS,GAAG;AACV,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B;AACA,WAAO,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,UAAU,MAAM,MAAM,CAAC,GAAG;AACtC,QAAI,CAAC,MAAM,UAAU;AACnB,YAAM,IAAI,IAAI,KAAK,QAAQ,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AACvE,aAAO,KAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IACpC;AACA,QAAI,KAAK,WAAW;AAClB,aAAO,KAAK,UAAU,eAAe,IAAI;AAAA,IAC3C;AACA,QAAI;AACJ,QAAI;AACF,WAAK,QAAQ,MAAM,UAAU,MAAM,GAAG;AACtC,YAAM,QAAQ,KAAK,QAAQ,KAAK,YAAY;AAC5C,UAAI,MAAM,MAAM;AACd,SAAC,GAAG,IAAI,CAAC,GAAG,KAAK;AAAA,MACnB;AAAA,IACF,SAAS,GAAG;AACV,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B;AACA,WAAO,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,iBAAiB,UAAU,MAAM,MAAM,CAAC,GAAG;AACzC,QAAI,CAAC,MAAM,UAAU;AACnB,YAAM,IAAI,IAAI,KAAK,QAAQ,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AACvE,aAAO,KAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IACpC;AACA,QAAI,KAAK,WAAW;AAClB,aAAO,KAAK,UAAU,eAAe,IAAI;AAAA,IAC3C;AACA,QAAI;AACJ,QAAI,KAAK,aAAa,eAAe;AACnC,iBAAW;AAAA,IACb,OAAO;AACL,iBAAW,KAAK;AAAA,IAClB;AACA,QACE,aAAa,KAAK,aAClB,SAAS,gBAAgB,eACzB,SAAS,iBACT;AACA,YAAM,WAAW,oBAAoB,QAAQ;AAC7C,UAAI,gBAAgB;AACpB,UAAI,KAAK,OAAO,IAAI,QAAQ,GAAG;AAC7B,wBAAgB,KAAK,OAAO,IAAI,QAAQ;AAAA,MAC1C,OAAO;AACL,wBAAgB,eAAe,UAAU,UAAU;AACnD,aAAK,OAAO,IAAI,UAAU,aAAa;AAAA,MACzC;AACA,UAAI,eAAe;AACjB,YAAI;AACF,gBAAMA,OAAM,KAAK,QAAQ,OAAO,UAAU,IAAI;AAC9C,iBAAOA;AAAA,QACT,SAAS,GAAG;AAAA,QAEZ;AAAA,MACF;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AACF,WAAK,QAAQ,MAAM,UAAU,MAAM,GAAG;AACtC,YAAM,QAAQ,KAAK,QAAQ,KAAK,UAAU;AAC1C,UAAI,MAAM,MAAM;AACd,cAAM,CAAC,GAAG,KAAK;AAAA,MACjB;AAAA,IACF,SAAS,GAAG;AACV,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B;AACA,WAAO,OAAO,CAAC;AAAA,EACjB;AACF;", "names": ["cssTree", "KEY_INPUT_EDIT", "isCustomElementName", "bidiFactory", "KEY_INPUT_EDIT", "nwsapi", "astPrefix", "selectorBranches", "isMatch", "find", "res"]}